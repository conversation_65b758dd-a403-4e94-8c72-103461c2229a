<template>
	<Navbar :type="1" :showSearch="false" placeholder="" leftText="评价"></Navbar>
	<view class="allPidding imgListBox">
		<view class="imgList">
			<view v-for="(item, index) in imgList" :key="index" @click="ckImg(index)">
				<image :src="item.url" mode="aspectFill" v-if="ckIndex != index"></image>
				<image :src="item.ckurl" mode="aspectFill" v-else></image>
				<text>{{ item.name }}</text>
			</view>
		</view>
	</view>
	<view class="allPidding textarea">
		<view class="textareaBorder">
			<textarea v-model="txt" maxlength="500"></textarea>
		</view>
	</view>
	<view class="allPidding" style="margin-top: 36rpx">
		<uv-upload :fileList="fileList1" accept="image" name="1" multiple="true" :maxCount="1" @afterRead="afterRead" @delete="deletePic"></uv-upload>
	</view>
	<view class="allPidding" style="margin-top: 42rpx">
		<uv-checkbox-group v-model="checkbox" shape="circle" @change="ckCheckbox">
			<uv-checkbox v-for="(item, index) in checkList" :key="index" :label="item.name" :name="item.name"></uv-checkbox>
		</uv-checkbox-group>
	</view>
	<view class="bottom">
		<view @click="up">
			<text>提交</text>
		</view>
	</view>
</template>

<script setup>
import Config from '@/common/config';
import { uptip } from '@/common/utils.js';
import { getToken } from '@/common/auth';
import { ref, getCurrentInstance } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { uploadFile } from '@/api/request';
import { createServiceReview } from '@/api/order';
const index = ref(null);
let id = ref(null);
let item_id = ref(null);
let ckIndex = ref(null);
let checkbox = ref([]);
let checkList = ref([{ name: '匿名评价' }]);
let fileList1 = ref([]);
let txt = ref('');
let imgList = [
	{ ckurl: '../../../static/icon/<EMAIL>', url: '../../../static/icon/<EMAIL>', name: '非常差' },
	{ ckurl: '../../../static/icon/<EMAIL>', url: '../../../static/icon/<EMAIL>', name: '较差' },
	{ ckurl: '../../../static/icon/<EMAIL>', url: '../../../static/icon/<EMAIL>', name: '一般' },
	{ ckurl: '../../../static/icon/<EMAIL>', url: '../../../static/icon/<EMAIL>', name: '推荐' },
	{ ckurl: '../../../static/icon/<EMAIL>', url: '../../../static/icon/<EMAIL>', name: '超赞' }
];
function ckImg(index) {
	if (index == ckIndex.value) {
		ckIndex.value = null;
	} else {
		ckIndex.value = index;
	}
	console.log(ckIndex.value);
}
function ckCheckbox(e) {
	checkbox.value = e;
	console.log(checkbox.value, e);
}
onLoad((e) => {
	id.value = e.id;
	item_id.value = e.item_id;
	index.value = e.index;
});
function deletePic(event) {
	let arr = fileList1.value;
	arr.splice(event.index, 1);
	fileList1.value = arr;
}
// 新增图片
async function afterRead(event) {
	// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式

	let lists = [].concat(event.file);
	let fileListLen = fileList1.value.length;
	lists.map((item) => {
		fileList1.value.push({
			...item,
			status: 'uploading',
			message: '上传中'
		});
	});
	for (let i = 0; i < lists.length; i++) {
		const result = await uploadFile(lists[i].url);
		console.log(result, 9999999);
		let item = fileList1.value[fileListLen];
		fileList1.value.splice(
			fileListLen,
			1,
			Object.assign(item, {
				status: 'success',
				message: '',
				url: Config.fileUrl + result.data.url,
				copyUrl: result.data.url
			})
		);
		fileListLen++;
	}
}
const instance = getCurrentInstance().proxy;
const eventChannel = instance.getOpenerEventChannel();
function up() {
	if (ckIndex.value == null) return uptip('请对服务进行评分');
	if (txt.value == '' || txt.value == null) return uptip('请对服务进行评价');
	let obj = {
		service_item_id: Number(id.value),
		editor: 'orderComment',
		// content: 'service review content',
		rating: (ckIndex.value + 1) * 20,
		is_default_positive: false,
		is_anonymous: checkbox.value.length == 0 ? false : true,
		parent: 0
	};
	let con = {
		txt: txt.value
	};
	if (fileList1.value.length != 0) {
		con.url = fileList1.value[0].copyUrl;
	}
	obj.content = JSON.stringify(con);
	console.log(obj);
	createServiceReview(id.value, obj).then((res) => {
		if (res.code == 20000) {
			uptip('评论已成功提交', 'success');
			eventChannel.emit('orderListComment', {
				index: index.value,
				has_review: true
			});
			eventChannel.emit('detailsComment', {
				index: index.value,
				has_review: true
			});
			uni.navigateBack();
		}
	});
}
</script>

<style lang="scss" scoped>
.allPidding {
	padding-left: 24rpx;
	padding-right: 24rpx;
}
.radio {
	display: flex;
	align-items: center;
}
.imgListBox {
	margin-top: 56rpx;
	display: flex;
	justify-content: center;
}
.imgList {
	width: 606rpx;
	display: flex;
	justify-content: space-between;
	view {
		display: flex;
		flex-direction: column;
		align-items: center;
		image {
			width: 80rpx;
			height: 80rpx;
			margin-bottom: 4rpx;
		}
		text {
			font-size: 24rpx;
			color: #3d3d3d;
		}
	}
}
.textarea {
	margin-top: 56rpx;
	.textareaBorder {
		padding: 32rpx;
		background-color: #f7f8fa;
		border-radius: 24rpx;
		font-size: 28rpx;
		color: #1f1f1f;
	}
	textarea {
		width: 100%;
		height: 126rpx;
	}
}
.bottom {
	position: fixed;
	bottom: 42rpx;
	width: 100%;
	display: flex;
	justify-content: center;
	view {
		width: 702rpx;
		height: 96rpx;
		background: #fe58b7;
		border-radius: 48rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 36rpx;
		color: #ffffff;
	}
}
</style>
