<template>
	<view class="rating-container">
		<view class="empty-stars">
			<image src="../../static/search/star.png"class="star-icon" />
		</view>
		<view class="filled-stars" :style="{ width: filledWidth }">
			<image src="../../static/search/star1.png" class="star-icon" />
		</view>
	</view>
</template>

<script setup>
	import {
		computed
	} from 'vue';

	const props = defineProps({
		score: {
			type: Number,
			default: 0,
			validator: value => value >= 0 && value <= 5
		},
	});

	// 计算实际显示的实星宽度
	const filledStars = computed(() => {
		// 确保分数在0-5范围内
		const score = Math.max(0, Math.min(5, props.score));
		return score
	});

	// 计算填充层的宽度百分比
	const filledWidth = computed(() => {
		return `${filledStars.value * 20}%`;
	});
</script>

<style lang="scss">
	.rating-container {
		position: relative;
		width: 160rpx;
	}

	.empty-stars {
		height: 32rpx;
		line-height: 32rpx;
	}

	.filled-stars {
		position: absolute;
		top: 0;
		left: 0;
		z-index: 1;
		overflow: hidden;
		height: 32rpx;
		line-height: 32rpx;
		width: 0;
	}

	.star-icon {
		width: 160rpx;
		height: 32rpx;
		vertical-align: top;
	}
</style>