<template>
	<view class="app-container">
		<image src="../static/login/bg.png" class="bg-img" />
		<uni-nav-bar :fixed="true" @clickLeft="back" left-icon="left" backgroundColor="rgba(0,0,0,0)" :border="false"
			:statusBar="true" />
		<image v-if="sysInfo.screenHeight >= 812" src="../static/login/logo.png" class="logo" />
		<image v-else-if="!isOtherPhone" src="../static/login/logo.png" class="logo" />
		<template v-if="!isNewUser">
			<view v-if="!isOtherPhone" class="login-btn-group">
				<button v-if="!agreement" class="btn-submit btn-fen" @tap="checkAgreement">手机号快捷登录</button>
				<button v-else class="btn-submit btn-fen" open-type="getPhoneNumber"
					@getphonenumber="getPhoneNumber">手机号快捷登录</button>
				<button class="btn-submit btn-online" @tap="handleOtherLogin(true)">其他手机号登录</button>
			</view>
			<view v-else class="account__body">
				<view class="account__body__cell">
					<text class="account__body__cell-title">手机号登录</text>
					<view class="account-input">
						<image src="../static/login/tel.png" class="icon-phone" />
						<view class="flex-1">
							<view class="label-wrap">
								<text class="label-text">手机号</text>
							</view>
							<uv-input v-model="account" type="number" placeholder="请输入你的手机号" :clearable="false"
								border="bottom" customStyle="padding:14rpx 0;" focus maxlength="11" fontSize="32rpx"
								color="#666" />
						</view>
					</view>
					<view v-if="!isPwd" class="account-input" style="margin-top: 36rpx">
						<image src="../static/login/code.png" class="icon-phone" />
						<view class="flex-1">
							<view class="label-wrap">
								<text class="label-text">验证码</text>
							</view>
							<uv-input v-model="code" type="number" placeholder="请输入验证码" :clearable="false"
								border="bottom" customStyle="padding:14rpx 0;" maxlength="6" fontSize="32rpx"
								color="#666">
								<template v-slot:suffix>
									<uv-code ref="uCode" @change="codeChange" seconds="60" changeText="X 秒后重新发送" />
									<uv-button @click="getCode" :text="tips" :hairline="false" />
								</template>
							</uv-input>
						</view>
					</view>
					<view v-if="isPwd" class="account-input" style="margin-top: 36rpx">
						<image src="../static/login/code.png" class="icon-phone" />
						<view class="flex-1">
							<view class="label-wrap">
								<text class="label-text">密码登录</text>
							</view>
							<uv-input v-model="password" type="password" placeholder="请输入密码" :clearable="false"
								border="bottom" customStyle="padding:14rpx 0;" fontSize="32rpx" color="#666" />
						</view>
					</view>
					<view style="display: flex; justify-content: space-between; padding: 10rpx 0">
						<view />
						<text style="color: #666; font-size: 28rpx" @tap="changeLoginType">{{ isPwd ? '短信登录' : '密码登录'
						}}</text>
					</view>
					<button class="btn-submit btn-fen" @tap="handleLogin">登录</button>
					<!-- <button class="btn-submit btn-black" open-type="getUserInfo" @getuserinfo="miniWechatLogin">微信登录</button> -->
					<button v-if="!isApp()" class="btn-submit btn-black" @tap="handleOtherLogin(false)">返回</button>
				</view>
			</view>

			<view class="agreement-footer">
				<view class="agreement" @tap="agreementChange">
					<view class="agreement-wrap">
						<image class="agreement-icon" :src="agreement ? checkOn : checkOff" />
						<!-- :src="`${agreement} ? '../static/login/check-on.png' : '../static/login/check-off.png'`" /> -->
						<view class="agreement-text">
							<text>我已阅读并同意</text>
							<text class="link" @tap.stop="toAgreement('1')">《用户协议》</text>
							<text class="link" @tap.stop="toAgreement('2')">《隐私政策》</text>
							<text class="link" @tap.stop="toAgreement('3')">《儿童/青少年个人信息保护规则》</text>
						</view>
					</view>
				</view>
				<uv-safe-bottom />
			</view>
		</template>

		<invite-code v-if="isNewUser" @back="back"></invite-code>
	</view>

	<uv-popup ref="popup" mode="bottom" safeAreaInsetBottom bgColor="none">
		<view class="popup-wrapper">
			<image class="close-icon" src="/static/icon/dimension/close.png" @tap.stop="closeAgreementPopup"></image>
			<view class="header">请阅读并同意以下条款</view>
			<view class="agreement">
				<text class="link" @tap.stop="toAgreement('1')">《用户协议》、</text>
				<text class="link" @tap.stop="toAgreement('2')">《隐私政策》、</text>
				<text class="link" @tap.stop="toAgreement('3')">《儿童/青少年个人信息保护规则》</text>
			</view>
			<view class="w-full mx-40">
				<button v-if="isOtherPhone" class="btn-submit btn-fen" @tap.stop="agreeLogin">同意并继续</button>
				<button v-else class="btn-submit btn-fen" open-type="getPhoneNumber"
					@getphonenumber="getPhoneNumber">同意并继续</button>
			</view>
		</view>
	</uv-popup>
</template>
<script setup>
import {
	ref,
	onBeforeUnmount,
	computed
} from 'vue';
import {
	useStore
} from 'vuex';
import {
	onLoad,
	onBackPress
} from '@dcloudio/uni-app';
import sha256 from 'js-sha256';
import checkOn from '../static/login/check-on.png';
import checkOff from '../static/login/check-off.png';

import {
	loginPassword,
	loginCode,
	loginPhone,
	wxLogin,
	login,
} from '@/api/user';
import {
	changeParam,
	navigateToWithCorrectStack
} from '@/common/utils';
import * as auth from '@/common/auth';
import InviteCode from './invite-code.vue';
import { isApp } from '../../../common/utils';

const store = useStore();
const inviteCode = computed(() => store.state.common.inviteCode);
const sysInfo = computed(() => store.state.common.sysInfo);
const account = ref('');
const password = ref('');
const code = ref('');
const agreement = ref(false);
const backUrl = ref('');
const emitName = ref('');
const loading = ref(false);
const isNavigatingBack = ref(false); // 添加标志位
const tips = ref('');
const uCode = ref(null);
const isPwd = ref(false);
// 同意协议弹窗 ref
const popup = ref(false);
// 是否是新用户填写邀请码
const isNewUser = ref(false);
const isOtherPhone = ref(isApp())

onLoad((option) => {
	console.log('onLoad:', option);
	if (option.backUrl) {
		backUrl.value = decodeURIComponent(option.backUrl);
	}
	// else {
	// 	console.error('backUrl 必传，否则无法回到指定页面');
	// }
	if (option.emitName) {
		emitName.value = option.emitName;
	}
});

onBackPress((options) => {
	console.log('options.from:', options.from);
	if (isNavigatingBack.value) {
		return false; // 不阻止默认返回行为
	}
	isNavigatingBack.value = true; // 设置标志位
	back();
	return true; // 阻止默认返回行为
});

function back() {
	if (backUrl.value) {
		navigateToWithCorrectStack({
			targetRoute: backUrl.value,
			emitName: emitName.value
		});
	} else {
		uni.navigateBack();
	}
}

function agreementChange() {
	agreement.value = !agreement.value;
}

function agreeLogin() {
	agreement.value = true;
	handleLogin();
}

function closeAgreementPopup() {
	popup.value.close();
}

function handleLogin() {
	if (!agreement.value) {
		popup.value.open();
		return;
	}
	// 验证输入是否为手机号
	const reg = /^[1][3-9]\d{9}$/;
	if (!reg.test(account.value)) {
		// 如果不是手机号格式，清空输入
		account.value = '';
		code.value = ''
		uni.showToast({
			title: '手机号码格式错误',
			icon: 'none'
		});
		closeAgreementPopup();
		return;
	}
	if (!isPwd.value) {
		codeLogin();
		return;
	}
	if (!password.value) {
		uni.showToast({
			title: '请输入密码',
			icon: 'none'
		});
		return;
	}
	loading.value = true;
	loginPassword({
		phone: account.value,
		password: sha256(password.value)
	}).then((res) => {
		loading.value = false;
		if (res.code === 20000) {
			console.log('login token:', res.data.token);
			auth.setToken(res.data.token);
			store.commit('userInfo/SET_TOKEN', res.data.token); // 将token存储到状态管理中
			store.dispatch('userInfo/getUserInfo');
			closeAgreementPopup();
			uni.navigateBack({
				success() {
					uni.showToast({
						title: '登录成功',
						icon: 'none'
					});
				}
			});
			// }
		}
	}).catch((e) => {
		loading.value = false;
	});
}

function checkAgreement() {
	if (!agreement.value) {
		popup.value.open();
		return;
	}
}

function toAgreement(type) {
	let url = ''
	switch (type) {
		case '1':
			url = '/pages/packageA/agreement/userAgreement'
			break
		case '2':
			url = '/pages/packageA/agreement/privacyAgreement'
			break
		case '3':
			url = '/pages/packageA/agreement/agreement'
			break
	}
	url && uni.navigateTo({
		url
	})
}

function codeChange(text) {
	tips.value = text;
}

function getCode() {
	// 验证输入是否为手机号
	const reg = /^[1][3-9]\d{9}$/;
	if (!reg.test(account.value)) {
		// 如果不是手机号格式，清空输入
		account.value = '';
		uni.showToast({
			title: '手机号码格式错误',
			icon: 'none'
		});
		return;
	}
	uni.showLoading({
		title: '正在获取验证码'
	});
	loginCode({
		phone: account.value
	}).then((res) => {
		uni.hideLoading();
		if (res.code === 20000) {
			// 这里此提示会被this.start()方法中的提示覆盖
			uni.showToast({
				title: '验证码已发送',
				icon: 'none'
			});
			// 通知验证码组件内部开始倒计时
			uCode.value.start();
		}
	}).catch(e => {
		uni.hideLoading();
	})
}

function changeLoginType() {
	isPwd.value = !isPwd.value;
}

function codeLogin() {
	if (!code.value) {
		uni.showToast({
			title: '请输入验证码',
			icon: 'none'
		});
		return;
	}
	loading.value = true;
	const params = {
		phone: account.value,
		code: code.value
	}
	if (inviteCode.value) {
		params.invite_code = inviteCode.value
	}
	closeAgreementPopup();
	loginPhone(params).then((res) => {
		loading.value = false;
		if (res.code === 20000) {
			console.log('login token:', res.data.token);
			auth.setToken(res.data.token);
			store.commit('userInfo/SET_TOKEN', res.data.token); // 将token存储到状态管理中
			store.dispatch('userInfo/getUserInfo');
			if (!!res.data?.is_new_user && !inviteCode.value) {
				isNewUser.value = true
			} else {
				store.dispatch('common/setInviteCode', '');
				auth.updateInviteCode('');
				if (!!res.data?.is_new_user && params.invite_code) {
					uni.redirectTo({
						url: '/pages/packageA/login-pwd/invite-user-info?inviteCode=' + params.invite_code,
					})
				} else {
					uni.navigateBack({
						success() {
							uni.showToast({
								title: '登录成功',
								icon: 'none'
							});
						}
					});
				}
			}
		}
	}).catch((e) => {
		loading.value = false;
	});
}

function handleOtherLogin(status) {
	isOtherPhone.value = status
}

// 手机号快捷登录
function getPhoneNumber(e) {
	console.log('getPhoneNumber:', e)
	agreement.value = true;
	closeAgreementPopup();
	loading.value = true;
	const phone_code = e.detail.code
	if (!phone_code) {
		console.log('拒绝手机号授权')
		return
	}
	wx.login({
		provider: 'weixin',
		success: async (data) => {
			console.log('登录获取 code:', data)
			const params = {
				wx_code: data.code, // 小程序用户登录凭证code
				phone_code: phone_code, // 手机号快捷登录凭证 code
			}
			if (inviteCode.value) {
				params.invite_code = inviteCode.value
			}
			const res = await login(params)
			loading.value = false;
			if (res.code === 20000) {
				console.log('login token:', res.data.token);
				auth.setToken(res.data.token);
				store.commit('userInfo/SET_TOKEN', res.data.token); // 将token存储到状态管理中
				store.dispatch('userInfo/getUserInfo');
				if (!!res.data?.is_new_user && !inviteCode.value) {
					isNewUser.value = true
				} else {
					store.dispatch('common/setInviteCode', '');
					auth.updateInviteCode('');
					if (!!res.data?.is_new_user && params.invite_code) {
						uni.redirectTo({
							url: '/pages/packageA/login-pwd/invite-user-info?inviteCode=' + params.invite_code,
						})
					} else {
						uni.navigateBack({
							success() {
								uni.showToast({
									title: '登录成功',
									icon: 'none'
								});
							}
						});
					}
				}
			}
		},
	})
}

uni.$on('pwdLoginEvent', (data) => {
	console.log('pwdLoginEvent 接收到事件通知:', data);
});

// 在页面销毁前移除事件监听 
onBeforeUnmount(() => {
	uni.$off('pwdLoginEvent');
});
</script>

<style lang="scss" scoped>
:deep(.uv-input__content__prefix-icon) {
	margin-right: 0 !important;
}

:deep(.uv-button--info) {
	color: #85878d !important;
	border-width: 0 !important;
	font-size: 28rpx !important;
	height: 40rpx !important;
	padding: 0 !important;
}

.app-container {
	position: relative;
	height: 100vh;
}

.logo {
	width: 340rpx;
	height: 336rpx;
	margin: 20rpx auto 0;
	position: relative;
	z-index: 1;
}

.bg-img {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	width: 100vw;
	height: 100vh;
	z-index: 0;
	bottom: 0;
}

.login-btn-group {
	padding-left: 72rpx;
	padding-right: 72rpx;
	padding-top: 146rpx;
}

.account__body {
	background-color: #fff;
	border-radius: 32rpx;
	margin: 30rpx 32rpx 0;
	position: relative;
	z-index: 1;
}

.account__body__cell {
	display: flex;
	flex-direction: column;
	padding: 0 40rpx 56rpx;
}

.account__body__cell-title {
	font-weight: 700;
	font-size: 52rpx;
	color: #333333;
	text-align: center;
	padding-top: 40rpx;
	margin-bottom: 32rpx;
}

.account__body__cell-subtitle {
	font-size: 26rpx;
	color: #9d9ea6;
	margin-top: 26rpx;
}

.account-input {
	display: flex;
}

.label-wrap {
	display: flex;
	height: 52rpx;
	align-items: center;
}

.icon-phone {
	width: 52rpx;
	height: 52rpx;
	margin-right: 18rpx;
}

.agreement {
	color: #9d9ea6;
	align-items: center;
	margin-top: 30rpx;
	font-size: 28rpx;
	position: relative;
	padding: 0 34rpx;

	.link {
		color: #48daea;
	}

	.tip-icon {
		position: absolute;
		bottom: 56rpx;
		left: 26rpx;
		width: 290rpx;
		height: 61.5rpx;
		z-index: 1;
	}
}

.agreement-footer {
	position: absolute;
	bottom: 40rpx;
	left: 0;
	right: 0;
}

.agreement-wrap {
	display: flex;
	align-items: center;
}

.agreement-text {
	color: #aaaaaa;
	flex: 1;
	font-size: 28rpx;
}

.agreement-icon {
	width: 48rpx;
	height: 48rpx;
	margin-right: 20rpx;
}

.pwd-container {
	display: flex;
	align-items: center;
	justify-content: space-around;
	margin-top: 50rpx;

	.line {
		background-color: #c6c7cd;
		width: 1rpx;
		height: 32rpx;
	}
}

.to-pwd {
	font-size: 28rpx;
	color: #333333;
}

.btn-submit {
	margin-left: 0;
	margin-right: 0;
	margin-top: 30rpx;
}

.btn-fen,
.btn-black {
	border-radius: 96rpx;
	color: #fff;
	font-size: 36rpx;
	height: 96rpx;
	line-height: 96rpx;
	align-items: center;
}

.btn-fen {
	background-color: #fe58b7 !important;
}

.btn-black {
	background-color: #1f1f1f !important;
}

.btn-online {
	background-color: transparent !important;
	color: #1F1F1F !important;

	&::after {
		display: none;
	}
}

.popup-wrapper {
	width: 100%;
	height: 420rpx;
	background: #FFFFFF;
	border-radius: 32rpx 32rpx 0 0;
	position: relative;
	padding: 32rpx;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	gap: 40rpx;
	align-items: center;

	.close-icon {
		position: absolute;
		width: 48rpx;
		height: 48rpx;
		top: 24rpx;
		right: 24rpx;
	}

	.header {
		width: 100%;
		text-align: center;
		font-weight: 500;
		font-size: 28rpx;
		color: #3D3D3D;
		line-height: 40rpx;
	}

	.agreement {
		font-size: 28rpx;
		color: #48DAEA;
		line-height: 40rpx;
	}
}
</style>