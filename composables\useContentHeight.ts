// 内容高度计算函数
export default function useContentHeight(options = {}) {
	// 获取系统信息
	const sysInfo = uni.getSystemInfoSync();

	// 默认配置
	const defaultOptions = {
		baseSpacing: '100rpx',  // 默认底部间距
		navBarHeight: () => 44, // 导航默认高度，可以是一个函数
		subtractNavBar: true,   // 是否减去导航栏高度
		subtractStatusBar: true, // 是否减去状态栏高度
		subtractSafeArea: true,  // 是否减去底部安全区域
	};

	// 合并
	const config = {
		...defaultOptions,
		...options,
	};

	// 状态栏高度（单位px）
	const statusBarHeight = sysInfo.statusBarHeight || 44;
	// 安全区域底部高度（单位px）
	const safeAreaInsetBottom = config.subtractSafeArea ? (sysInfo.safeAreaInsets?.bottom || 0) : 0;
	// 导航栏高度：如果 config.navBarHeight 是函数，则调用函数获取；否则直接使用（数字）
	let navBarHeight : number;
	if (typeof config.navBarHeight === 'function') {
		navBarHeight = config.navBarHeight();
	} else {
		navBarHeight = config.navBarHeight;
	}

	return {
		contentHeight: `calc(100vh - ${navBarHeight}px - ${statusBarHeight}px - ${safeAreaInsetBottom}px - ${config.baseSpacing})`,
		statusBarHeight,
		navBarHeight,
		safeAreaInsetBottom
	};
}