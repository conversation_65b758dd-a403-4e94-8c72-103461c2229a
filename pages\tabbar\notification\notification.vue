<template>
	<view class="app-container">
		<z-paging ref="paging" :auto="false" :fixed="true" :refresher-enabled="false" :loading-more-enabled="false"
			:hide-empty-view="true">
			<template #top>
				<Navbar leftText="消息" :type="4" />
				<view class="search">
					<view style="width: 592rpx; margin-left: 24rpx">
						<NotificationNavbar :state="true" placeholder="消息内容"></NotificationNavbar>
					</view>
					<!-- 	<image src="../../../static/icon/<EMAIL>" mode="" @click="toContacts"></image> -->
					<view class="searchYQ" @click="showYQ">
						<image src="../../../static/icon/<EMAIL>" mode="" @click="toContacts"></image>
						<text>邀请朋友</text>
					</view>
				</view>
			</template>
			<view class="content allPidding">
				<view class="messageBox" v-for="(item, index) in messageList" @click="toType(item)" :key="index">
					<image :src="item.image" mode=""></image>
					<view class="messageBoxRight">
						<view>
							<text>{{ item.name }}</text>
							<text>{{ item.time }}</text>
						</view>
						<view>
							<text class="textHide">{{ item.body }}</text>
							<view v-if="item.count">{{ item.count }}</view>
						</view>
						<view class="type" v-if="item.type == 2">商家</view>
					</view>
				</view>
				<view v-if="friendConversationList.length > 0">
					<view v-for="item in friendConversationList" class="messageBox" :key="item.id"
						@click="toFriendConversation(item)">
						<image :src="prefixFilePath(item.friend.avatar)"></image>
						<view class="messageBoxRight">
							<view>
								<text>{{ item?.friend?.nickname || '-' }}</text>
								<text>{{ item.time }}</text>
							</view>
							<view>
								<text class="textHide" v-if="item.message">{{ item.message.msg_type === 'text' ? item.message.message :
									item.message.msg_type === 'file' ? '[图片]' : '' }}</text>
								<view v-if="item?.count">{{ item.count }}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<template #bottom>
				<cc-myTabbar :tabBarShow="3" @change="handleTabChange"></cc-myTabbar>
			</template>
			<uv-overlay :show="overlayShow">
				<view class="serve" @tap.stop>
					<view class="serveTitle">
						<text>推荐服务</text>
						<image @click="overlayShow = false" src="../../../static/icon/<EMAIL>" mode=""></image>
					</view>
					<view style="margin-top: 108rpx"></view>
					<view class="allPidding bottomBox">
						<view class="serveRedTitle">
							<image src="../../../static/icon/<EMAIL>" mode=""></image>
							<text>好友消费拿分佣</text>
						</view>
						<view class="serveList" @click="QRcode">
							<view>
								<image src="../../../static/icon/<EMAIL>" mode=""></image>
								<view>
									<text style="padding-bottom: 16rpx">分享邀请码</text>
									<text>我的邀请码：{{ inviteCode }}</text>
								</view>
							</view>
							<image style="width: 40rpx; height: 40rpx" src="../../../static/icon/<EMAIL>" mode=""></image>
						</view>
						<view class="serveList" style="position: relative">
							<button style="width: 100%; height: 144rpx; position: fixed; opacity: 0" open-type="share"></button>
							<view>
								<image src="../../../static/icon/<EMAIL>" mode=""></image>
								<view>
									<text style="padding-bottom: 16rpx">添加微信朋友</text>
									<text>分享专属链接给微信中的朋友</text>
								</view>
							</view>
							<image style="width: 40rpx; height: 40rpx" src="../../../static/icon/<EMAIL>" mode=""></image>
						</view>
					</view>
				</view>
			</uv-overlay>
		</z-paging>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { onShow, onShareAppMessage } from '@dcloudio/uni-app';
import { myInviteCode, myQRInviteCode } from '@/api/user';
import Config from '@/common/config.js'

import { useNotification } from './hooks/useNotification';

const store = useStore();
const commonStore = computed(() => store.state.common);
const userInfo = computed(() => store.state.userInfo);

const { messageList, systemNewMsgCount, friendConversationList } = useNotification();
const initRef = ref(false);

onShow(async () => {
	uni.hideTabBar();
	if (initRef.value) {
		store.dispatch('common/getAdminSetting');
	}
	await store.dispatch('message/initClient');
});

onMounted(() => {
	uni.hideTabBar();
	initRef.value = true;
});

const handleTabChange = (index) => {
	if (index !== 3) {
		store.dispatch('message/closeSocket');
	}
};

// function toContacts() {
// 	uni.navigateTo({
// 		url: '/pages/tabbar/notification/contacts'
// 	});
// }
function toType(item) {
	if (item.type == 0) {
		systemNewMsgCount.value = 0;
		uni.navigateTo({
			url: `/pages/tabbar/notification/systemNotification`
		});
		return
	}
	const title = encodeURIComponent(item.name)
	if (item.type == 1 || item.type == 2) {
		uni.navigateTo({
			url: `/pages/tabbar/notification/ordersNotification?type=${item.type}&title=${title}`
		});
	}
}

function toFriendConversation(item) {
	// 清除该好友的未读消息
	store.commit('friendNotification/clearFriendUnreadCount', item.id);
	const { nickname, avatar, id: friendId } = item.friend;

	uni.navigateTo({
		url: `/pages/packageA/friend-chat/friend-chat?id=${item.id}&friendId=${friendId}&nickname=${nickname}&avatar=${avatar}&from=notification`
	})
}

const overlayShow = ref(false);
let inviteCode = ref(null);
function showYQ() {
	myInviteCode().then((res) => {
		if (res.code == 20000) {
			inviteCode.value = res.data.invite_code;
			overlayShow.value = true;
		}
	});
}
function QRcode() {
	const showQRImage = (url) => {
		uni.previewImage({
			current: prefixFilePath(url),
			urls: [prefixFilePath(url)],
			longPressActions: {
				itemList: ['发送给朋友', '保存图片', '收藏'],
				success: () => { },
				fail: (err) => console.error('长按操作失败:', err)
			}
		});
	};

	if (userInfo.value.invite_url) {
		showQRImage(userInfo.value.invite_url);
		return;
	}

	const params = {
		page: 'pages/tabbar/home/<USER>',
		scene: `inviteCode=${inviteCode.value}`,
		check_path: false,
		env_version: Config.envVersion
	};

	myQRInviteCode(params, true)
		.then(res => {
			showQRImage(res.data.url)
			store.dispatch('userInfo/getUserInfo');
		})
		.catch(err => {
			console.error('生成二维码失败:', err);
			uni.showToast({
				title: '生成二维码失败',
				icon: 'none'
			});
		});
}
function prefixFilePath(url) {
	if (!url) return '';
	return commonStore.value.cloudFileUrl + url;
}
onShareAppMessage(() => {
	return {
		imageUrl: '../../../static/img/share.jpg',
		title: `「(๑•̀ㅂ•́)و✧ 发现宝藏！速戳『次元星尘』小程序，动漫周边、同好集结全都有~输入分享码【${inviteCode.value}】还能解锁隐藏福利哒！ヽ(✿ﾟ▽ﾟ)ノ」 `,
		path: `/pages/tabbar/home/<USER>
	};
});
</script>
<style lang="scss" scoped>
// :deep(.flex-1) {
// 	width: 592rpx !important;
// }

.allPidding {
	padding: 0 24rpx;
}

.textHide {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.search {
	margin-top: 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;

	// image {
	// 	width: 64rpx;
	// 	height: 64rpx;
	// 	z-index: 9999 !important;
	// 	margin-right: 36rpx;
	// }
	.searchYQ {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-right: 36rpx;

		text {
			font-weight: 400;
			font-size: 20rpx;
			color: #3d3d3d;
		}

		image {
			width: 64rpx;
			height: 64rpx;
			z-index: 9999 !important;
		}
	}
}

.content {
	margin-top: 36rpx;

	.messageBox {
		height: 120rpx;
		display: flex;
		margin-bottom: 24rpx;

		image {
			width: 96rpx;
			height: 96rpx;
		}

		.messageBoxRight {
			position: relative;
			width: 582rpx;
			height: 100%;
			background: #f7f8fa;
			border-radius: 24rpx;
			margin-left: 24rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;

			.type {
				position: absolute;
				width: 64rpx;
				height: 40rpx;
				background: #ffe540;
				border-radius: 0rpx 24rpx 0rpx 16rpx;
				right: 0;
				top: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 24rpx;
				color: #3d3d3d;
			}

			view {
				display: flex;
				align-items: center;
				font-size: 28rpx;
				color: #85878d;
				justify-content: space-between;
			}

			view:nth-child(1) {
				margin-bottom: 20rpx;

				text:nth-child(1) {
					font-size: 32rpx;
					color: #1f1f1f;
					padding-left: 20rpx;
				}

				text:nth-child(2) {
					padding-right: 76rpx;
				}
			}

			view:nth-child(2) {
				text {
					padding-left: 20rpx;
					width: 476rpx;
				}

				view {
					display: flex;
					justify-content: center;
					align-items: center;
					width: fit-content;
					padding: 0 8rpx;
					height: 32rpx;
					background-color: #fe1e42;
					border-radius: 16rpx;
					font-size: 24rpx;
					color: #ffffff;
					margin-right: 20rpx;
				}
			}
		}
	}
}

.serve {
	position: fixed;
	bottom: 0;
	padding-bottom: 30rpx;
	border-radius: 32rpx 32rpx 0rpx 0rpx;
	width: 100%;
	background-color: #fff;
	font-size: 32rpx;
	color: #3d3d3d;
	height: 598rpx;

	.serveList {
		height: 144rpx;
		border-bottom: 2rpx solid #eeeeee;
		display: flex;
		justify-content: space-between;
		align-items: center;

		view {
			display: flex;
			align-items: center;

			text:nth-child(1) {
				font-weight: 400;
				font-size: 32rpx;
				color: #1f1f1f;
			}

			text:nth-child(2) {
				font-weight: 400;
				font-size: 28rpx;
				color: #85878d;
			}

			view {
				display: flex;
				flex-direction: column;
				align-items: start;
			}

			image {
				width: 96rpx;
				height: 96rpx;
				margin-right: 24rpx;
			}
		}
	}

	.bottomBox {
		.serveRedTitle {
			height: 64rpx;
			background: rgba(254, 30, 66, 0.12);
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			display: flex;
			align-items: center;

			image {
				width: 32rpx;
				height: 32rpx;
				margin-left: 20rpx;
				margin-right: 16rpx;
			}

			text {
				font-weight: 400;
				font-size: 24rpx;
				color: #fe1e42;
			}
		}
	}

	.serveTitle {
		border-radius: 32rpx 32rpx 0rpx 0rpx;
		width: 100%;
		height: 108rpx;
		background-color: #fff;
		z-index: 999;
		position: fixed;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 28rpx;

		image {
			position: fixed;
			width: 48rpx;
			height: 48rpx;
			right: 32rpx;
		}
	}
}
</style>
