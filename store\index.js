// 状态管理
import userInfo from '@/store/modules/userInfo'
import common from '@/store/modules/common'
import message from '@/store/modules/message'
import notice from '@/store/modules/notice'
import location from '@/store/modules/location'
import service from '@/store/modules/services.js'
import expoDetail from '@/store/modules/expoDetail'
import friendNotification from '@/store/modules/friendNotification'

// #ifndef VUE3
import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)
// 导入相关模块
const store = new Vuex.Store({
// #endif

// #ifdef VUE3
import { createStore } from 'vuex'
const store = createStore({
// #endif
	modules: {
		userInfo,
		common,
		message,
		notice,
		location,
		service,
		expoDetail,
		friendNotification,
	}
})

export default store
