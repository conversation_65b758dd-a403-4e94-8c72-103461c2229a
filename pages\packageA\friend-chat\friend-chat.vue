<template>
	<z-paging 
		ref="paging" 
		class="chat-wrapper" 
		:refresher-enabled="false" 
		:use-chat-record-mode="true"
		:auto-show-back-to-top="false"
		:auto-show-system-info="false"
		:hide-refresher-when-loading="true"
		:loading-more-enabled="true"
		:default-page-size="pageSize"
		safe-area-inset-bottom 
		@query="queryList"
		v-model="messageList">
		
		<!-- 顶部导航 -->
		<template #top>
			<Navbar :type="3" leftWidth="500rpx" :leftText="friendInfo.nickname" :userImg="prefixFilePath(friendInfo.avatar)"
				:onLeftTextClick="() => toUser(friendInfo.id)"></Navbar>
		</template>

		<!-- 消息列表 -->
		<view 
			v-for="(item, index) in messageList" 
			:key="item.id || index"
			style="transform: scaleY(-1);"
			class="message-wrapper">
			<!-- 时间分隔线 -->
			<view v-if="shouldShowTime(item, index)" class="time-divider">
				<text class="time-text">{{ formatMessageTime(item.created_at) }}</text>
			</view>

			<!-- 消息气泡 -->
			<view class="message-item" :class="{ 'is-myself': isMyself(item.from_id) }">
				<!-- 对方头像 -->
				<image v-if="!isMyself(item.from_id)" class="avatar" :src="prefixFilePath(friendInfo.avatar)"
					@click="toUser(item.from_id)" />

				<!-- 消息内容 -->
				<view class="message-content">
					<!-- 用户名 -->
					<view class="username" @click="toUser(item.from_id)">
						{{ isMyself(item.from_id) ? userInfo.nickname : friendInfo.nickname }}
					</view>

					<!-- 文本消息 -->
					<view v-if="item.msg_type === 'text'" class="message-bubble text-bubble">
						{{ item.message }}
					</view>

					<!-- 图片消息 -->
					<image v-if="item.msg_type === 'file'" class="message-bubble image-bubble"
						:src="prefixFilePath(item.message)" @click="previewImage(prefixFilePath(item.message))"
						mode="aspectFill" />
				</view>

				<!-- 自己的头像 -->
				<image v-if="isMyself(item.from_id)" class="avatar" :src="prefixFilePath(userInfo?.avatar)"
					@click="toUser(item.from_id)" />
			</view>
		</view>

		<!-- 底部聊天输入框 -->
		<template #bottom>
			<view class="footer allPidding bottom" :style="{ paddingBottom: keyboardHeight + 'px' }">
				<bottom-comment-input @keyboardHeightChange="onKeyboardHeightChange" @onSubmit="onSubmit" />
			</view>
		</template>
	</z-paging>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue';
import { useStore } from 'vuex';
import { onLoad, onUnload } from '@dcloudio/uni-app';
import { getFrientMessageList } from '@/api/notification';
import dayjs from 'dayjs';

const store = useStore();
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);
const userInfo = computed(() => store.state.userInfo);

// 基础数据
const paging = ref(null);
const conversationId = ref(null);
const friendInfo = ref({
	nickname: '',
	avatar: '',
	id: ''
});

// 消息列表相关
const messageList = ref([]);
const pageSize = 10;

// 键盘高度
const safeAreaBottom = uni.getWindowInfo().safeAreaInsets.bottom;
const keyboardHeight = ref(0);

// 时间显示间隔（5分钟）
const TIME_INTERVAL = 5 * 60 * 1000;

// 判断是否为自己发送的消息
const isMyself = (id) => {
	return id === userInfo.value.id;
};

// 添加文件路径前缀
function prefixFilePath(url) {
	if (!url) return '';
	return cloudFileUrl.value + url;
}

// 键盘高度变化处理
const onKeyboardHeightChange = (height) => {
	const spanHeight = (safeAreaBottom && height > 0) ? (height - safeAreaBottom / 2) : height;
	keyboardHeight.value = spanHeight;
};

// z-paging 查询方法
const queryList = async (pageNo, pageSize) => {
	try {
		const res = await getFrientMessageList(conversationId.value, {
			page: pageNo,
			size: pageSize,
		});

		if (res.code === 20000) {
			let newList = (res.data.results?.filter(item =>
				item.msg_type === 'text' || item.msg_type === 'file'
			) || []);
			
			// 使用 z-paging 的完成方法
			paging.value.complete(newList);
		} else {
			paging.value.complete(false);
		}
	} catch (error) {
		console.log('queryList Error: ', error);
		paging.value.complete(false);
	}
};

// 判断是否应该显示时间
const shouldShowTime = (currentItem, index) => {
	// 使用 scaleY(-1) 翻转后，视觉上第一条消息是最后一条，需要调整逻辑
	if (index === messageList.value.length - 1) return true;

	// 由于翻转，需要比较下一条消息的时间（视觉上的上一条）
	const nextItem = messageList.value[index + 1];
	if (!nextItem) return true;

	const currentTime = dayjs(currentItem.created_at);
	const nextTime = dayjs(nextItem.created_at);

	// 时间差超过5分钟就显示时间
	return currentTime.diff(nextTime) > TIME_INTERVAL;
};

// 格式化消息时间
const formatMessageTime = (timestamp) => {
	const messageTime = dayjs(timestamp);
	const now = dayjs();

	// 判断是否是今天
	if (messageTime.isSame(now, 'day')) {
		return messageTime.format('HH:mm');
	}

	// 判断是否是昨天
	if (messageTime.isSame(now.subtract(1, 'day'), 'day')) {
		return `昨天 ${messageTime.format('HH:mm')}`;
	}

	// 判断是否是今年
	if (messageTime.isSame(now, 'year')) {
		return messageTime.format('M月D日 HH:mm');
	}

	// 其他情况显示完整日期
	return messageTime.format('YYYY年M月D日 HH:mm');
};

// 发送消息
function onSubmit(comment) {
	const { text, image } = comment;

	if (text) {
		const data = {
			conversationId: conversationId.value,
			friendId: friendInfo.value.id,
			content: text,
			type: 'text'
		};
		store.dispatch('message/chatSend', data);
	}

	if (image) {
		const data = {
			conversationId: conversationId.value,
			friendId: friendInfo.value.id,
			content: image,
			type: 'file'
		};
		store.dispatch('message/chatSend', data);
	}
}

// 跳转用户主页
function toUser(id) {
	uni.navigateTo({
		url: `/pages/home-pages/user-mine?id=${id}`
	});
}

// 预览图片
function previewImage(url) {
	uni.previewImage({
		current: 0,
		urls: [url]
	});
}

// 接收新消息
const receiveMessage = (data) => {
	console.log('friendChat:receiveMessage', data);
	
	// 只处理与当前好友相关的消息
	const currentFriendId = parseInt(friendInfo.value.id);
	const currentUserId = parseInt(userInfo.value.id);
	
	// 检查消息是否与当前聊天相关：要么是好友发给我的，要么是我发给好友的
	const isFromCurrentFriend = data.from_id === currentFriendId && data.to_id === currentUserId;
	const isToCurrentFriend = data.from_id === currentUserId && data.to_id === currentFriendId;
	
	if (isFromCurrentFriend || isToCurrentFriend) {
		// 使用 z-paging 的方法添加新消息到末尾
		paging.value?.addChatRecordData(data, true);
	}
};

onLoad(async (e) => {
	const { id, nickname, avatar, from, friendId } = e;
	conversationId.value = id;
	friendInfo.value = { nickname, avatar, id: friendId };

	// 设置当前聊天会话状态
	store.commit('message/updateCurrentPageInfo', {
		...store.state.message.currenPageInfo,
		isFriend: true,
		conversationId: parseInt(id)
	});

	// 清除该好友的未读消息（如果有的话）
	if (friendId) {
		store.commit('friendNotification/clearFriendUnreadCount', parseInt(friendId));
	}

	// 监听新消息
	uni.$on('friendChat:receiveMessage', receiveMessage);

	// 初始化WebSocket连接
	await store.dispatch('message/initClient');
});

onUnload(() => {
	// 清除当前聊天会话状态
	store.commit('message/updateCurrentPageInfo', {
		...store.state.message.currenPageInfo,
		isFriend: false,
		conversationId: null
	});

	// 移除消息监听
	uni.$off('friendChat:receiveMessage', receiveMessage);
});
</script>

<style lang="scss" scoped>
.chat-wrapper {
	display: flex;
	flex-direction: column;
	height: 100vh;

	.bottom,
	.top {
		flex-shrink: 0;
	}

	.message-view {
		flex: 1;
	}
}

.allPidding {
	padding: 0 32rpx;
}

.message-wrapper {
	width: 100%;
}

.time-divider {
	display: flex;
	justify-content: center;
	margin: 32rpx 0 24rpx;

	.time-text {
		font-size: 24rpx;
		color: #999;
		padding: 8rpx 16rpx;
		border-radius: 12rpx;
	}
}

.tipsBox {
	margin-top: 32rpx;
	background-color: rgba(254, 30, 66, 0.12);
	border-radius: 24rpx;
	padding: 20rpx;

	font-size: 24rpx;
	color: #fe1e42;

	image {
		width: 25rpx;
		height: 25rpx;
		margin-right: 4rpx;
		margin-bottom: 4rpx;
	}
}

.topDetails {
	margin-top: 32rpx;
	display: flex;

	.imageView {
		width: 208rpx;
		height: 208rpx;
		border-radius: 20rpx;
		margin-right: 32rpx;
		overflow: hidden;
		background-color: #000;

		image {
			width: 208rpx;
			height: 208rpx;
		}
	}

	.topDetailsBox {
		flex: 1;
		position: relative;
		display: flex;
		flex-direction: column;

		text:nth-child(1) {
			font-weight: 700;
			font-size: 32rpx;
			color: #3d3d3d;
		}

		text:nth-child(2) {
			margin-top: 16rpx;
			font-size: 28rpx;
			color: #ffce59;
		}

		.priceText {
			margin-top: 22rpx;

			text {
				font-weight: 700;
				font-size: 36rpx;
				color: #fe58b7 !important;
				margin-top: 0;
			}

			.price {
				font-size: 24rpx;
				color: #fe58b7;
			}
		}

		.btn {
			width: 144rpx;
			height: 56rpx;
			background: #fe58b7;
			border-radius: 48rpx;
			position: absolute;
			right: 0;
			bottom: 0;
			display: flex;
			justify-content: center;
			align-items: center;

			text {
				font-size: 24rpx !important;
				color: #ffffff;
			}
		}
	}
}

.footer {
	box-sizing: border-box;
	// padding: 24rpx 24rpx 68rpx;
	padding: 0;
	width: 100%;
	border-top: 2rpx solid #f4f4f4;
	display: flex;
	align-items: center;
	justify-content: center;

	.comment-wrapper {
		width: 100%;
		height: 88rpx;
		box-sizing: border-box;
		background: #f3f3f3;
		border-radius: 40rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-left: 32rpx;
		padding-right: 40rpx;

		.placeholder {
			font-weight: 400;
			font-size: 32rpx;
			color: #85878d;
		}

		.icon-wrapper {
			display: flex;
			align-items: center;
			gap: 28rpx;

			.icon {
				width: 56rpx;
				height: 56rpx;
			}
		}
	}
}

.mention-container {
	height: fit-content;
	background: #ffffff;
	border-radius: 32rpx 32rpx 0rpx 0rpx;
}

.message-item {
	box-sizing: border-box;
	margin-top: 16rpx;
	width: 100%;
	display: flex;
	gap: 16rpx;
	align-items: flex-start;
	padding: 0 32rpx;

	&.is-myself {
		.message-content {
			align-items: flex-end;
		}
	}

	.avatar {
		width: 56rpx;
		height: 56rpx;
		border-radius: 50%;
		background-color: #eee;
	}

	.message-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 8rpx;

		.username {
			width: fit-content;
			font-size: 24rpx;
			color: #aaa;
		}

		.text-bubble {
			width: fit-content;
			font-size: 32rpx;
			color: #1f1f1f;
			padding: 16rpx;
			border-radius: 16rpx;
			background-color: #f7f8fa;
			white-space: pre-wrap;
			word-break: break-word;
		}

		.image-bubble {
			width: 128rpx;
			height: 128rpx;
			border-radius: 16rpx;
		}
	}
}
</style>
