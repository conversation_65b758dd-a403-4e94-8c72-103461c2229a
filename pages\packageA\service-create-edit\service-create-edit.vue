<template>
  <z-paging ref="paging" class="app-container" :fixed="false">
    <template #top>
      <Navbar :type="1" :leftText="navTitle" leftWidth="240rpx"></Navbar>
    </template>

    <view class="wrapper">
      <dust-crop-image :width="300" :height="300" @crop="(e) => chooseCover(e)">
        <template #trigger>
          <view class="upload-cover" v-if="!form.cover">
            <image class="icon" src="../static/service/<EMAIL>"></image>
            <view>添加封面</view>
          </view>
          <view class="upload-cover has-cover" v-else>
            <image class="cover" mode="aspectFit" :src="form.previewCover"></image>
          </view>
        </template>
      </dust-crop-image>
      <view class="description-wrapper">
        <view class="label">套餐标题</view>
        <textarea placeholder="输入内容" v-model="form.name" />
      </view>

      <view class="row-item-wrapper flex">
        <view class="label">参考价：</view>
        <!-- <view class="price-input-wrapper">
          <text class="price-prefix">￥</text>
          <input type="number" v-model="form.price" />
        </view> -->
        <prefix-input v-model:inputValue="form.price"></prefix-input>
      </view>
      <view class="row-item-wrapper flex" @click="() => openPopup('bond')">
        <view class="label">保证金：</view>
        <view class="flex">
          <text>{{ form.bond }}%</text>
        </view>
      </view>

      <view class="row-item-wrapper flex" @click="() => openPopup('status')">
        <view class="label">状态</view>
        <view class="flex items-center row-item-value">
          <text>{{ form.is_on_sale ? '上架' : '下架' }}</text>
          <image class="icon" src="/static/icon/<EMAIL>"></image>
        </view>
      </view>
    </view>

    <template #bottom>
      <view class="footer flex" v-if="isEdit">
        <view class="secondary btn" @click="handleDelete">删除</view>
        <view class="primary btn" @click="handleUpdate">保存</view>
      </view>
      <view class="footer" v-else>
        <view class="primary btn" @click="handleCreate">确认</view>
      </view>
    </template>
  </z-paging>
  <uv-popup ref="popup" mode="bottom" bgColor="none">
    <view class="popup-wrapper" v-if="popupType === 'status'">
      <view class="status-item" :class="{ active: form.is_on_sale === true }" @click="() => handleSelection(true)">
        <text>上架</text>
        <image v-if="form.is_on_sale === true" class="checked-icon" src="/static/icon/<EMAIL>"></image>
      </view>
      <view class="status-item" :class="{ active: form.is_on_sale === false }" @click="() => handleSelection(false)">
        <text>下架</text>
        <image v-if="form.is_on_sale === false" class="checked-icon" src="/static/icon/<EMAIL>"></image>
      </view>
    </view>
    <view class="popup-wrapper" v-if="popupType === 'bond'">
      <view class="status-item" :class="{ active: form.bond === item }" v-for="item in bondList" :key="item"
        @click="() => handleSelectionBond(item)">
        <text>{{ item }}</text>
        <image v-if="form.bond === item" class="checked-icon" src="/static/icon/<EMAIL>"></image>
      </view>
    </view>
  </uv-popup>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app';
import { useCommit } from './hook/useCommit';
import { useTitle } from '../service-create/hooks/useTitle';

const popup = ref(null)
const popupType = ref(null)
const isEdit = ref(false)
const itemId = ref(0)
const serviceId = ref(0)
const bondList = ref([0, 5, 10, 15, 20])
const form = ref({
  cover: '',
  previewCover: '',
  name: '',
  price: '',
  bond: 10,
  is_on_sale: true,
})

onLoad((option) => {
  isEdit.value = (option.edit === 'true')
  itemId.value = option.itemId
  serviceId.value = option.serviceId
})

const { isDaliy } = useTitle()

const { handleCreate, handleDelete, handleUpdate } = useCommit({ form, itemId, serviceId })

function openPopup(type) {
  popup.value.open()
  popupType.value = type
}

const handleSelection = (value) => {
  form.value.is_on_sale = value
  popup.value.close()
}

const handleSelectionBond = value => {
  form.value.bond = value
  popup.value.close()
}

const navTitle = computed(() => {
  if (isDaliy.value) {
    return isEdit.value ? '编辑套餐' : '新增套餐'
  } else {
    return isEdit.value ? '编辑服务项' : '套餐详情'
  }
})

const chooseCover = (e) => {
  const imagePath = e.tempFilePath
  if (imagePath) {
    form.value.cover = imagePath
    form.value.previewCover = imagePath
  }
}
</script>

<style scoped lang='scss'>
.wrapper {
  box-sizing: border-box;
  padding: 0 24rpx;
  width: 100%;
  margin-top: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 32rpx;

  .upload-cover {
    width: 100%;
    height: 368rpx;
    border-radius: 20rpx;
    background-color: #efefef;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20rpx;
    position: relative;
    font-size: 28rpx;
    color: #85878D;
    overflow: hidden;

    &.has-cover {
      background-color: #000;
    }

    .cover {
      width: 100%;
      height: 100%;
    }

    .close-icon {
      position: absolute;
      top: 0;
      right: 0;
      width: 40rpx;
      height: 40rpx;
    }

    .icon {
      width: 64rpx;
      height: 64rpx;
    }
  }

  .description-wrapper {
    width: 100%;
    height: 246rpx;
    background: #EEEEEE;
    border-radius: 20rpx;
    box-sizing: border-box;
    padding: 24rpx;
    display: flex;
    flex-direction: column;

    .label {
      width: 100%;
      font-size: 28rpx;
      color: #1F1F1F;
      line-height: 40rpx;
    }

    textarea {
      flex: 1;
      width: 100%;
      margin-top: 24rpx;
    }
  }

  .row-item-wrapper {
    width: 100%;
    height: 80rpx;
    box-sizing: border-box;
    padding: 24rpx;
    background: #F7F8FA;
    border-radius: 16rpx;
    justify-content: space-between;
    align-items: center;

    .label {
      font-size: 32rpx;
      color: #3D3D3D;
      line-height: 32rpx;
    }

    .icon {
      width: 40rpx;
      height: 40rpx;
    }

    .row-item-value {
      gap: 8rpx;
      font-size: 32rpx;
      color: #3D3D3D;
    }

    input {
      width: 80rpx;
      text-align: right;
    }

    .price-input-wrapper {
      display: flex;
      align-items: center;
      font-size: 32rpx;
      color: #3D3D3D;
      line-height: 32rpx;
    }
  }
}

.footer {
  width: 100%;
  box-sizing: border-box;
  padding: 0 24rpx 68rpx;
  gap: 24rpx;

  .btn {
    flex: 1;
    height: 96rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 40rpx;
    font-weight: 700;
    font-size: 32rpx;
    color: #1F1F1F;

    &.primary {
      background: #FE58B7;
      color: #fff;
    }

    &.secondary {
      background-color: #eee;
    }
  }
}

.popup-wrapper {
  width: 100%;
  min-height: 300rpx;
  overflow-y: auto;
  box-sizing: border-box;
  padding: 24rpx;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 24rpx;

  .status-item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 24rpx;
    border-bottom: 2rpx solid #EEEEEE;
    font-size: 32rpx;
    color: #1F1F1F;

    &:last-child {
      border: none;
    }

    &.active {
      color: #FE58B7;
    }

    .checked-icon {
      width: 40rpx;
      height: 40rpx;
    }
  }
}
</style>