<template>
  <view class="app-container">
    <Navbar :type="1" leftWidth="240rpx" leftText="绑定银行卡"></Navbar>

    <view class="wrapper">
      <view class="title">
        输入银行卡信息
      </view>

      <view class="row-wrapper">
        <view class="label">卡号</view>
        <input class="form-item" placeholder="请输入" v-model="form.cardNo" @blur="checkBankCard" />
      </view>

      <view class="row-wrapper">
        <view class="label">所属银行</view>
        <view class="form-item flex items-center justify-between" @click="openBankPicker">
          <text>{{ form.bank || '选择所属银行' }}</text>
          <image class="icon" src="../static/service/down-tra.png"></image>
        </view>
      </view>

      <dust-location-picker @confirm="confirmLocation">
        <template #trigger>
          <view class="row-wrapper">
            <view class="label">银行卡开户所在地</view>
            <view class="form-item flex items-center justify-between">
              <text>{{ form.location.label || '选择银行开户所在地' }}</text>
              <image class="icon" src="../static/service/down-tra.png"></image>
            </view>
          </view>
        </template>
      </dust-location-picker>

      <view class="row-wrapper">
        <view class="label">所属人姓名</view>
        <input class="form-item" placeholder="请输入" v-model="form.name" />
      </view>

      <view class="row-wrapper">
        <view class="label">身份证号</view>
        <input class="form-item" placeholder="请输入" v-model="form.idCard" />
      </view>

      <view class="row-wrapper">
        <view class="label">银行预留手机号</view>
        <view class="form-item flex items-center ">
          <view class="prefix">+086</view>
          <input type="digit" placeholder="请输入" v-model="form.phone" />
        </view>
      </view>

      <view class="btn" @click="confirmBindCard">下一步</view>
    </view>

    <uv-picker ref="bankPicker" :columns="bankList" closeOnClickOverlay round="32rpx"
      confirmColor="#FE58B7" @confirm="selectBank"></uv-picker>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import bankCardData from './bankType.json'
import { bindBankCard } from '@/api/wallet'

const form = ref({
  cardNo: '',
  bank: '',
  cardType: '',
  name: '',
  idCard: '',
  phone: '',
  location: {
    province: '',
    city: '',
    label: ''
  }
})

const bankList = ref([Object.values(bankCardData)])
const bankPicker = ref(null)
const openBankPicker = () => {
  bankPicker.value.open()
}
const closeBankPicker = () => {
  bankPicker.value.close()
}

const selectBank = ({value}) => {
  form.value.bank = value[0]
  closeBankPicker()
}

const confirmLocation = (value) => {
  form.value.location = {
    province: value[0].full_code,
    city: value[1].full_code,
    label: value[0].name + ' ' + value[1].name
  }
}

const checkBankCard = () => {
  if (!form.value.cardNo) return

  uni.request({
    url: `https://ccdcapi.alipay.com/validateAndCacheCardInfo.json?_input_charset=utf-8&cardNo=${form.value.cardNo}&cardBinCheck=true`,
    method: 'GET',
    success: (res) => {
      if (res.data.bank) {
        form.value.bank = bankCardData[res.data.bank]
        form.value.cardType = res.data.cardType
      }
    },
    fail: (err) => {
      console.log(err)
    }
  })
}

const checkBindCardInfo = () => {
  function isMobileNumber(phoneNumber) {
    const mobileRegex = /^1(3|4|5|7|8)\d{9}$/;
    return mobileRegex.test(phoneNumber);
  }

  function isIdCardNumber(idCardNumber) {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    return idCardRegex.test(idCardNumber);
  }

  if (!form.value.cardNo) {
    uni.showToast({ title: '请输入银行卡号', icon: 'none' })
    return false
  }

  if (!form.value.name) {
    uni.showToast({ title: '请输入所属人姓名', icon: 'none' })
    return false
  }

  if (!form.value.idCard) {
    uni.showToast({ title: '请输入所属人身份证号', icon: 'none' })
    return false
  }

  if (!form.value.location.label) {
    uni.showToast({ title: '请选择银行卡开户所在地', icon: 'none' })
    return false
  }

  if (!form.value.phone) {
    uni.showToast({ title: '请输入手机号', icon: 'none' })
    return false
  }

  if (!isIdCardNumber(form.value.idCard)) {
    uni.showToast({ title: '请输入正确的身份证号', icon: 'none' })
    return false
  }

  if (!isMobileNumber(form.value.phone)) {
    uni.showToast({ title: '请输入正确的手机号', icon: 'none' })
    return false
  }
  return true
}

const confirmBindCard = async () => {
  if (checkBindCardInfo()) {
    const params = {
      card_no: form.value.cardNo,
      bank: form.value.bank,
      name: form.value.name,
      cert_no: form.value.idCard,
      prov_id: form.value.location.province,
      area_id: form.value.location.city,
      is_settle_default: false,
      phone: form.value.phone,
      type: form.value.cardType
    }
    try {
      const res = await bindBankCard(params)
      if (res.code === 20000) {
        uni.$emit('wallet-bankcard-reload-data')
        uni.$emit('wallet-page-reload-data')
        uni.navigateBack({
          delta: 1
        })
      }
	  // TODO: CODE
      // if (res.code === 4503003) {
      //   uni.navigateTo({
      //     url: '/pages/packageA/verify-idcard/verify-idcard'
      //   })
      // }
    } catch (error) {
      console.log(error, 'bindBankCard')
    }
  }
}
</script>

<style scoped lang='scss'>
.wrapper {
  margin-top: 40rpx;
  flex: 1;
  box-sizing: border-box;
  padding: 0 24rpx;

  .title {
    font-size: 32rpx;
    color: #1D2129;
    margin-bottom: 8rpx;
  }

  .row-wrapper {
    margin-top: 40rpx;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 12rpx;

    .label {
      font-size: 32rpx;
      color: #3D3D3D;
    }

    .form-item {
      width: 100%;
      height: 72rpx;
      background: #F7F8FA;
      border-radius: 16rpx;
      box-sizing: border-box;
      padding: 20rpx 32rpx 20rpx 24rpx;
      font-size: 32rpx;
      color: #3D3D3D;

      .icon {
        width: 32rpx;
        height: 32rpx;
      }

      .prefix {
        margin-right: 24rpx;
      }

      input {
        width: 100%;
      }
    }
  }

  .btn {
    width: 100%;
    height: 96rpx;
    background: #FE58B7;
    border-radius: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36rpx;
    color: #FFFFFF;
    margin-top: 56rpx;
  }
}

.popup-wrapper {
  width: 100%;
  height: 564rpx;
  background: #FFFFFF;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-y: scroll;
}
</style>