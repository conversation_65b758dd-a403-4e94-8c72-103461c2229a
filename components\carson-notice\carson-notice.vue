<template>
	<view v-if="show" class="carson-notice" :style="{ backgroundColor, borderRadius }">
		<image v-if="!!customIcon" :src="customIcon"
			:style="{ width: fontSize * 1.5 + 'px', height: fontSize * 1.5 + 'px' }">
		</image>
		<uni-icons v-if="!customIcon && (showIcon === true || showIcon === 'true')" class="notice_left" type="sound"
			:color="color" :size="fontSize * 1.5" />
		<!-- 垂直滚动用到轮播组件 -->
		<swiper class="notice-center" vertical v-if="direction == 'column'" :autoplay="true"
			:interval="speed == 'fast' ? 4000 : speed == 'normal' ? 6000 : 10000" :duration="500" :circular="true"
			disable-touch>
			<swiper-item v-for="(item, index) in list" :key="index" class="swiperIn" @tap="goItem(item)">
				<view class="swiperInView" :style="{
					color: color,
					fontSize: fontSize + 'px',
					lineHeight: fontSize * 1.5 + 'px',
					minHeight: fontSize * 1.5 + 'px',
				}">{{ item[theKey] }}</view>
			</swiper-item>
		</swiper>
		<!-- 水平滚动取第一条公告 -->
		<view class="notice-center2" :style="{
			color: color,
			fontSize: fontSize + 'px',
			lineHeight: fontSize * 1.5 + 'px',
			minHeight: fontSize * 1.5 + 'px',
		}" v-else>
			<view class="notice-center_view" :class="speed">{{ list[rowIndex][theKey] }}</view>
		</view>
		<view class="notice_right" v-if="isShowGetMore" @tap="goMore">
			<text v-if="!useMoreIcon" :style="{ color: moreColor, fontSize: fontSize + 'px' }">{{ moreText }}</text>
			<uni-icons v-else :type="moreIconType" :color="moreColor"></uni-icons>
		</view>
		<view class="uni-noticebar-close" v-if="isShowClose" @tap="close">
			<uni-icons type="closeempty" :color="color" :size="fontSize * 1.1" />
		</view>
	</view>
</template>
<script setup name="carson-notice">
import { ref, watch, computed, onMounted, onBeforeUnmount } from 'vue'

const emits = defineEmits(['goItem', 'goMore', 'close']);
const props = defineProps({
	// 是否显示左侧icon
	showIcon: {
		type: [Boolean, String],
		default: true
	},
	// 是否将左侧 icon 替换成自定义
	customIcon: {
		type: String,
		default: ''
	},
	// 是否显示更多
	showMore: {
		type: [Boolean, String],
		default: true
	},
	useMoreIcon: {
		type: [Boolean, String],
		default: false
	},
	moreIconType: {
		type: String,
		default: 'right'
	},
	moreText: {
		type: String,
		default: '查看更多'
	},
	backgroundColor: {
		type: String,
		default: '#FFF9EA'
	},
	borderRadius: {
		type: String,
		default: '0px'
	},
	color: {
		type: String,
		default: '#FF9A43'
	},
	fontSize: {
		type: Number,
		default: 13
	},
	moreColor: {
		type: String,
		default: '#FF9A43'
	},
	//公告数组，必须是二维数组
	list: {
		type: Array,
		default() {
			return []
		}
	},
	//公告数组的键名
	theKey: {
		type: String,
		default: 'title'
	},
	//方向，column垂直，row水平时取第一条公告
	direction: {
		type: String,
		default: 'column'
	},
	//滚动速度，快fast，慢slow，默认normal
	speed: {
		type: String,
		default: 'normal'
	},
	showClose: {
		// 是否显示左侧关闭按钮
		type: [Boolean, String],
		default: false
	},
})
let rowIndex = ref(0)
let show = ref(true)
let timer = ref(null) // 新增一个 timer 变量用于存储定时器

const isShowGetMore = computed(() => props.showMore === true || props.showMore === 'true')
const isShowClose = computed(() => props.showClose === true || props.showClose === 'true')

const goItem = (item) => {
	emits('goItem', item)
}
const goMore = () => {
	emits('goMore')
}
const close = () => {
	show.value = false;
	emits('close')
}
const setTime = () => {
	if (props.direction == "row") {
		let time = props.speed == "fast" ? 4000 : props.speed == "normal" ? 6000 : 10000;

		const nextRow = () => {
			if (rowIndex.value == props.list.length - 1) {
				rowIndex.value = 0;
			} else {
				rowIndex.value = rowIndex.value + 1;
			}
			timer.value = setTimeout(nextRow, time);
		};

		timer.value = setTimeout(nextRow, time); // 第一次触发
	}
}
onMounted(() => {
	setTime()
})
onBeforeUnmount(() => {
	if (timer.value) {
		clearTimeout(timer.value); // 在组件销毁时清除定时器
	}
})
</script>
<style lang="scss">
.carson-notice {
	font-size: 26upx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 10upx 20upx;

	.notice_left {
		margin: 0 10upx 0 0;
	}

	.notice-center {
		flex: 1;

		.swiperIn {
			height: 80upx;
			display: flex;
			align-items: center;

			.swiperInView {
				overflow: hidden;
				display: -webkit-box;
				-webkit-line-clamp: 1;
				-webkit-box-orient: vertical;
			}
		}
	}

	.notice-center2 {
		flex: 1;
		position: relative;
		display: flex;
		align-items: center;
		overflow: hidden;

		.notice-center_view {
			position: absolute;
			white-space: nowrap;
			padding-left: 100%;

			&.fast {
				animation: notice 4s linear infinite both;
			}

			&.normal {
				animation: notice 6s linear infinite both;
			}

			&.slow {
				animation: notice 10s linear infinite both;
			}
		}
	}

	.notice_right {
		margin: 0 0 0 10upx;
	}

	.uni-noticebar-close {
		margin-left: 8px;
		margin-right: 5px;
	}

	@keyframes notice {
		100% {
			transform: translate3d(-100%, 0, 0);
		}
	}
}
</style>
