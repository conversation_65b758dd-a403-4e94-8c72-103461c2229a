<!-- 活动发布主界面 -->
<template>
	<view class="container">
		<Navbar :type="1"  leftText="举报"></Navbar>
		<view class="main">
			<view class="reason-container">
				<text class="label">举报理由:</text>
				<text>{{reason}}</text>
			</view>
			<view class="desc-container">
				<text class="label">举报描述</text>
				<textarea class="desc" type="textarea" v-model="desc" placeholder="请输入活动描述" placeholder-class="desc-placeholder" />
			</view>
			<view class="item">
				<text>图片材料提交</text>
				<view style="padding: 15rpx;"/>
				<image-upload :fileList="imageUrls" @delete="deleteImage" @uploadSucess="uploadImage" multiple maxCount="4"/>
			</view>
			
		</view>
		<view class="footer-contener">
			<view class="btn-container" @tap.stop="commitHandle">
				<text>提交</text>
			</view>
		</view>
	</view>
	
</template>
<script setup lang="ts">
	import { ref, toRaw, onUnmounted, watchEffect, computed } from 'vue';
	import { reportService, reportComiket } from "@/api/report";
	import { onLoad } from "@dcloudio/uni-app";
	import typeSelect from "./components/type-select.vue";
	import violationData from "./const";
	import { ReportType, QueryParma } from "./const";
	import {
		uploadFile
	} from '@/api/request';
	
	
	type Data = typeof violationData;
	type DataItem = Data[number];
	
	const reason = ref("");
	const desc = ref("");
	let templateQuery: QueryParma;
	onLoad((query: QueryParma) => {
		templateQuery = query;
		const selectedData = violationData.find(item => item.value == query.firtstTypeCode);
		const selectItem = selectedData.children.find(item => item.value == query.secondTypeCode);
		reason.value = selectItem.text;
		console.log("创建的", query)
	});
	
	// 上传的图片数组
	const imageUrls = ref<{url: string}[]>([]);
	const uploadImage = (files: any[]) => {
		files.forEach(item => {
			imageUrls.value.push({url: item.url});
		})
	};
	
	// 图片删除
	const deleteImage = (item: {index: number}) => {
		imageUrls.value.splice(item.index, 1)
	}
	
	// 提交检验输入项
	const preCommiteCheck = function() {
		interface ChackItem {
			name: string;
			validate: () => string | undefined;
		}
		const checkItems: ChackItem[] = [
			{name: "举报图片", validate: () => {
				if (imageUrls.value.length) return;
				return "请上传举报图片"
			}},
		];
		 
		for (let item of checkItems) {
			const errMsg = item.validate();
			if (errMsg) {
				uni.showToast({
					title: errMsg,
					icon: "none"
				});
				return false
			}
		}
		return true;
	}
	
	const commitHandle = () => {
		
		const validate = preCommiteCheck();
		if (!validate) return;
		uni.hideLoading()
		uni.showLoading({
			title: "提交中..."
		});
		const request = templateQuery.type == ReportType.Service ? reportService : reportComiket;
		request(templateQuery.id, {
			"reason_type": +templateQuery.secondTypeCode,
			"reason": imageUrls.value.length && JSON.stringify(imageUrls.value),
			description: desc.value
		}).then(res => {
			const isSucessed = res.code === 20000;
			uni.hideLoading();
			if (!isSucessed) return uni.showToast({
				icon: "error",
				title: "提交失败"
			});
			
			uni.navigateBack({delta: 3});
		}).finally(() => uni.hideLoading());
		
	}
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		height: 100%;
		box-sizing: border-box;
		
		padding-bottom: constant(safe-area-inset-bottom);;
		padding-bottom: env(safe-area-inset-bottom);
	}
	
	.main {
		flex: 1 1 auto;
		height: 100%;
		display: flex;
		flex-direction: column;
		gap: 15rpx;
		box-sizing: border-box;
		padding: 32rpx 32rpx 24rpx;
		overflow-y: hidden;
	}
	
	.reason-container {
		display: flex;
		align-items: center;
		gap: 34rpx;
		height: 80rpx;
		width: 100%;
		border-radius: 16rpx;
		padding: 0 24rpx;
		box-sizing: border-box;
		background-color:  rgba(247, 248, 250);
	}
	.desc-container {
		display: flex;
		flex: 0 0 auto;
		width: 100%;
		height: 292rpx;
		flex-direction: column;
		gap: 20rpx;
		background-color: rgba(247, 248, 250);
		border-radius: 16rpx;
		padding: 24rpx;
		
		box-sizing: border-box;
		color: rgba(31, 31, 31);
		.title {
			width: 100%;
			background-color: transparent;
			flex: 0 0 auto;
			height: 56rpx;
			font-size: 40rpx;
			line-height: 56rpx;
			font-weight: 700;
		}
		.title-placeholder {
			color: rgba(133, 135, 141);
		}
		.desc {
			width: 100%;
			flex: 1 1 auto;
			background-color: transparent;
			font-size: 36rpx;
			line-height: 56rpx;
			font-weight: 400;
		}
		.desc-placeholder {
			color: rgba(133, 135, 141);
		}
		
	}
	.footer-contener {
		padding: 0 44rpx;
	}
	.btn-container {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 16rpx;
	
		height: 78rpx;
		flex: 1 1 auto;
		background-color: rgba(238, 238, 238);
		border-radius: 40rpx;
			
		font-size: 32rpx;
		font-weight: 700;
		image {
		   width: 40rpx;
		   height: 40rpx;
		}
	}
</style>
