<template>
  <view class="input-container">
    <!-- 前缀 -->
    <text class="prefix">￥</text>
    <!-- 输入框：无边框、透明背景，宽度动态绑定 -->
    <input class="input" type="number" :style="{ width: inputWidth + 'px' }" v-model="inputValue" @input="onInput"
      placeholder="" />
    <!-- 隐藏的测量文本 -->
    <text class="measure-text">{{ inputValue || placeholderText }}</text>
  </view>
</template>

<script setup>
import { ref, nextTick, getCurrentInstance } from 'vue'

const inputValue = defineModel('inputValue', { type: String, default: '' })

const props = defineProps({
  placeholder: {
    type: String,
    default: '请输入金额'
  },
  maxWidth: {
    type: Number,
    default: 200
  },
})
const instance = getCurrentInstance();

const inputWidth = ref(0)

const onInput = (e) => {
  // 动态计算宽度
  nextTick(() => {
    calculateWidth();
  });
}

const calculateWidth = () => {
  const query = uni.createSelectorQuery().in(instance);
  query.select('.measure-text').boundingClientRect(res => {
    if (res) {
      // 设置输入框宽度（不超过最大宽度）
      inputWidth.value = Math.min(Math.max(20, res.width), props.maxWidth);
    }
  }).exec();
}
</script>

<style scoped>
.input-container {
  display: flex;
  align-items: center;
  padding: 10rpx;
  border: none;
  border-radius: 8rpx;
}

.prefix {
  font-size: 32rpx;
  color: #333;
  margin-right: 8rpx;
}

.input {
  flex: none;
  /* 禁止拉伸 */
  font-size: 32rpx;
  border: none;
  outline: none;
  background-color: transparent;
  min-width: 20px;
  /* 最小宽度 */
}

.measure-text {
  position: absolute;
  visibility: hidden;
  /* 隐藏测量元素 */
  font-size: 32rpx;
  /* 必须与输入框字体大小一致 */
  white-space: nowrap;
}
</style>