<template>
	<view class="describes" v-if="type == 'json'">
		<text>商家简介</text>
		<text>{{ objData.instructions }}</text>
		<image :src="item" v-for="(item, index) in imgArr" mode="widthFix" @click="ckimg(index)"></image>
		<view style="height: 20rpx"></view>
	</view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useStore } from 'vuex';
const store = useStore();
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

function prefixFilePath(url) {
	if (!url) return '';
	return cloudFileUrl.value + url;
}
defineOptions({
	name: 'editorDate'
});
const props = defineProps({
	type: {
		type: String,
		default: 'json' // 默认 1 返回上一页，如果要自定义返回，则 传 0
	},
	data: {
		type: String,
		default: ''
	},
	artworks: {
		type: Array,
		default: []
	}
});
const objData = ref({});
const imgArr = ref([]);
onMounted(() => {
	if (props.type == 'json') {
		objData.value = JSON.parse(props.data);
	}
	if (props.artworks.length != 0) {
		for (let i = 0; i < props.artworks.length; i++) {
			imgArr.value.push(prefixFilePath(props.artworks[i].url));
		}
	}
});
function ckimg(index) {
	uni.previewImage({
		urls: imgArr.value,
		current: index,
		longPressActions: {
			itemList: ['发送给朋友', '保存图片', '收藏'],
			success: function (data) {},
			fail: function (err) {}
		}
	});
}
</script>

<style lang="scss">
.describes {
	border-top: 2rpx solid #d8d8d8;
	border-bottom: 2rpx solid #d8d8d8;
	display: flex;
	flex-direction: column;

	text:nth-child(1) {
		font-size: 32rpx;
		color: #3d3d3d;
		margin-top: 32rpx;
		margin-bottom: 12rpx;
	}

	text:nth-child(2) {
		font-size: 28rpx;
		color: #85878d;
		margin-bottom: 20rpx;
	}

	image {
		width: 100%;
	}
}
</style>
