<template>
	<view class="page-total">
		<view class="tab-list">
			<view class="list" v-for="(item,index) in commonStore.TabBarList" @tap="onTabBar(item,index)"
				:style="{marginTop: (item.index == 2) ?  '-20rpx' : '0px'}" :key="index">
				<template v-if="item.index !== 2">
					<image :src="item.acImg" v-if="tabBarShow === item.index" class="normal" />
					<image :src="item.img" v-if="tabBarShow !== item.index" class="normal" />
				</template>
				<view v-else class="big">
					<image :src="item.img" class="amplification" />
					<view class="line" />
				</view>
				<text v-if="item.index !== 2" :class="{ action: tabBarShow === item.index }">{{item.name}}</text>
			</view>
		</view>
		<uv-safe-bottom></uv-safe-bottom>
	</view>
	<uv-popup ref="popup" :overlay="false" bg-color="none" custom-style="height: 100%; width: 100%;" :closeOnClickOverlay="false" :safeAreaInsetTop="false">
		<publish-component @close="closePublishPage" />
	</uv-popup>
</template>

<script setup>
	import {
		ref,
		onMounted,
		computed
	} from 'vue'
	import {
		useStore
	} from 'vuex';
	import PublishComponent from "@/pages/tabbar/publish/publish";
	defineOptions({
		name: 'ccTabbar',
	})

	defineProps({
		tabBarShow: {
			type: Number,
			default: 0,
		},
	})

	const store = useStore();
	const emit = defineEmits(['change'])
	const commonStore = computed(() => store.state.common);
	
	const popup = ref(null);
	let isPublishShow = false;
	const showPublishPage = () => {
		if (isPublishShow) return;
		popup.value && popup.value.open();
		isPublishShow = true;
	}
	
	const closePublishPage = () => {
		console.log("close--1-1")
		if (!isPublishShow) return;
		popup.value && popup.value.close();
		isPublishShow = false;
	}

	onMounted(() => {
		uni.hideTabBar();
	})

	function onTabBar(item, index) {
		emit('change', item.index)
		closePublishPage();
		switch (item.index) {
			case 0:
				uni.switchTab({
					url: '/pages/tabbar/home/<USER>'
				})
				break;
			case 1:
				uni.switchTab({
					url: '/pages/tabbar/dimension/dimension'
				})
				break;
			case 2:
				showPublishPage();
				// uni.switchTab({
				// 	url: '/pages/tabbar/publish/publish'
				// })
				break;
			case 3:
				uni.switchTab({
					url: '/pages/tabbar/notification/notification'
				})
				break;
			case 4:
				uni.switchTab({
					url: '/pages/tabbar/mine/mine'
				})
				break;
		}
	}
</script>

<style lang="scss" scoped>
	.page-total {
		// box-shadow: 0rpx -1rpx 2rpx 1rpx rgba(0, 0, 0, 0.1);
		border-top: 1rpx solid #eee;
		z-index: 10;
		position: relative;
		background-color: #FFFFFF;
	}

	.tab-list {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		height: 100rpx;
		box-sizing: border-box;

		.list {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 33%;
			position: relative;

			image {
				vertical-align: middle;
			}

			.big {
				background-color: #fff;
				border: 1rpx solid #eee;
				border-radius: 2000px;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 106rpx;
				height: 106rpx;
				position: relative;

				.line {
					background-color: #fff;
					position: absolute;
					left: -10rpx;
					width: 120rpx;
					height: 160rpx;
					top: 13rpx;
				}
			}

			.amplification {
				width: 104rpx;
				height: 104rpx;
				position: relative;
				z-index: 1;
			}

			.normal {
				width: 58rpx;
				height: 58rpx;
			}

			text {
				color: #000;
				font-size: 22rpx;
			}

			.action {
				color: #000;
			}

			.center {
				position: absolute;
				bottom: 0;
			}
		}
	}
</style>