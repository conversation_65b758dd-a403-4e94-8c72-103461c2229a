<template>
  <view class="wrapper">
    <view class="header">
      <view class="title">{{ `${100} 评论` }}</view>
      <image class="close-icon" src="/static/icon/dimension/close.png" @click="onCancel"></image>
    </view>
    <view class="main">
      <z-paging ref="paging" v-model="commentList" @query="queryList" :fixed="false">
        <uni-list :border="false">
          <uni-list-item v-for="comment in commentList" :key="comment.id" :border="false">
            <template v-slot:body>
              <comment-block :item="comment" @onMore="() => openPopup(isOwner ? 0 : 1)"></comment-block>
              <view class="comment-children" v-if="comment.children && comment.children.length > 0">
                <comment-block v-for="childComment in comment.children" :item="childComment"></comment-block>
              </view>
            </template>
          </uni-list-item>
        </uni-list>
        <template #loadingMoreNoMore>
          <view></view>
        </template>
      </z-paging>
    </view>
    <view class="footer">
      <view class="comment-wrapper" @click="openRichtextPopup">
        <text class="placeholder">想说些什么</text>
        <view class="icon-wrapper">
          <image class="icon" src="/static/icon/dimension/mention.png"></image>
          <image class="icon" src="/static/icon/dimension/picture.png"></image>
          <image class="icon" src="/static/icon/dimension/emoji.png"></image>
        </view>
      </view>
    </view>
  </view>
  <uv-popup ref="popup" mode="bottom" bgColor="none">
    <video-more v-if="popupType === 0" text="删除评论" icon="/static/icon/dimension/icon_shanc.png"
      @close="closePopup"></video-more>
    <video-more v-if="popupType === 1" text="举报评论" @close="closePopup"></video-more>
  </uv-popup>

  <uv-popup ref="richtextPopup" mode="bottom" bgColor="none">
    <view class="mention-container">
      <comment-textarea v-model:comment="comment" :userList="userList" @keyboardHeightChange="onKeyboardHeightChange"></comment-textarea>
    </view>
    <view v-if="keyboardHeight" :style="{height: `${keyboardHeight}px`}"></view>
  </uv-popup>

</template>

<script setup>
import { ref } from 'vue'
import CommentBlock from './comment-block.vue';
import VideoMore from '../video-more/video-more.vue';
import CommentTextarea from '../comment-textarea/comment-textarea.vue'

const emit = defineEmits(['close'])

// 当前用户是否是视频创作者，用于展示 删除评论 / 举报评论
const isOwner = ref(true)

const comment = ref('')
const userList = ref([
	{ id: 1, name: '张三', avatar: 'https://p1.itc.cn/q_70/images03/20230427/97e4cf398c1c453f98f8135b202479d6.jpeg' },
	{ id: 2, name: '李四', avatar: 'https://p1.itc.cn/q_70/images03/20230427/97e4cf398c1c453f98f8135b202479d6.jpeg' },
	{ id: 3, name: '王五', avatar: 'https://p1.itc.cn/q_70/images03/20230427/97e4cf398c1c453f98f8135b202479d6.jpeg' },
	{ id: 4, name: '王五', avatar: 'https://p1.itc.cn/q_70/images03/20230427/97e4cf398c1c453f98f8135b202479d6.jpeg' },
	{ id: 5, name: '王五', avatar: 'https://p1.itc.cn/q_70/images03/20230427/97e4cf398c1c453f98f8135b202479d6.jpeg' },
	{ id: 6, name: '王五', avatar: 'https://p1.itc.cn/q_70/images03/20230427/97e4cf398c1c453f98f8135b202479d6.jpeg' },
	{ id: 7, name: '王五', avatar: 'https://p1.itc.cn/q_70/images03/20230427/97e4cf398c1c453f98f8135b202479d6.jpeg' },
	{ id: 8, name: '王五', avatar: 'https://p1.itc.cn/q_70/images03/20230427/97e4cf398c1c453f98f8135b202479d6.jpeg' },
]);


const popup = ref(null)
/*
* 0 -> 删评
* 1 -> 举报评论
*/
const popupType = ref(null)
const openPopup = (type) => {
  popup.value.open()
  popupType.value = type
}

const closePopup = () => {
  popup.value.close()
  popupType.value = null
}

const richtextPopup = ref(null)
const keyboardHeight = ref(0)
const openRichtextPopup = () => {
  richtextPopup.value.open()
}

const onKeyboardHeightChange = (height) => {
  keyboardHeight.value = height
  console.log(keyboardHeight.value)
}

const paging = ref(null)
const commentList = ref([])
const allList = ref(generateData())

function fetchComment({
  pageNum,
  pageSize
}) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const start = (pageNum - 1) * pageSize;
      const end = pageNum * pageSize;
      const pageData = allList.value.slice(start, end);
      resolve({
        code: 200,
        data: pageData,
      });
    }, 500); // 模拟网络延迟
  });
}

async function queryList(pageNo, pageSize) {
  const params = {
    pageNum: pageNo,
    pageSize: 10,
  }

  uni.showLoading()
  let res = await fetchComment(params)
  uni.hideLoading()
  if (res.code === 20000) {
    paging.value.complete(res.data);
  } else {
    paging.value.complete(false);
  }
}

function onCancel() {
  emit('close')
}

function generateData() {
  const list = []
  for (let i = 0; i < 100; i++) {
    const comment = {
      comment: '评论评论评论评论评论评论',
      id: i,
      time: '15:00',
      name: '用户名',
      avatar: 'https://p1.itc.cn/q_70/images03/20230427/97e4cf398c1c453f98f8135b202479d6.jpeg',
      children: [
        {
          comment: '评论评论评论评论评论评论',
          id: 'children1' + (Math.random() * 100).toFixed(2),
          time: '15:00',
          name: '用户名',
          avatar: 'https://p1.itc.cn/q_70/images03/20230427/97e4cf398c1c453f98f8135b202479d6.jpeg',
        },
        {
          comment: '评论评论评论评论评论评论',
          id: 'children1' + (Math.random() * 100).toFixed(2),
          time: '15:00',
          name: '用户名',
          avatar: 'https://p1.itc.cn/q_70/images03/20230427/97e4cf398c1c453f98f8135b202479d6.jpeg',
        }
      ]
    }
    list.push(comment)
  }
  return list
}
</script>

<style scoped lang='scss'>
.uni-list--border {
  display: none;
}

.wrapper {
  width: 100%;
  height: 664rpx;
  background: #FFFFFF;
  border-radius: 32rpx 32rpx 0rpx 0rpx;

  .header {
    width: 100%;
    padding: 30rpx 24rpx 0;
    box-sizing: border-box;
    display: flex;
    align-items: center;

    .title {
      flex: 1;
      text-align: center;
      font-weight: 500;
      font-size: 28rpx;
      color: #3D3D3D;
      line-height: 40rpx;
    }

    .close-icon {
      width: 48rpx;
      height: 48rpx;
    }
  }

  .main {
    width: 100%;
    height: 448rpx;
    overflow: auto;

    .comment-children {
      padding-left: 110rpx;
      display: flex;
      flex-direction: column;
      gap: 28rpx;
      margin-top: 28rpx;
    }
  }

  .footer {
    width: 100%;
    height: 136rpx;
    border-top: 2rpx solid #f4f4f4;
    box-sizing: border-box;
    padding: 24rpx 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .comment-wrapper {
      width: 100%;
      height: 88rpx;
      box-sizing: border-box;
      background: #F3F3F3;
      border-radius: 40rpx 40rpx 40rpx 40rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 32rpx;
      padding-right: 40rpx;

      .placeholder {
        font-weight: 400;
        font-size: 32rpx;
        color: #85878D;
      }

      .icon-wrapper {
        display: flex;
        align-items: center;
        gap: 28rpx;

        .icon {
          width: 56rpx;
          height: 56rpx;
        }
      }
    }
  }
}

.mention-container {
  height: fit-content;
  background: #FFFFFF;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  padding: 32rpx 40rpx 0;


}
</style>