<script setup>
import { ref, computed, watch } from 'vue'
import Model from '@/components/my-model/Model'
import { addFriend, delFriend, reportUser, addBest, delBest } from '@/api/message'
import { addBlackItem, deleteBlackItem } from '@/api/user'
import switch_off from '@/static/home/<USER>'
import switch_on from '@/static/home/<USER>'
import add_friend from '@/static/home/<USER>'
import del_friend from '@/static/home/<USER>'
import add_kith from '@/static/home/<USER>'
import del_kith from '@/static/home/<USER>'

let props = defineProps(['data', 'show', 'onClose', 'onChange'])
// relationship: 关系 0:无关 1:好友 2:密友
// 好友状态 11:当前用户申请好友中 12:对方申请当前用户为好友中 21:当前用户申请好友被拒绝 22:对方申请当前用户为好友被拒绝
// 密友状态 31:当前用户申请好友中 32:对方申请当前用户为好友中 41:当前用户申请好友被拒绝 42:对方申请当前用户为好友被拒绝

let info = ref(props.data)
let remark = ref(info.value.privacy?.remark || '')
let hide_my_posts = ref(info.value.privacy?.hide_my_posts || false)
let hide_their_posts = ref(info.value.privacy?.hide_their_posts || false)
let focus = ref(false)

watch(() => props.data, (newVal) => {
	info.value = newVal
})

let friendTxt = computed(() => {
	const noFriend = info.value.relationship === 11 ? '待通过' : '申请好友'
	const txt = info.value.is_friend ? '移除好友' : noFriend
	return txt
})
let kithhTxt = computed(() => {
	const noKithh = info.value.relationship === 31 ? '待通过' : '天剑亲友'
	const txt = info.value.is_best ? '移除亲友' : noKithh
	return txt
})
const onFriend = async () => {
	if (info.value.is_friend) {
		await delFriend(info.value.relation_id)
		uni.showToast({
			title: '好友已移除',
			icon: 'none'
		})
		props.onChange()
	} else {
		if (info.value.relationship === 11) {
			uni.showToast({
				title: '对方还未通过你的好友申请，请耐心等待哦！',
				icon: 'none'
			})
			return
		}
		await addFriend(info.value.userinfo.id)
		props.onChange()
		uni.showToast({
			title: '已发送好友申请',
			icon: 'none'
		})
	}
}
const onAccusation = async () => {
	await reportUser(info.value.userinfo.id)
	props.onChange()
	uni.showToast({
		title: '已举报',
		icon: 'none'
	})
}
const onBlacklist = async () => {
	if (info.value.in_blacklist) {
		await deleteBlackItem(info.value.userinfo.id)
	} else {
		await addBlackItem({ target_id: info.value.userinfo.id })
	}
	uni.showToast({
		title: info.value.in_blacklist ? '已取消拉黑' : '已拉黑',
		icon: 'none'
	})
	props.onChange()
}
const onKithh = async () => {
	if (info.value.is_best) {
		await delBest(info.value.relation_id)
		uni.showToast({
			title: '已移除亲友',
			icon: 'none'
		})
		props.onChange()
	} else {
		if (info.value.relationship === 31) {
			uni.showToast({
				title: '对方还未通过你的亲友申请，请耐心等待哦！',
				icon: 'none'
			})
			return
		}
		await addBest(info.value.userinfo.id)
		props.onChange()
		uni.showToast({
			title: '已发送亲友申请',
			icon: 'none'
		})
	}
}
const onHide = () => {
	props.onClose({
		remark: remark.value,
		hide_my_posts: hide_my_posts.value,
		hide_their_posts: hide_their_posts.value,
	})
}
</script>

<template>
	<Model :show="show" :onClose="onHide" :height="'1030rpx'">
		<template #title>
			<div class="title">
				<div class="name">{{ info.userinfo.nickname || '用户' + (info.userinfo.uid || info.userinfo.account ||
					info.userinfo.id)}}</div>
				<div class="info">{{ info.userinfo.bio }}</div>
			</div>
		</template>
		<template #content>
			<div class="content">
				<div class="item" @click="onFriend">
					<image class="item_icon" :src="info.is_friend ? del_friend : add_friend" />
					<span class="item_text">{{ friendTxt }}</span>
				</div>
				<div class="item" @click="onAccusation">
					<image class="item_icon" src="@/static/home/<USER>" />
					<span class="item_text">举报</span>
				</div>
				<div class="item" @click="onBlacklist">
					<image class="item_icon" src="@/static/home/<USER>" />
					<span class="item_text">{{ info.in_blacklist ? '取消拉黑' : '拉黑' }}</span>
				</div>
			</div>
			<div class="lable">
				<input :focus="focus" @blur="focus = false" v-model="remark" class="input" type="text"
					placeholder="设置备注" maxlength="20" />
				<image class="lable_icon" src="@/static/home/<USER>" @click="focus = true" />
			</div>
			<div class="lable" v-if="info.is_friend">
				<span :style="{ color: info.is_best ? '#FE1E42' : '#3D3D3D' }">{{ kithhTxt }}</span>
				<image class="lable_icon" :src="info.is_best ? del_kith : add_kith" @click="onKithh" />
			</div>
			<div class="lable">
				<span>不让他（她）看我的作品</span>
				<image class="lable_icon" :src="hide_my_posts ? switch_on : switch_off"
					@click="hide_my_posts = !hide_my_posts" />
			</div>
			<div class="lable">
				<span>不看他（她）的作品</span>
				<image class="lable_icon" :src="hide_their_posts ? switch_on : switch_off"
					@click="hide_their_posts = !hide_their_posts" />
			</div>
		</template>
	</Model>
</template>

<style scoped lang="scss">
.title {
	padding: 32rpx 32rpx 5rpx;

	.name {
		font-weight: 600;
		font-size: 36rpx;
		color: #1F1F1F;
		line-height: 36rpx;
	}

	.info {
		margin-top: 16rpx;
		font-size: 28rpx;
		color: #1F1F1F;
		line-height: 28rpx;
	}
}

.content {
	display: flex;
	justify-content: center;
	padding: 35rpx 0 8rpx;
	gap: 32rpx;

	.item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 206rpx;
		height: 206rpx;
		background: #F7F8FA;
		border-radius: 16rpx;

		.item_icon {
			width: 80rpx;
			height: 80rpx;
		}

		.item_text {
			margin-top: 16rpx;
			font-size: 32rpx;
			color: #3d3d3d;
			line-height: 48rpx;
		}
	}
}

.lable {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 104rpx;
	margin: 32rpx 32rpx 0;
	padding: 0 40rpx 0 32rpx;
	font-weight: 600;
	font-size: 32rpx;
	color: #3D3D3D;
	line-height: 48rpx;
	border-radius: 16rpx;
	background: #F7F8FA;

	.input {
		flex: 1;
		margin-right: 10rpx;
	}

	.lable_icon {
		width: 56rpx;
		height: 56rpx;
		margin-left: 8rpx
	}
}
</style>