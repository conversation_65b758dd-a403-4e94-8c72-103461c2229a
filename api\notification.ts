import { requestApi } from './request';

// 创建服务咨询列表
export const createConsult = (id: number) => {
	return requestApi({
		url: `/api/v1/foreground/message/consult`,
		method: 'POST',
		data: {
			service_item_id: id
		}
	});
};

//获取服务咨询
export const getConsult = (role: string) => {
	return requestApi({
		url: `/api/v1/foreground/message/consult?role=${role}`,
		method: 'GET'
	});
};
//获取服务咨询详情
export const getConsultInfo = (consult_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/message/consult/${consult_id}`,
		method: 'GET'
	});
};

//更改咨询
export const putConsult = (id: number, consult_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/message/consult/${consult_id}/item`,
		method: 'PUT',
		data: {
			service_item_id: id
		}
	});
};

interface ConsultDetailMessageParams {
	page: number;
	size: number;
}


// 获取咨询消息最新内容
export const getConsultDetailMessage = (consult_id: number, data: ConsultDetailMessageParams) => {
	return requestApi({
		url: `/api/v1/foreground/message/consult/${consult_id}/message`,
		method: 'GET',
		data
	})
}

// 上报查看时间
export const getConsultMessageReport = (consult_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/message/consult/${consult_id}/report`,
		method: 'GET',
	})
}

interface FriendMessageListParams {
	page: number;
	size: number;
	order_by: string;
}

export const getFrientMessageList = (friend_id: number, data: FriendMessageListParams) => {
	return requestApi({
		url: `/api/v1/foreground/message/socialize/${friend_id}/message`,
		method: 'GET',
		data
	})
}

export const getFriendConversationList = () => {
	return requestApi({
		url: `/api/v1/foreground/message/conversation`,
		method: 'GET'
	})
}

interface CreateFriendConversationParams {
	user_id?: number;
	friend_id?: number;
	group_id?: number;
}

export const createFriendConversation = (params: CreateFriendConversationParams) => {
	return requestApi({
		url: `/api/v1/foreground/message/conversation`,
		data: params
	})
}