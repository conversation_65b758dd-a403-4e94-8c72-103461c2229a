<template>
  <z-paging class="app-container">
    <template #top>
      <Navbar :type="1" :leftText="typeMap[type] || ''"></Navbar>
    </template>

    <view class="content">
      <view class="user-info">
        <image class="user-avatar" :src="prefixFilePath(otherUserInfo.avatar)" />
        <view class="detail">
          <view class="name">{{ otherUserInfo.nickname }}</view>
          <view class="rate-wrapper">
            <uv-rate size="32rpx" gutter="1" :value="otherUserInfo.score / 20" allowHalf active-color="#FE58B7"
              inactive-color="#FE58B7" readonly></uv-rate>
            <view class="rate-text">{{ (otherUserInfo.score / 20) > 0 ? `${(otherUserInfo.score / 20).toFixed(1)}分 推荐` :
              '暂无评分'
            }}
            </view>
          </view>
          <!-- <view class="label-wrapper">
            <view class="label-item" v-for="item in otherUserInfo.tags" :key="item">
              {{ item }}
            </view>
          </view> -->
        </view>
      </view>

      <view class="divide-horizontal"></view>

      <view class="service-wrapper" v-if="expoDataList.length > 0">
        <view class="title">漫展{{ typeMap[type] || '' }}</view>
        <uni-list class="dust-uni-list" :border="false">
          <uni-list-item :border="false" class="dust-uni-list-item" v-for="(item, index) in expoDataList"
            :key="item?.id">
            <template v-slot:body>
              <view class="combo-item-wrapper" @click="() => handleTo('expo', item?.id)">
                <expo-combo-item :dateSlot="item?.date_slot" :item="item" />
              </view>
            </template>
          </uni-list-item>
        </uni-list>
      </view>

      <view class="service-wrapper" v-if="daliyDataList.length > 0">
        <view class="title">日常{{ typeMap[type] || '' }}</view>

        <uni-list class="dust-uni-list" :border="false">
          <uni-list-item :border="false" class="dust-uni-list-item" v-for="(item, index) in daliyDataList"
            :key="item.id">
            <template v-slot:body>
              <view class="combo-item-wrapper" @click="() => handleTo('daily')">
                <daliy-combo-item :item="item" />
              </view>
            </template>
          </uni-list-item>
        </uni-list>
      </view>

      <view class="empty-wrapper" v-if="expoDataList.length === 0 && daliyDataList.length === 0">
        <empty-list>
          <text>该用户暂无{{ typeMap[type] || '' }}服务</text>
        </empty-list>
      </view>
    </view>
  </z-paging>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { useStore } from 'vuex'
import { getOtherUserInfo, getOtherUserServices } from '@/api/message'
import DaliyComboItem from '../service-create/components/daliy-combo-item.vue'
import ExpoComboItem from './expo-combo-item.vue'

const store = useStore();

const typeMap = {
  'photography': '约拍',
  'makeup': '约妆',
}
const type = ref('')
const otherUserId = ref('')
const otherUserInfo = ref({})

const daliyServiceId = ref('')
const daliyDataList = ref([])
const expoDataList = ref([])

onLoad((options) => {
  type.value = options.type
  otherUserId.value = options.id
  fetchOtherUserInfo()
  fetchOtherUserServices()
})

function fetchOtherUserInfo() {
  getOtherUserInfo(otherUserId.value).then(res => {
    if (res.code === 20000) {
      otherUserInfo.value = res.data.userinfo
      otherUserInfo.value.tags = ['化妆', '摄影', '化妆师', '摄影师']
    }
  })
}

function fetchOtherUserServices() {
  const categoriesMap = {
    'photography': '1',
    'makeup': '2',
  }
  getOtherUserServices(otherUserId.value, { categories: categoriesMap[type.value] }).then(res => {
    if (res.code === 20000) {
      const list = res.data.results
      const scoreList = []
      let daliyItems = []
      list.forEach(item => {
        if (!item.is_daily) {
          expoDataList.value.push(item)
        } else {
          daliyItems = daliyItems.concat(item.items)
          daliyServiceId.value = item.id
        }
        scoreList.push(item.score)
      })
      daliyDataList.value = daliyItems
      otherUserInfo.value.score = scoreList.reduce((a, b) => a + b, 0) / scoreList.length
    }
  })
}

function prefixFilePath(url) {
  if (!url) return '';
  return store.state.common.cloudFileUrl + url;
}

function handleTo(type, id) {
  let url = ''
  if (type === 'expo') {
    const item = expoDataList.value.find(item => item.id === id)

    url = `/pages/packageA/other-home-service/expo-service-detail?id=${otherUserId.value}&type=${type}&serviceId=${id}&expoId=${item.expo_id}`
  }
  if (type === 'daily') {
    url = `/pages/packageA/other-home-service/daily-service-detail?id=${otherUserId.value}&type=${type}&serviceId=${daliyServiceId.value}`
  }
  url && uni.navigateTo({
    url
  })
}
</script>

<style scoped lang="scss">
.content {
  flex: 1;

  .user-info {
    margin-top: 32rpx;
    display: flex;
    gap: 12rpx;
    box-sizing: border-box;
    padding: 0 24rpx;

    .user-avatar {
      width: 56rpx;
      height: 56rpx;
      border-radius: 50%;
    }

    .detail {
      display: flex;
      flex-direction: column;
      gap: 20rpx;

      .name {
        font-weight: 700;
        font-size: 32rpx;
        color: #3D3D3D;
      }

      .rate-wrapper {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #FE58B7;
        gap: 8rpx;
      }

      .label-wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 8rpx;

        .label-item {
          padding: 0 16rpx;
          height: 40rpx;
          background: #F7F8FA;
          border-radius: 8rpx 8rpx 8rpx 8rpx;
          font-size: 24rpx;
          color: #1F1F1F;
        }
      }
    }

  }

  .divide-horizontal {
    width: 100%;
    height: 2rpx;
    background-color: #D8D8D8;
    margin: 32rpx 0;
  }

  .service-wrapper {
    box-sizing: border-box;
    padding: 0 24rpx;
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    margin-top: 32rpx;

    .title {
      font-weight: 700;
      font-size: 32rpx;
      color: #3D3D3D;
    }

    .combo-item-wrapper {
      width: 100%;
    }
  }

  .empty-wrapper {
    margin-top: 32rpx;
    padding: 0 24rpx;
  }
}
</style>
