<template>
  <view class="demo-container">
    <view class="demo-header">
      <text class="demo-title">城市选择组件示例</text>
    </view>
    
    <!-- 选择结果显示 -->
    <view class="result-section">
      <view class="result-title">选择结果：</view>
      <view class="result-content">
        <text v-if="selectedLocation.province.name">
          {{ selectedLocation.province.name }}
        </text>
        <text v-if="selectedLocation.city.name">
          - {{ selectedLocation.city.name }}
        </text>
        <text v-if="selectedLocation.district.name">
          - {{ selectedLocation.district.name }}
        </text>
        <text v-if="!selectedLocation.province.name" class="placeholder">
          请选择地区
        </text>
      </view>
    </view>
    
    <!-- 模式选择 -->
    <view class="mode-section">
      <view class="mode-title">选择模式：</view>
      <view class="mode-buttons">
        <button 
          class="mode-btn" 
          :class="{ active: currentMode === 'province' }"
          @click="changeMode('province')"
        >
          仅省份
        </button>
        <button 
          class="mode-btn" 
          :class="{ active: currentMode === 'province-city' }"
          @click="changeMode('province-city')"
        >
          省市
        </button>
        <button 
          class="mode-btn" 
          :class="{ active: currentMode === 'province-city-district' }"
          @click="changeMode('province-city-district')"
        >
          省市区
        </button>
      </view>
    </view>
    
    <!-- 城市选择组件 -->
    <city-select 
      :mode="currentMode"
      :value="selectedLocation"
      :custom-nav-height="customNavHeight"
      @change="handleLocationChange"
    />
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import CitySelect from './city-select.vue'

// 当前选择模式
const currentMode = ref('province-city-district')

// 选中的位置信息
const selectedLocation = ref({
  province: {},
  city: {},
  district: {}
})

// 自定义导航栏高度（如果有自定义导航栏）
const customNavHeight = computed(() => {
  // 这里可以根据实际情况设置导航栏高度
  return 0
})

// 切换选择模式
function changeMode(mode) {
  currentMode.value = mode
  // 清空选择结果
  selectedLocation.value = {
    province: {},
    city: {},
    district: {}
  }
}

// 处理位置选择变化
function handleLocationChange(location) {
  selectedLocation.value = location
  console.log('选择的位置:', location)
  
  // 这里可以根据需要进行其他处理
  // 比如保存到本地存储、发送到服务器等
}
</script>

<style lang="scss" scoped>
.demo-container {
  height: 100vh;
  background-color: #f5f5f5;
}

.demo-header {
  padding: 40rpx 32rpx 20rpx;
  background-color: #fff;
  border-bottom: 1px solid #ebeef5;
  
  .demo-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #303133;
  }
}

.result-section {
  padding: 32rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  
  .result-title {
    font-size: 28rpx;
    color: #606266;
    margin-bottom: 16rpx;
  }
  
  .result-content {
    font-size: 32rpx;
    color: #303133;
    
    .placeholder {
      color: #c0c4cc;
    }
  }
}

.mode-section {
  padding: 32rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
  
  .mode-title {
    font-size: 28rpx;
    color: #606266;
    margin-bottom: 24rpx;
  }
  
  .mode-buttons {
    display: flex;
    gap: 20rpx;
    
    .mode-btn {
      flex: 1;
      padding: 20rpx;
      font-size: 26rpx;
      color: #606266;
      background-color: #f5f7fa;
      border: 1px solid #dcdfe6;
      border-radius: 8rpx;
      
      &.active {
        color: #409eff;
        background-color: #ecf5ff;
        border-color: #b3d8ff;
      }
    }
  }
}
</style>
