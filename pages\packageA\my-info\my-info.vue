<template>
	<view class="app-container">
		<Navbar :type="1" leftText="编辑资料" :goBackTier="0" @onBack="goBack" leftWidth="400rpx"></Navbar>
		<view class="entrance-container">
			<dust-crop-image ref="headerCrop" :width="300" :height="300" :radius="150" @crop="(e) => handleCrop(e, 'avatar')">
				<template #trigger>
					<view class="header" @tap="showBackgroundHandle">
						<dust-image-base :src="formData.avatar" :local="false" aspectRatio="1:1"
							wrapperRadius="50%"></dust-image-base>
						<image class="edit-icon" src="../static/myinfo/<EMAIL>"></image>
					</view>
				</template>
			</dust-crop-image>
			<view class="entrance-content">
				<view class="entrance-item" @click="() => popupOpen('nickname')">
					<text class="label">昵称</text>
					<view class="value-wrapper">
						<text class="value textHide">{{ formData.nickname }}</text>
						<image class="right-icon" src="../static/myinfo/<EMAIL>"></image>
					</view>
				</view>
				<view class="entrance-item" @click="() => popupOpen('sex')">
					<text class="label">性别</text>
					<view class="value-wrapper">
						<text class="value">{{ formatSexText(formData.sex) || '保密' }}</text>
						<image class="right-icon" src="../static/myinfo/<EMAIL>"></image>
					</view>
				</view>
				<view class="entrance-item" @click="() => popupOpen('birthday')">
					<text class="label">出生日期</text>
					<view class="value-wrapper">
						<text class="value">{{ formData?.birthday ? formatTimestampToDate(formData.birthday) : '选择生日' }}</text>
						<image class="right-icon" src="../static/myinfo/<EMAIL>"></image>
					</view>
				</view>
				<dust-location-picker mode="province-city-district" @confirm="saveArea">
					<template #trigger>
						<view class="entrance-item">
							<text class="label">地区</text>
							<view class="value-wrapper">
								<text class="value">{{ formData.area || '选择地区' }}</text>
								<image class="right-icon" src="../static/myinfo/<EMAIL>"></image>
							</view>
						</view>
					</template>
				</dust-location-picker>
				<view class="entrance-item" @click="handleToEditBio">
					<text class="label">简介信息</text>
					<view class="value-wrapper">
						<text class="value textHide">{{ formData.bio || '' }}</text>
						<image class="right-icon" src="../static/myinfo/<EMAIL>"></image>
					</view>
				</view>
				<dust-crop-image ref="backgroundCrop" :width="1080" :height="1920" @crop="(e) => handleCrop(e, 'bgImg')">
					<template #trigger>
						<view class="entrance-item" @tap="showBackgroundHandle">
							<text class="label">背景图</text>
							<view class="value-wrapper">
								<image class="right-icon" src="../static/myinfo/<EMAIL>"></image>
							</view>
						</view>
					</template>
				</dust-crop-image>
			</view>
		</view>
	</view>

	<uv-popup ref="popup" mode="bottom" safeAreaInsetBottom bgColor="none" @change="popupChange">
		<view class="popup-wrapper">
			<template v-if="popupType === 'nickname'">
				<view class="header">
					<text @click="popup.close">取消</text>
					<text>修改昵称</text>
					<text class="save-btn" @click="saveNickname">保存</text>
				</view>
				<view class="input-wrapper">
					<input type="text" v-model="formData.inputNickname">
					<text class="input-limit-text">{{ formData.inputNickname.length }}/24</text>
				</view>
			</template>
			<template v-else-if="popupType === 'sex'">
				<view class="header">
					<text @click="popup.close">取消</text>
					<text>修改性别</text>
					<text class="save-btn" @click="saveSex">保存</text>
				</view>
				<view class="sex-selector-wrapper">
					<view class="sex-selector-item" :class="{ active: item.value === formData.inputSex }" v-for="item in sexList"
						:key="item.name + 'edit-myinfo'" @click="() => { formData.inputSex = item.value }">
						<text>{{ item.name }}</text>
						<image class="selection-icon" src="/static/icon/<EMAIL>" v-if="item.value === formData.inputSex">
						</image>
					</view>
				</view>
			</template>
			<template v-else-if="popupType === 'birthday'">
				<view class="header">
					<text @click="popup.close">取消</text>
					<text>修改生日</text>
					<text class="save-btn" @click="saveBirthday">保存</text>
				</view>
				<date-picker @change="changeBirthday"></date-picker>
			</template>
		</view>
	</uv-popup>
</template>

<script setup>
import {
	ref,
	computed,
	onMounted,
	reactive,
	watch,
} from 'vue'
import {
	useStore
} from 'vuex';
import {
	filterEmote,
	formatTimestampToDate,
} from '@/common/utils'
import {
	updateUserInfo,
	myInviteCode,
	myQRInviteFriendCode,
} from '@/api/user'
import {
	uploadFile
} from '@/api/request'
import Config from '@/common/config.js'
import DatePicker from './components/birthday-picker.vue'

const store = useStore();
const userInfo = computed(() => store.state.userInfo)

const sexList = [
	{
		name: '男',
		value: 1,
	},
	{
		name: '女',
		value: 2,
	},
	{
		name: '保密',
		value: 3,
	}
]

const popup = ref(null)
const popupType = ref('')
const formData = reactive({
	avatar: userInfo.value.avatar,
	inputNickname: userInfo.value.nickname,
	nickname: userInfo.value.nickname,
	sex: userInfo.value.gender,
	inputSex: userInfo.value.gender || 3,
	birthday: userInfo.value.birthday,
	inputBirthday: userInfo.value.birthday,
	area: '',
	inputArea: '',
	bio: userInfo.value.bio,
})
let inviteCode = ref(null);

onMounted(() => {
	console.log(userInfo.value)
	showYQ()
	formatAreaText()
})

// 是否要显示背景
let showBackground = false;
const headerCrop = ref(null);
const backgroundCrop = ref(null);
// 返回按钮拉结
const goBack = () => {
	if (showBackground) {
		headerCrop.value && headerCrop.value.closeCropper();
		backgroundCrop.value && backgroundCrop.value.closeCropper();
	} else {
		uni.navigateBack()
	}
}

uni.$on('myinfo-refresh-formData-bio', (value) => {
	formData.bio = value
})

function showYQ() {
	myInviteCode().then((res) => {
		if (res.code == 20000) {
			inviteCode.value = res.data.invite_code;
		}
	});
}

function showBackgroundHandle() {
	showBackground = true;
}
function handleCrop(e, type) {
	showBackground = false;
	if (!e) return;
	const imagePath = e.tempFilePath
	saveAvatarOrBgImg(type, imagePath)
}

function formatAreaText() {
	const { province, city, district } = userInfo.value
	if (city) {
		formData.area = city.name
		if (district) {
			formData.area = city.name + ' - ' + district.name
		}
	} else {
		formData.area = ''
	}
}

function formatSexText(value) {
	if (value === 1) {
		return '男'
	} else if (value === 2) {
		return '女'
	} else {
		return '保密'
	}
}

function popupOpen(type) {
	popupType.value = type
	popup.value.open()
}

function popupChange(e) {
	if (!e.show) {
		formData.inputNickname = formData.nickname
		popupType.value = ''
	}
}

function changeBirthday(date) {
	formData.inputBirthday = date
}

function saveBirthday() {
	if (formData.inputBirthday === formData.birthday) {
		popup.value.close()
		return
	}
	updateUserInfo({
		// ...userInfo.value,
		birthday: new Date(formData.inputBirthday).getTime()
	}).then(res => {
		if (res.code === 20000) {
			store.commit('userInfo/UPDATE_USER_INFO', {
				birthday: new Date(formData.inputBirthday).getTime()
			})
			formData.birthday = new Date(formData.inputBirthday).getTime()
			popup.value.close()
		}
	})
}

function saveNickname() {
	if (formData.inputNickname.length > 24) {
		uni.showToast({
			title: '昵称不能超过24个字符',
			icon: 'none'
		})
		return
	}
	if (formData.inputNickname === formData.nickname) {
		popup.value.close()
		return
	}
	updateUserInfo({
		// ...userInfo.value,
		nickname: formData.inputNickname
	}).then(res => {
		if (res.code === 20000) {
			getNewQrCode()
			formData.nickname = formData.inputNickname
			popup.value.close()
		}
	})
}

function saveSex() {
	if (formData.sex === formData.inputSex) {
		popup.value.close()
		return
	}
	updateUserInfo({
		// ...userInfo.value,
		gender: formData.inputSex
	}).then(res => {
		if (res.code === 20000) {
			store.commit('userInfo/UPDATE_USER_INFO', {
				gender: formData.inputSex
			})
			formData.sex = formData.inputSex
			popup.value.close()
		}
	})
}

function saveAvatarOrBgImg(type, imagePath) {
	uploadFile(imagePath).then(res => {
		if (res.code === 20000) {
			type === 'avatar' ? formData.avatar = res.data.url : formData.bgImg = res.data.url
			const data = {}
			if (type === 'avatar') {
				data.avatar = res.data.url
			} else {
				data.background_url = res.data.url
			}
			updateUserInfo(data).then(res => {
				if (res.code === 20000) {
					if (type === 'avatar') {
						getNewQrCode()
						return
					}
					store.dispatch('userInfo/getUserInfo');
				}
			})
		}
	})
}

function getNewQrCode() {
	const params = {
		page: 'pages/tabbar/home/<USER>',
		scene: `inviteCode=${inviteCode.value}`,
		check_path: false,
		env_version: Config.envVersion
	};
	
	myQRInviteFriendCode(params, true)
		.then(res => {
			store.dispatch('userInfo/getUserInfo');
		})
		.catch(err => {
			console.error('生成二维码失败:', err);
			uni.showToast({
				title: '生成二维码失败',
				icon: 'none'
			});
		});
}

function saveArea(data) {
	const [province, city, district] = data
	const params = {
		city: city.id,
		province: province.id,
	}
	if (district) {
		params.district = district.id
	}
	updateUserInfo(params).then(res => {
		if (res.code === 20000) {
			store.commit('userInfo/UPDATE_USER_INFO', {
				city: {
					id: city.id,
					name: city.name,
				},
				province: {
					id: province.id,
					name: province.name,
				},
			})
			formData.area = city.name

			if (district) {
				store.commit('userInfo/UPDATE_USER_INFO', {
					district: {
						id: district.id,
						name: district.name,
					},
				})
				formData.area = city.name + ' - ' + district.name
			}
		}
	})
	popup.value.close()
}

function handleToEditBio() {
	uni.navigateTo({
		url: '/pages/packageA/my-info/edit-bio?bio=' + formData.bio
	})
}

</script>

<style lang="scss" scoped>
.entrance-container {
	margin-top: 48rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 56rpx;
	box-sizing: border-box;
	padding: 0 32rpx;

	.header {
		width: 112rpx;
		height: 112rpx;
		border: 2rpx solid #48DAEA;
		border-radius: 50%;
		position: relative;

		.edit-icon {
			width: 48rpx;
			height: 48rpx;
			position: absolute;
			bottom: 0;
			left: 50%;
			transform: translateX(-50%) translateY(50%);
		}
	}

	.entrance-content {
		width: 100%;
		background: #F7F8FA;
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		box-sizing: border-box;
		padding: 40rpx 28rpx;
		display: flex;
		flex-direction: column;
		gap: 56rpx;

		.entrance-item {
			flex: 1;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.label {
				font-size: 36rpx;
				color: #1F1F1F;
				line-height: 36rpx;
				flex-shrink: 0;
			}

			.value-wrapper {
				width: 60%;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				gap: 16rpx;
				font-size: 36rpx;
				color: #85878D;
				line-height: 36rpx;
				text-align: right;

				.right-icon {
					flex-shrink: 0;
					width: 40rpx;
					height: 40rpx;
				}
			}
		}
	}
}

.popup-wrapper {
	width: 100%;
	height: fit-content;
	background: #FFFFFF;
	border-radius: 32rpx 32rpx 0 0;
	box-sizing: border-box;
	padding: 32rpx;

	.header {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 32rpx;
		color: #1F1F1F;
		margin-bottom: 56rpx;

		.save-btn {
			color: #FE58B7;
		}
	}

	.input-wrapper {
		width: 100%;
		display: flex;
		align-items: center;
		gap: 24rpx;
		padding-bottom: 32rpx;

		>input {
			flex: 1;
			height: 80rpx;
			background: #F7F8FA;
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			box-sizing: border-box;
			padding: 0 32rpx;
		}

		.input-limit-text {
			flex-shrink: 0;
			font-weight: 400;
			font-size: 36rpx;
			color: #85878D;
		}
	}

	.sex-selector-wrapper {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 24rpx;

		.sex-selector-item {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-bottom: 24rpx;
			border-bottom: 2rpx solid #EEEEEE;
			font-size: 32rpx;
			color: #1F1F1F;

			&:last-child {
				border: none;
			}

			&.active {
				color: #FE58B7;
			}

			.selection-icon {
				width: 40rpx;
				height: 40rpx;
			}
		}
	}
}
</style>