<template>
	<z-paging class="app-container" ref="paging" v-model="dataList" @query="queryList" :safe-area-inset-bottom="true" :use-safe-area-placeholder="true">
		<Navbar :type="1" :showSearch="true" placeholder="" :leftText="typeSwitch(info.status)"></Navbar>
		<view class="top allPidding">
			<text>订单信息</text>
			<view>
				<image src="../../../static/icon/<EMAIL>" mode="aspectFill"></image>
				<text>客服</text>
			</view>
		</view>
		<view class="listBox allPidding">
			<view class="listBoxBorder allPidding">
				<view class="listBoxCenter">
					<view class="imageView">
						<image :src="prefixFilePath(info.snapshot.cover)" mode="aspectFit"></image>
					</view>
					<view class="listBoxCenterRight">
						<text>{{ info.snapshot.name }}</text>
						<view>
							<text>
								<text class="price">￥</text>
								<text>{{ info.amount / 100 }}</text>
							</text>
						</view>
					</view>
				</view>
				<view class="listBoxBottom">
					<image :src="prefixFilePath(info.vendor.avatar)" mode="aspectFill" v-if="orderType == 'buy'" @tap="handleToUserMine(info.vendor)"></image>
					<image :src="prefixFilePath(info.user.avatar)" mode="aspectFill" v-else @tap="handleToUserMine(info.user)"></image>
					<text v-if="orderType == 'buy'" @tap="handleToUserMine(info.vendor)">{{ info.vendor.nickname }}</text>
					<text v-else @tap="handleToUserMine(info.user)">{{ info.user.nickname }}</text>
					<image src="../../../static/my/<EMAIL>" mode="aspectFill" class="sex" v-if="info.vendor.sex == 1 && orderType == 'buy'"></image>
					<image src="../../../static/my/<EMAIL>" mode="aspectFill" class="sex" v-if="info.vendor.sex == 2 && orderType == 'buy'"></image>
					<image src="../../../static/my/<EMAIL>" mode="aspectFill" class="sex" v-if="info.user.sex == 1 && orderType == 'sell'"></image>
					<image src="../../../static/my/<EMAIL>" mode="aspectFill" class="sex" v-if="info.user.sex == 2 && orderType == 'sell'"></image>
				</view>
			</view>
		</view>
		<view class="listComment allPidding" v-if="orderType != 'sell' && info.status == 8 && !info.has_review" @click="toComment(info.id)">
			<view class="allPidding" style="padding-top: 28rpx; padding-bottom: 28rpx">
				<text>评价下，服务的如何</text>
			</view>
		</view>
		<view class="listBody allPidding">
			<view class="listBorder"></view>
			<view class="listBodyList">
				<view><text>实付金额</text></view>
				<text>¥{{ info.amount / 100 }}</text>
			</view>
			<view class="listBodyList">
				<view><text>订单编号</text></view>
				<text>{{ info.number }}</text>
			</view>
			<view class="listBodyList">
				<view><text>使用情况</text></view>
				<text style="color: #48daea">{{ typeSwitch(info.status) }}</text>
			</view>
			<view class="listBodyList">
				<view><text>商家保证金</text></view>
				<text>{{ info.bond }}%</text>
			</view>
			<view class="listBodyList">
				<view><text>用户名称</text></view>
				<text>{{ info.user.nickname }}</text>
			</view>
			<view class="listBodyList">
				<view><text>服务时间</text></view>
				<text>{{ timeToString(info.appointment_at, 2) }}</text>
			</view>
			<view class="listBodyList">
				<view><text>下单时间</text></view>
				<text>{{ timeToString(info.snapshot.created_at) }}</text>
			</view>
			<view class="listBodyList" v-if="info.finished_at">
				<view><text>完成时间</text></view>
				<text>{{ timeToString(info.finished_at) }}</text>
			</view>
			<view class="listBorder"></view>
		</view>
		<view style="height: 104rpx"></view>
		<view class="bottom">
			<view class="redbutton" v-if="(info.status == 1 || info.status == 256) && orderType == 'buy'" @click="topay">立即支付</view>
			<view class="redbutton" v-if="info.status == 4 && orderType == 'sell'" @click="execute">立即执行</view>
			<view class="redbutton" v-if="info.status == 2 && orderType == 'buy'" @click="use">去使用</view>
			<view class="fffbutton" v-if="(info.status == 2 || info.status == 4) && orderType == 'sell'" @click="cancel">取消订单</view>
			<view class="fffbutton" v-if="info.status == 2 && orderType == 'buy'" @click="userDefault">取消订单</view>
		</view>
		<view style="height: 42rpx; width: 100%"></view>
	</z-paging>
</template>

<script setup>
import { getMineBalance } from '../../../api/wallet';
import { payOrder, orderInfo, cancelOrder, executeOrder, userCancelOrder, userExecuteOrder, userDefaultCancelOrder } from '../../../api/order';
import { ref, computed, getCurrentInstance } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import { useStore } from 'vuex';
import { timeToString } from '@/common/utils';
const instance = getCurrentInstance().proxy;
const eventChannel = instance.getOpenerEventChannel();
const store = useStore();
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);
function prefixFilePath(url) {
	if (!url) return '';
	return cloudFileUrl.value + url;
}
let id = ref(null);
// 订单类型：商家 / 用户
let orderType = ref(null);
let title = ref('');
const paging = ref(null);
const index = ref(null);
onLoad((e) => {
	id.value = e.id;
	orderType.value = e.type;
	console.log(orderType.value, 111);
	index.value = e.index;
	getInfo();
});
let info = ref({});
function getInfo() {
	orderInfo(id.value, orderType.value).then((res) => {
		res.data.snapshot = JSON.parse(res.data.snapshot);
		info.value = res.data;
		console.log(info.value);
	});
}
function toComment(id) {
	uni.navigateTo({
		url: `/pages/packageA/orderComment/orderComment?id=${id}&index=${index.value}`,
		events: {
			// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
			detailsComment: function (data) {
				info.has_review = data.has_review;
				eventChannel.emit('orderDeatilsComment', data);
			}
		}
	});
}
function typeSwitch(type) {
	let txt = '';
	switch (type) {
		case 1:
			txt = '待支付';
			break;
		case 2:
			txt = '待完成';
			break;
		case 4:
			txt = '已使用';
			break;
		case 8:
			txt = '已完成';
			break;
		case 16:
			txt = '已取消';
			break;
		case 32:
			txt = '已弃用';
			break;
		case 64:
			txt = '用户违约';
			break;
		case 128:
			txt = '商家违约';
			break;
		case 256:
			txt = '待支付';
			break;
		case 512:
			txt = '订单超时';
			break;
		default:
			break;
	}
	return txt;
}
async function MineBalance() {
	return getMineBalance().then(async (res) => {
		return res.data.balance - res.data.freeze;
	});
}
async function topay() {
	uni.showModal({
		title: '提示',
		content: `是否支付订单金额${info.value.amount / 100}元`,
		success: async function (r) {
			if (r.confirm) {
				let d = {
					password: '8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92'
				};
				let balance = await MineBalance();
				if (balance / 100 < info.value.amount / 100) {
					//平台支付
					d.pay_method = 2;
					const code = await getLoginCode();
					d.code = code;
					payOrder(info.value.id, d).then((res) => {
						uni.requestPayment({
							provider: 'wxpay',
							orderInfo: res.data.wx,
							timeStamp: res.data.wx.timeStamp,
							nonceStr: res.data.wx.nonceStr,
							package: res.data.wx.package,
							signType: res.data.wx.signType,
							paySign: res.data.wx.paySign,
							success: async (res) => {
								if (res.errMsg === 'requestPayment:ok') {
									uni.showToast({
										title: '支付成功',
										duration: 2000
									});
									getInfo();
									MineBalance();
									eventChannel.emit('orderDetailsType', {
										status: 2
									});
								}
							},
							fail: (err) => {
								console.log(err, 'fail');
							}
						});
					});
				} else {
					//余额支付
					d.pay_method = 1;
					payOrder(info.value.id, d).then((res) => {
						if (res.code == 20000) {
							uni.showToast({
								title: '支付成功',
								duration: 2000
							});
							// valueInit();
							getInfo();
							MineBalance();
						} else {
							uni.showToast({
								title: '支付失败',
								icon: 'error',
								duration: 2000
							});
						}
					});
				}
			}
		}
	});
}
const getLoginCode = () => {
	return new Promise((resolve, reject) => {
		uni.login({
			success: (res) => {
				if (!res.code) {
					reject(res);
				}
				resolve(res.code);
			},
			fail: (err) => {
				reject(err);
			}
		});
	});
};

//商家违约
function cancel() {
	uni.showModal({
		title: '提示',
		content: `取消订单将扣除违约金${(info.value.amount * info.value.bond) / 100 / 100}元`,
		success: function (res) {
			if (res.confirm) {
				cancelOrder(info.value.id).then((res2) => {
					if (res2.code == 20000) {
						uni.showToast({
							title: '订单已取消',
							duration: 2000
						});
						getInfo();
						eventChannel.emit('orderDetailsType', {
							status: 128
						});
					}
				});
			}
		}
	});
}
//用户违约
function userDefault() {
	uni.showModal({
		title: '提示',
		content: `取消订单将扣除违约金${(info.value.amount * info.value.bond) / 100 / 100}元`,
		success: function (res) {
			if (res.confirm) {
				userDefaultCancelOrder(info.value.id).then((res2) => {
					if (res2.code == 20000) {
						uni.showToast({
							title: '订单已取消，订单金额将在7个工作日，自动退款',
							icon: 'none',
							duration: 2000
						});
						getInfo();
						eventChannel.emit('orderDetailsType', {
							status: 64
						});
					}
				});
			}
		}
	});
}

//商家执行
function execute() {
	if (isTime()) {
		uni.showModal({
			title: '提示',
			content: `请确认已完成服务`,
			success: function (res) {
				if (res.confirm) {
					executeOrder(info.value.id).then((res2) => {
						if (res2.code == 20000) {
							getInfo();
							eventChannel.emit('orderDetailsType', {
								status: 8
							});
						}
					});
				}
			}
		});
	} else {
		uni.showToast({
			title: '当前还未到服务时间',
			icon: 'none',
			duration: 2000
		});
	}
}
//
//用户使用
function use() {
	if (isTime()) {
		uni.showModal({
			title: '提示',
			content: `请确认商家已为您完成了服务`,
			success: function (res) {
				if (res.confirm) {
					userExecuteOrder(info.value.id).then((res2) => {
						if (res2.code == 20000) {
							getInfo();
							eventChannel.emit('orderDetailsType', {
								status: 4
							});
						}
					});
				}
			}
		});
	} else {
		uni.showToast({
			title: '当前还未到服务时间',
			icon: 'none',
			duration: 2000
		});
	}
}
//判断时间
function isTime() {
	return new Date().getTime() > info.value.appointment_at;
}
function handleToUserMine(item) {
	uni.navigateTo({
		url: '/pages/home-pages/user-mine?id=' + item.id,
	})
}
</script>

<style lang="scss" scoped>
.allPidding {
	padding-left: 24rpx;
	padding-right: 24rpx;
}
.top {
	display: flex;
	justify-content: space-between;
	margin-top: 22rpx;
	text {
		font-size: 32rpx;
		color: #1f1f1f;
	}
	view {
		display: flex;
		flex-direction: column;
		align-items: center;
		image {
			height: 32rpx;
			width: 32rpx;
		}
		text {
			font-size: 24rpx;
			color: #85878d;
		}
	}
}
.listComment {
	view {
		height: 276rpx;
		background: #f7f8fa;
		border-radius: 24rpx;
		margin-top: 22rpx;
		font-size: 28rpx;
		color: #aaaaaa;
	}
}
.listBox {
	margin-top: 24rpx;
	.listBoxBorder {
		background-color: #f7f8fa;
	}
	.listBoxCenter {
		height: 252rpx;
		padding-top: 24rpx;
		display: flex;
		border-bottom: 2rpx solid #eeeeee;
		.imageView {
			background-color: #000;
			width: 208rpx;
			height: 208rpx;
			border-radius: 20rpx;
			margin-right: 32rpx;
			overflow: hidden;
			image {
				width: 208rpx;
				height: 208rpx;
			}
		}
		.listBoxCenterRight {
			width: 414rpx;
			height: 208rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			text:nth-child(1) {
				font-size: 32rpx;
				color: #3d3d3d;
			}
			view {
				font-size: 24rpx;
				display: flex;
				margin-top: 20rpx;
				text {
					color: #fe58b7;
					font-size: 36rpx;
				}
				.price {
					color: #fe58b7 !important;
					font-size: 24rpx;
				}
			}
		}
	}
	.listBoxBottom {
		height: 100rpx;
		display: flex;
		align-items: center;
		image {
			width: 64rpx;
			height: 64rpx;
			border-radius: 50%;
			border: 4rpx solid rgba(255, 255, 255, 0.7);
			margin-right: 16rpx;
		}
		text {
			font-size: 32rpx;
			color: #1f1f1f;
		}
		.sex {
			width: 48rpx;
			height: 48rpx;
			margin-left: 8rpx;
		}
	}
}
.listBody {
	margin-top: 24rpx;
	border-radius: 24rpx;
	.listBorder {
		height: 20rpx;
		width: 100%;
		background-color: #f7f8fa;
	}
	.listBodyList {
		padding-left: 32rpx;
		background-color: #f7f8fa;
		font-size: 28rpx;
		display: flex;
		line-height: 56rpx;
		view {
			width: 140rpx;
			color: #3d3d3d;
			text-align: right;
			margin-right: 56rpx;
		}
		text:nth-child(2) {
			color: #85878d;
		}
	}
}
.bottom {
	width: 100%;
	// position: fixed;
	// bottom: 42rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	view {
		width: 702rpx;
		height: 96rpx;
		background: #fe58b7;
		border-radius: 48rpx;
		color: #fff;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	view:nth-child(2) {
		margin-top: 32rpx;
		color: #000;
		background-color: #eeeeee;
	}
}
</style>
