<template>
  <view class="wrapper" :style="{ paddingBottom: keyboardHeight + 'px' }">
    <bottom-comment-input :auto-focus="true" :placeholder="placeholder" @keyboardHeightChange="keyboardHeightChange"
      @onSubmit="handleSubmitComment" />
  </view>

</template>

<script setup>
import { computed, ref } from 'vue'
import {
	publishAnnouncementComment,
} from "@/api/announcement";

const props = defineProps({
  replyComment: {
    type: Object,
    default: () => { }
  },
  replyParentId: {
    type: String,
    default: null
  },
  expoId: {
    type: [String, Number],
    required: true
  }
})

const emit = defineEmits(['submitCb', 'refreshComments'])

const placeholder = computed(() => {
  return `回复 @${props.replyComment.user_nickname}: `
})

const keyboardHeight = ref(0)

const keyboardHeightChange = (height) => {
  keyboardHeight.value = height
}

const handleSubmitComment = async (comment) => {
  // comment: { image: '', text: '' }
  const commitComment = {
    ...comment,
  }

  if (props.replyParentId) {
    commitComment.text = `${placeholder.value}${comment.text}`
  }

  try {
    const res = await publishAnnouncementComment(props.expoId, {
      editor: 'json',
      content: JSON.stringify(commitComment),
      parent: props.replyParentId ? props.replyParentId : props.replyComment.id
    })
    if (res.code === 20000) {
      // 通知父组件刷新评论列表
      if (props.replyParentId) {
        // 如果是子评论回复到父评论，则只刷新该父评论的子评论
        emit('refreshComments', {
          type: 'reply',
          parentId: props.replyParentId
        })
      } else {
        // 如果是直接回复根评论，则刷新整个评论列表
        emit('refreshComments', {
          type: 'all'
        })
      }
      emit('submitCb')
    }
  } catch (error) {
    console.log(error)
  }
}

</script>

<style lang="scss" scoped>
.wrapper {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;

}
</style>
