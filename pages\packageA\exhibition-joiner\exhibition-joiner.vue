<template>
  <z-paging class="app-container">
    <template #top>
      <Navbar :type="1" leftText="参加人员"></Navbar>
      <view class="divider"></view>
    </template>
    <view class="container">
      <view class="people-item" v-for="item in detail?.joinins" :key="item.id">
        <image class="avatar" :src="prefixFilePath(item.avatar)" @tap="handleToUserMine(item)"></image>
        <text class="name">{{ item.nickname || 'unknow' }}</text>
      </view>
    </view>
  </z-paging>
</template>

<script setup>
import { useStore } from 'vuex'
import { computed } from 'vue';

const store = useStore()
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

const expoDetailState = computed(() => store.state.expoDetail)
const detail = computed(() => expoDetailState.value.expoDetail)

function prefixFilePath(path) {
  if (!path) return ''
  return path ? cloudFileUrl.value + path : ''
}
function handleToUserMine(item) {
	uni.navigateTo({
		url: '/pages/home-pages/user-mine?id=' + item.id,
	})
}
</script>

<style scoped lang='scss'>
.divider {
  width: 100%;
  height: 2rpx;
  background-color: #eee;
  margin-bottom: 40rpx;
}
.container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  align-items: start;
  gap: 32rpx;
  box-sizing: border-box;
  padding: 0 32rpx;

  .people-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    gap: 16rpx;
    font-size: 28rpx;
    color: #1F1F1F;
    line-height: 28rpx;

    .avatar {
      width: 112rpx;
      height: 112rpx;
      border-radius: 50%;
    }

    .name {
      width: 100%;
      text-align: center;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-word;
    }
  }
}
</style>