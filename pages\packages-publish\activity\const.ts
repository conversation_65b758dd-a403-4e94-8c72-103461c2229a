// 发布-活动-服务项被选择时出发全局事件
export const PUBLISH_ACTIVITY_SERVICE_SELECT = "publish-avtivity-service-select";
export const PUBLISH_ACTIVITY_ADDRESS_SELECT = "publish-avtivity-address-select";
export const PUBLISH_ACTIVITY_TYPES_SELECTE = "publish-avtivity-types-select";

// 地址服务中列表数据
export interface AddressItem {
	title: string;			 		// 标题
	address: string;					// 详情
	selected: boolean;				// 是否选中
	latitude: number,				// 维度
	longitude: number;				// 精度
	adcode: number;					// 行政区编码 共6个 以省市区各两个拼接
	distance: string;				// 距离
	// 根据adcode请求接口转换的地址
	province?: number,
	city?: number,
	district?: number,
}
export interface SearchResponse {
	address: string;
	category: string;
	tel: string
	title: string;
	_distance: number
	id: string;
	location: {
		lat: number;
		lng: number;
	};
	ad_info: {
		adcode: number;
		province: string;
		city: string;
		district: string;
	};
}