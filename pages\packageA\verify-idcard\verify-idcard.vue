<template>
  <view class="app-container">
    <Navbar :type="1" leftWidth="240rpx" leftText="实名认证"></Navbar>

    <view class="wrapper">
      <view class="row-wrapper">
        <view class="label">姓名</view>
        <input class="form-item" v-model="form.name" placeholder="姓名" />
      </view>

      <view class="row-wrapper">
        <view class="label">身份证号</view>
        <input class="form-item" v-model="form.cert_no" placeholder="身份证号" />
      </view>

      <view class="row-wrapper">
        <view class="label">身份证类型</view>
        <radio-group @change="radioChange">
          <label class="radio">
            <radio value="1" color="#FE58B7" style="transform:scale(0.7)" checked="true" />长期有效
          </label>
          <label class="radio">
            <radio value="0" color="#FE58B7" style="transform:scale(0.7)" />非长期有效
          </label>
        </radio-group>
      </view>

      <view class="row-wrapper">
        <view class="label">有效期</view>

        <view class="flex items-center date-range">
          <view class="form-item" @click="openPopup('begin')">{{ form.cert_begin_date }}</view>
          <view>-</view>
          <view class="form-item" @click="openPopup('end')">{{ form.cert_end_date }}</view>
        </view>
      </view>
      <view class="btn" @click="verifyIdCard">确定</view>
    </view>
  </view>
  <uv-popup ref="popup" mode="bottom" bgColor="none" :closeOnClickOverlay="true">
    <view class="popup-wrapper">
      <dust-date-picker v-if="popupType === 'begin'" mode="single" :canEarlyToday="true" :showTimeSlot="false" :yearRange="[-60, 30]"
        :dateSlot="form.cert_begin_date" @confirm="popupConfirm"></dust-date-picker>
      <dust-date-picker v-if="popupType === 'end'" mode="single" :canEarlyToday="true" :showTimeSlot="false" :yearRange="[-60, 30]"
        :dateSlot="form.cert_end_date" @confirm="popupConfirm"></dust-date-picker>
    </view>
  </uv-popup>
</template>

<script setup>
import { ref } from 'vue'
import { useStore } from 'vuex'
import { verifyBankCard } from '@/api/wallet'

const store = useStore()
const form = ref({
  cert_no: '',
  name: '',
  cert_validity_type: '1',
  cert_begin_date: '',
  cert_end_date: ''
})
const popup = ref(null)
const popupType = ref('')

const radioChange = (e) => {
  form.value.cert_validity_type = e.detail.value
}

const openPopup = (type) => {
  popupType.value = type
  popup.value.open()
}

const popupConfirm = (e) => {
  const { dateSlot } = e
  if(!dateSlot) return
  if (popupType.value === 'begin') {
    form.value.cert_begin_date = dateSlot.replace(/-/g, '')
  } else {
    form.value.cert_end_date = dateSlot.replace(/-/g, '')
  }
  popup.value.close()
  popupType.value = ''
}

const checkedForm = () => {
  function isIdCardNumber(idCardNumber) {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    return idCardRegex.test(idCardNumber);
  }

  function isTrueDate(date) {
    return date.length === 8
  }

  if (!isIdCardNumber(form.value.cert_no)) {
    uni.showToast({ title: '请输入正确的身份证号', icon: 'none' })
    return false
  }

  if (!isTrueDate(form.value.cert_begin_date) || !isTrueDate(form.value.cert_end_date)) {
    uni.showToast({ title: '请输入正确的有效期', icon: 'none' })
    return false
  }
  return true
}

async function verifyIdCard() {
  if (!checkedForm()) {
    return
  }
  try {
    const p = form.value
    const res = await verifyBankCard(p)
    if (res.code === 20000) {
      store.dispatch('userInfo/getUserInfo')
      uni.navigateBack({
        delta: 1
      })
    }
  } catch (error) {
    console.log(error, 'verifyBankCard')
  }
}
</script>

<style scoped lang='scss'>
.wrapper {
  margin-top: 40rpx;
  flex: 1;
  box-sizing: border-box;
  padding: 0 24rpx;

  .row-wrapper {
    margin-top: 40rpx;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 12rpx;

    .label {
      font-size: 32rpx;
      color: #3D3D3D;
    }

    .date-range {
      gap: 24rpx;
    }

    .form-item {
      width: 100%;
      height: 72rpx;
      background: #F7F8FA;
      border-radius: 16rpx;
      box-sizing: border-box;
      padding: 20rpx 32rpx 20rpx 24rpx;
      font-size: 32rpx;
      color: #3D3D3D;

      .icon {
        width: 32rpx;
        height: 32rpx;
      }

      .prefix {
        margin-right: 24rpx;
      }

      input {
        width: 100%;
      }
    }
  }

  .btn {
    width: 100%;
    height: 96rpx;
    background: #FE58B7;
    border-radius: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36rpx;
    color: #FFFFFF;
    margin-top: 56rpx;
  }
}

.popup-wrapper {
  width: 100%;
  height: fit-content;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
}
</style>