<template>
  <view class="wrapper">
    <view class="row">
      <image class="icon" :src="icon"></image>
      <text class="text">{{text}}</text>
      <image class="icon" src="/static/icon/dimension/right.png"></image>
    </view>
    <view class="divide-line"></view>
    <view class="cancel-btn" @click="onCancel">取消</view>
  </view>
</template>

<script setup>
const emit = defineEmits(['close'])
const props = defineProps({
  text: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    default: '/static/icon/dimension/icon_jb.png'
  }
})

const onCancel = () => {
  emit('close')
}
</script>

<style scoped lang='scss'>
.wrapper {
  width: 750rpx;
  height: 264rpx;
  background: #FFFFFF;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
  display: flex;
  gap: 32rpx;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .row {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 32rpx;
    box-sizing: border-box;

    .icon {
      width: 48rpx;
      height: 48rpx;
    }

    .text {
      flex: 1;
      margin-left: 16rpx;
      font-weight: 400;
      font-size: 32rpx;
      color: #1F1F1F;
      line-height: 32rpx;
    }
  }

  .divide-line {
    width: 100%;
    height: 2rpx;
    background: #F4F4F4;
  }

  .cancel-btn {
    font-weight: 400;
    font-size: 32rpx;
    color: #1F1F1F;
    width: 100%;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>