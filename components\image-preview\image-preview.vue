<template>
	<view class="file-picker__box-content">
		<image class="file-image" :src="url" mode="aspectFit" @click.stop="prviewImage(url)"></image>
		<view v-if="delIcon" class="icon-del-box" @click.stop="delFile(url)">
			<view class="icon-del"></view>
			<view class="icon-del rotate"></view>
		</view>
	</view>
</template>

<script setup lang="ts">

	const props = defineProps({
		delIcon: {
			type: Boolean,
			default: true
		},
		url: {
			type: String,
			require: true
		}
	});
	
	interface Emits {
	  (e: "delete", url: string): void;
	}
	const emits = defineEmits<Emits>();
	
	const prviewImage = function(img: string) {
		if (!img) return;
		uni.previewImage({
			urls: [img],
		});
	};
	const delFile = function(img: string) {
		if (!img) return;
		emits('delete', img)
	};
</script>

<style scoped>
	.file-picker__box-content {
		position: relative;
		display: flex;
		justify-content: stretch;
		align-items: stretch;
		border: 1px #eee solid;
		border-radius: 5px;
		overflow: hidden;
	}
	
	.icon-del-box {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		align-items: center;
		justify-content: center;
		position: absolute;
		top: 3px;
		right: 3px;
		height: 26px;
		width: 26px;
		border-radius: 50%;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 2;
		transform: rotate(-45deg);
	}
	
	.rotate {
		position: absolute;
		transform: rotate(90deg);
	}
	
	.icon-del {
		width: 15px;
		height: 2px;
		background-color: #fff;
		border-radius: 2px;
	}
</style>