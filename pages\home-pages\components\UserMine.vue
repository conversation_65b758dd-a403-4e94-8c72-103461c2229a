<template>
	<view class="app-container">
		<view class="bg-wrap">
			<image class="bg-img"
				:src="userInfo?.background_url ? prefixFilePath(userInfo?.background_url) : userPagesBg"
				mode="widthFix" />
			<view class="bg-mask" :style="{ opacity: isTopHeight ? 0 : 1 }" />
		</view>
		<z-paging ref="pagePaging" refresher-only @onRefresh="onRefresh" @scrolltolower="scrolltolower"
			@scroll="scroll">
			<template #top>
				<Navbar :type="1" backgroundColor="transparent" />
			</template>
			<view class="topHeigt" :style="{ height: isTopHeight ? topHeight : defTopHeight }" @click="onBg" />
			<view class="user">
				<view class="info">
					<view class="flex items-center">
						<text class="user-name" @click="onShowName">{{ otherInfo?.privacy?.remark || (userInfo?.nickname
							|| '用户' +
							(userInfo?.uid || userInfo?.account || userInfo?.id))
							}}</text>
						<text :class="['user-interest', otherInfo?.is_followed && 'user-interest-no']"
							@click="onFollow">
							{{ otherInfo?.is_followed ? '已关注' : '关注' }}
						</text>
					</view>
					<view class="uid">UID: {{ userInfo?.account || '-' }}</view>
					<view class="user-bio">{{ userInfo?.bio || '这家伙什么都没留下~' }}</view>
					<view class="gender">
						<image v-if="userInfo?.gender === 1" class="gender-icon" src="/static/my/<EMAIL>" />
						<image v-if="userInfo?.gender === 2" class="gender-icon" src="/static/my/<EMAIL>" />
						<view class="addr" v-if="!!userInfo?.city">
							<text>{{ userInfo?.city.name }}{{ userInfo?.district ? userInfo?.district.name : ''
								}}</text>
						</view>
					</view>
				</view>
				<view class="avatar-bar">
					<uv-avatar :src="prefixFilePath(userInfo?.avatar)" size="176rpx" mode="aspectFill"
						default-url="/static/Default_Avatar_boy3.png"></uv-avatar>
				</view>
			</view>
			<view class="box-info">
				<view class="info-data">
					<view class="flex flex-col items-center">
						<text class="num">{{ userInfo?.follow_count || 0 }}</text>
						<text class="name">关注</text>
					</view>
					<view class="flex flex-col items-center">
						<text class="num">{{ userInfo?.fans_count || 0 }}</text>
						<text class="name">粉丝</text>
					</view>
					<view class="flex flex-col items-center">
						<text class="num">{{ userInfo?.thumbs_up_count || 0 }}</text>
						<text class="name">获赞</text>
					</view>
				</view>
				<view class="flex">
					<image class="icon-msg" src="/static/home/<USER>" @click="toFriendChat"/>
					<image class="icon-msg" src="/static/home/<USER>" @click="onOther" />
				</view>
			</view>
			<view class="reservation">
				<view class="reservation-item" @click="() => toOtherHomeService('photography')">
					<image class="reservation-icon" src="/static/img/exhibition-details/icon_sy.png" />
					<text class="reservation-txt">约拍</text>
				</view>
				<view class="reservation-item" @click="() => toOtherHomeService('makeup')">
					<image class="reservation-icon" src="/static/img/exhibition-details/icon_hz.png" />
					<text class="reservation-txt">约妆</text>
				</view>
			</view>

			<view class="my-tabs">
				<view class="tabs-container" style="z-index: 100; position: sticky; top: 0">
					<dust-tabs v-model="current" :list="tabList" tabType="primary"></dust-tabs>
				</view>
				<view class="tabs-body">
					<view class="item" v-for="item in otherInfo?.dimension.results" :key="item.id">
						<image class="item-img" :src="prefixFilePath(item.cover)" />
						<view class="sc">
							<view class="sc-bar">
								<image class="icon-sc" src="/static/my/<EMAIL>" />
							</view>
							<text>{{ item.like_count }}</text>
						</view>
					</view>
				</view>
				<uv-empty text="该用户暂未发布次元" marginTop="100" v-if="!otherInfo?.dimension.results.length" />
			</view>
		</z-paging>
		<quick-login-dialog></quick-login-dialog>
	</view>
	<OtherModel v-if="otherModelShow" :show="true" :data="info" :onClose="closeOtherModel" :onChange="getUserInfo" />
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useStore } from 'vuex'
import OtherModel from './OtherModel.vue';
import { getOtherUserInfo, putUserRemark, putUserNoLookMy, putUserNoLookTheir } from '@/api/message'
import { createFriendConversation } from '@/api/notification'
import { addFollow, delFollow } from '@/api/user'
import { onLoad, onBackPress, onUnload } from '@dcloudio/uni-app';
import userPagesBg from '@/static/my/<EMAIL>'

const store = useStore();
const info = ref()
const userInfo = ref()
const otherInfo = ref()
const current = ref(0); // tabs组件的current值，表示当前活动的tab选项
const pagePaging = ref(null);
const tabList = reactive([{ name: '次元', value: 0 }])
const otherModelShow = ref(false);
const userId = ref(0);
const statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 44
const viewHeight = 143 + 61.5 + 36;
const defTopHeight = `calc(100vw / 750 * 974 - ${statusBarHeight + viewHeight}px)`;
const topHeight = `calc(100vw / 750 * 974 - ${statusBarHeight}px)`;
let isTopHeight = ref(false)

onLoad((e) => {
	userId.value = e.id || 101;
})
onMounted(() => {
	getUserInfo()
})

// 获取用户信息
const getUserInfo = async () => {
	const { data } = await getOtherUserInfo(userId.value);
	const { userinfo, ...otherinfo } = data
	userInfo.value = userinfo;
	otherInfo.value = otherinfo;
	info.value = data
	console.log(userInfo.value, '1')
}
// 关注
const onFollow = async () => {
	otherInfo.value?.is_followed ? await delFollow(userId.value) : await addFollow(userId.value)
	await getUserInfo()
	pagePaging.value.endRefresh()
}
// 显示名字
const onShowName = () => {
	uni.showModal({
		title: otherInfo.value?.privacy?.remark || userInfo.value?.nickname,
		showCancel: false,
	})
}
// 点击蒙版
const onBg = () => {
	isTopHeight.value = !isTopHeight.value
	pagePaging.value.scrollToTop()
}
// 监听滚动
const scroll = (e) => {
	// isTopHeight.value = e.detail.scrollTop > 0
	console.log('监听滚动', e.detail.scrollTop);
	if (e.detail.scrollTop > 100 && isTopHeight.value) {
		isTopHeight.value = false
		pagePaging.value.scrollToTop()
	}

}
// 下拉刷新
const onRefresh = async () => {
	await getUserInfo()
	pagePaging.value.endRefresh()
}
// 上拉加载
const scrolltolower = () => {
}
// 点击更多
const onOther = () => {
	otherModelShow.value = true;
}
// 跳转其他用户服务
const toOtherHomeService = (type) => {
	uni.navigateTo({
		url: `/pages/packageA/other-home-service/other-home-service?type=${type}&id=${userId.value}`
	})
}


// 关闭其他弹窗并做信息更新相关处理
const closeOtherModel = async ({ remark, hide_my_posts, hide_their_posts, blacklist, friend, kithh, accusation }) => {
	// 对应的值改变了再去请求接口，有一个改变了就重新调取userInfo接口
	let flag = false;
	if (remark !== otherInfo.value?.privacy?.remark) {
		flag = true;
		await putUserRemark(userId.value, remark)
	}
	if (hide_my_posts !== otherInfo.value?.privacy?.hide_my_posts) {
		flag = true;
		await putUserNoLookMy(userId.value)
	}
	if (hide_their_posts !== otherInfo.value?.privacy?.hide_their_posts) {
		flag = true;
		await putUserNoLookTheir(userId.value)
	}
	flag && getUserInfo()
	otherModelShow.value = false;
}
// 当前环境域名和资源地址拼接
const prefixFilePath = (url) => {
	if (!url) return '';
	return store.state.common.cloudFileUrl + url;
}

const toFriendChat = () => {
	const { nickname, avatar } = userInfo.value
	createFriendConversation({
		friend_id: null,
		user_id: +userId.value,
		group_id: null 
	}).then(res => {
		if(res.code === 20000) {
			console.log('res', res)
			// res.data 应该包含 conversationId
			const conversationId = res.data.id || res.data.conversation_id || userId.value;
			uni.navigateTo({
				url: `/pages/packageA/friend-chat/friend-chat?id=${conversationId}&friendId=${userId.value}&nickname=${nickname}&avatar=${avatar}&from=userMine`
			})
		}
	})
}
</script>

<style lang="scss" scoped>
.bg-wrap {
	position: absolute;
	z-index: 0;
	top: 0;
	left: 0;
	right: 0;
	width: 100vw;

	.bg-img {
		width: 100vw;
	}

	.bg-mask {
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		width: 100%;
		transition: all 0.5s;
		background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.7) 54%, #FFFFFF 100%);
	}
}

.topHeigt {
	//动画
	transition: all 0.5s;
}


.user {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	padding: 24rpx 40rpx 0rpx;
}

.info {
	flex: 1;

	.user-name {
		max-width: calc(100vw - 400rpx);
		color: #1f1f1f;
		font-weight: 700;
		font-size: 48rpx;
		// 超出一行省略
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	.user-interest {
		width: 116rpx;
		height: 56rpx;
		margin-left: 32rpx;
		text-align: center;
		font-size: 28rpx;
		line-height: 56rpx;
		color: #ffffff;
		border-radius: 28rpx;
		background: #FE58B7;
	}

	.user-interest-no {
		color: #3D3D3D;
		background-color: #fff;
	}

	.uid {
		margin-top: 24rpx;
		font-size: 28rpx;
		color: rgba(31, 31, 31, 0.70);
	}

	.user-bio {
		margin-top: 20rpx;
		font-size: 28rpx;
		color: rgba(31, 31, 31, 0.70);
	}

	.gender {
		display: flex;
		align-items: center;
		margin-top: 28rpx;
	}

	.gender-icon {
		width: 48rpx;
		height: 48rpx;
	}

	.addr {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 48rpx;
		padding: 0 18rpx;
		margin-left: 18rpx;
		border-radius: 48rpx;
		font-size: 24rpx;
		color: #4e5969;
		border: 1rpx solid rgba(255, 255, 255, 0.5);
		background: rgba(255, 255, 255, 0.6);
	}
}

.avatar-bar {
	margin-left: 10rpx;
	border-radius: 50%;
	border: 2rpx solid rgba(255, 255, 255, 0.50);
}

.box-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 40rpx 0;

	.info-data {
		display: flex;
		align-items: center;
		justify-content: space-between;
		min-width: 45vw;
		gap: 20rpx;

		.num {
			font-weight: 700;
			font-size: 40rpx;
			color: #3d3d3d;
		}

		.name {
			font-weight: 500;
			font-size: 24rpx;
			color: #3d3d3d;
			margin-top: 10rpx;
		}
	}

	.icon-msg {
		width: 56rpx;
		height: 56rpx;
		margin-left: 24rpx;
	}
}

.reservation {
	display: flex;
	padding: 48rpx 40rpx 0;

	.reservation-item {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 184rpx;
		height: 80rpx;
		margin-right: 32rpx;
		border-radius: 40rpx;
		border: 2rpx solid #4E5969;
		background: #FFFFFF;
	}

	.reservation-icon {
		width: 56rpx;
		height: 56rpx;
		margin-right: 8rpx;
	}

	.reservation-txt {
		font-size: 28rpx;
		color: #3D3D3D;
	}
}

.my-tabs {
	margin-top: 40rpx;
	border-radius: 32rpx 32rpx 0rpx 0rpx;
	box-shadow: 0rpx -20rpx 24rpx 0rpx rgba(78, 89, 105, 0.1);
	background-color: #fff;

	.tabs-container {
		background: #ffffff;
		border-radius: 32rpx 32rpx 0rpx 0rpx;
		padding: 32rpx;
		position: relative;
		z-index: 1;
	}

	.tabs-body {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		// grid-template-rows: repeat(3, 1fr);
		padding: 0 32rpx 32rpx;
		gap: 32rpx;

		.item {
			border-radius: 24rpx;
			position: relative;
		}

		.item-img {
			border-radius: 24rpx;
			width: 208rpx;
			height: 160rpx;
		}

		.sc {
			background: rgba(0, 0, 0, 0.5);
			border-radius: 20rpx 0rpx 24rpx 0rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 88rpx;
			height: 32rpx;
			position: absolute;
			right: 0;
			bottom: 0;
			font-size: 20rpx;
			color: #FFFFFF;
		}

		.sc-bar {
			position: relative;
			width: 24rpx;
			height: 24rpx;
			margin-right: 8rpx;
		}

		.icon-sc {
			width: 44rpx;
			height: 44rpx;
			position: absolute;
			left: -6rpx;
			top: 0;
		}
	}
}
</style>
