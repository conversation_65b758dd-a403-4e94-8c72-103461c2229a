


type Item<T extends string> = {
	text: string;
	value: T;
	children?: T extends `${infer P}${string}` 
    ? Item<`${P}${string}`>[] 
    : never;
};

type ValueCode = "1" | "2" | "3" | "4" | "5" | "6";
const violationData: Item<ValueCode>[] = [
  {
    text: "违法信息类",
    value: "1",
    children: [
      {text: "暴力恐怖内容", value: "101"},
      {text: "违禁品信息", value: "102"},
      {text: "煽动颠覆政权", value: "103"},
      {text: "其他违法内容", value: "104"}
    ]
  }, {
    text: "不良信息类",
    value: "2",
    children: [
      { text: "色情低俗", value: "201"},
      { text: "血腥暴力", value: "202"},
      { text: "自杀自残", value: "203"},
      { text: "封建迷信", value: "204"}
    ]
  }, {
    text: "侵权类",
    value: "3",
    children: [
      { text: "侵犯著作权", value: "301"},
      { text: "侵犯隐私权", value: "302"},
      { text: "肖像权侵权", value: "303"},
      { text: "名誉权侵权", value: "304"}
    ]
  }, {
    text: "虚假信息类",
    value: "4",
    children: [
      { text: "不实谣言", value: "401"},
      { text: "伪科学", value: "402"},
      { text: "虚假广告", value: "403"},
      { text: "仿冒诈骗", value: "404"}
    ]
  }, {
    text: "社区规范类",
    value: "5",
    children: [
      { text: "垃圾广告", value: "501"},
      { text: "重复发布", value: "502"},
      { text: "引战行为", value: "503"},
      { text: "无关内容", value: "504"},
      { text: "恶意灌水", value: "505"}
    ]
  }, {
    text: "特殊类型（可选）",
    value: "6",
    children: [
      { text: "未成年人保护", value: "601"},
      { text: "动物虐待", value: "602"},
      { text: "歧视性内容", value: "603"},
      { text: "其他违规", value: "604"}
    ]
  }
];

export default violationData;

// 举报类型
export const enum ReportType {
	Service = 1,  	// 服务举报
	Comiket 		// 漫展举报
}
// 进入举报需要传递的query类型
export interface QueryParma {
	type: ReportType;  	// 举报类型
	id: string;			// 举报id
	firtstTypeCode?: string  //一级类型id
	secondTypeCode?: string  //二级类型id
}