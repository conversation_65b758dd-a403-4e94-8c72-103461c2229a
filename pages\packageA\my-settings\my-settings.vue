<template>
	<view class="app-container">
		<Navbar :type="1" leftText="设置"></Navbar>
		<view class="entrance-container">
			<view class="box">
				<view class="box-main">
					<view class="box-item" @tap="onNav('/pages/packageA/agreement/userAgreement', 'userAgreement')">
						<text class="v-text">用户协议</text>
						<view class="v-value">
							<image class="icon" src="/static/icon/<EMAIL>"></image>
						</view>
					</view>
					<view class="box-item" @tap="onNav('/pages/packageA/agreement/privacyAgreement', 'privacyAgreement')">
						<text class="v-text">隐私政策</text>
						<view class="v-value">
							<image class="icon" src="/static/icon/<EMAIL>"></image>
						</view>
					</view>
					<view class="box-item" @tap="onNav('/pages/packageA/agreement/agreement', 'agreement')">
						<text class="v-text">儿童/青少年个人信息保护规则</text>
						<view class="v-value">
							<image class="icon" src="/static/icon/<EMAIL>"></image>
						</view>
					</view>
					<view v-if="!!userInfo.token" class="box-item" @tap="onNav('/pages/packageA/invite-list/invite-list', 'invite-list')">
						<text class="v-text">自己邀请人</text>
						<view class="v-value">
							<image class="icon" src="/static/icon/<EMAIL>"></image>
						</view>
					</view>
					<view v-if="!!userInfo.token" class="box-item" @tap="onNav('/pages/packageA/blacklist/blacklist', 'blacklist')">
						<text class="v-text">黑名单</text>
						<view class="v-value">
							<image class="icon" src="/static/icon/<EMAIL>"></image>
						</view>
					</view>
					<view class="box-item" v-if="!!userInfo.token" @tap="onNav('/pages/packageA/verify-idcard/verify-idcard', 'verify-idcard')">
						<text class="v-text">实名认证</text>
						<view class="v-value">
							<text class="val">{{userInfo.cert_level <= 0 ? '未实名' : '已实名'}}</text>
							<image v-if="userInfo.cert_level <= 0" class="icon" src="/static/icon/<EMAIL>"></image>
						</view>
					</view>
					<view v-if="!!userInfo.token" class="box-item" @tap="onNav('/pages/packageA/my-questions/list', 'my-questions')">
						<text class="v-text">常见问题</text>
						<view class="v-value">
							<image class="icon" src="/static/icon/<EMAIL>"></image>
						</view>
					</view>
					<view class="box-item" @tap="onNav('/pages/packageA/agreement/commissionAgreement', 'commissionAgreement')">
						<text class="v-text">返佣规则</text>
						<view class="v-value">
							<image class="icon" src="/static/icon/<EMAIL>"></image>
						</view>
					</view>
					<view class="box-item" @tap="onNav('/pages/packageA/my-help-center/helpCenter', 'helpCenter')">
						<text class="v-text">帮助与客服</text>
						<view class="v-value">
							<image class="icon" src="/static/icon/<EMAIL>"></image>
						</view>
					</view>
				</view>
			</view>
			
			<btn-group v-if="!!userInfo.token">
				<button type="primary" class="btn-submit" @tap="handleLogout">退出登录</button>
			</btn-group>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
	} from 'vue'
	import {
		useStore
	} from 'vuex';

	const store = useStore();
	const userInfo = computed(() => store.state.userInfo)
	const adminSetting = computed(() => store.state.common.adminSetting);

	function handleLogout() {
		uni.showModal({
			title: '提示',
			content: '确定要退出登录吗？',
			success(e) {
				if (e.confirm) {
					store.dispatch('userInfo/logout')
					store.dispatch('userInfo/cleanUseInfo')
					uni.navigateBack()
				}
			}
		})
	}
	
	const tips = () => {
		uni.showToast({
			title: '功能开发中',
			icon: 'none'
		})
	}
	
	function onNav(url, type) {
		if (type === 'verify-idcard') {
			if (userInfo.value.cert_level <= 0) {
				uni.navigateTo({
					url,
				})
			}
		} else {
			uni.navigateTo({
				url,
			})
		}
	}
</script>

<style lang="scss" scoped>
	.entrance-container {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		flex: 1;
	}
	
	.box {
		padding: 32rpx;
	}
	.icon {
		width: 40rpx;
		height: 40rpx;
	}
	.box-main {
		padding: 12rpx 0;
		background: #F7F8FA;
		border-radius: 16rpx;
	}
	.box-item {
		display: flex;
		align-items: center;
		font-size: 36rpx;
		color: #1F1F1F;
		padding: 30rpx 32rpx;
		.v-text {
			flex: 1;
		}
		.v-value {
			display: flex;
			align-items: center;
			
			.val {
				color: #000;
				font-size: 30rpx;
			}
		}
	}
	
	.btn-wrap {
		padding: 0 40rpx;
	}
</style>