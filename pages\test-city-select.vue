<template>
  <view class="test-page">
    <view class="header">
      <text class="title">通讯录风格城市选择器测试</text>
    </view>
    
    <view class="content">
      <view class="result-section" v-if="singleResult || multipleResult.length > 0">
        <text class="section-title">选择结果：</text>
        
        <!-- 单选结果 -->
        <view class="result-item" v-if="singleResult">
          <text class="label">单选结果：</text>
          <text class="city-name">{{ singleResult.name }}</text>
        </view>
        
        <!-- 多选结果 -->
        <view class="result-item" v-if="multipleResult.length > 0">
          <text class="label">多选结果：</text>
          <view class="cities">
            <text class="city-name" v-for="city in multipleResult" :key="city.id">{{ city.name }}</text>
          </view>
        </view>
      </view>
      
      <view class="button-section">
        <button class="test-btn" @click="showSingleSelect">单选城市</button>
        <button class="test-btn" @click="showMultipleSelect">多选城市</button>
        <button class="test-btn clear" @click="clearResult">清空结果</button>
      </view>
    </view>
    
    <!-- 单选城市弹窗 -->
    <uv-popup v-model:show="showSinglePopup" mode="bottom" :round="20" :safe-area-inset-bottom="true">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">选择城市（单选）</text>
          <uv-icon name="close" size="40" @click="showSinglePopup = false"></uv-icon>
        </view>
        <city-select 
          :multiple="false"
          :value="singleResult" 
          :show-hot-cities="true"
          :show-current-location="true"
          @confirm="handleSingleConfirm"
          @change="handleSingleChange">
        </city-select>
      </view>
    </uv-popup>
    
    <!-- 多选城市弹窗 -->
    <uv-popup v-model:show="showMultiplePopup" mode="bottom" :round="20" :safe-area-inset-bottom="true">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">选择城市（多选）</text>
          <uv-icon name="close" size="40" @click="showMultiplePopup = false"></uv-icon>
        </view>
        <city-select 
          :multiple="true"
          :value="multipleResult" 
          :show-hot-cities="true"
          :show-current-location="true"
          @confirm="handleMultipleConfirm"
          @change="handleMultipleChange">
        </city-select>
      </view>
    </uv-popup>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import CitySelect from '@/components/city-select/city-select.vue'

const showSinglePopup = ref(false)
const showMultiplePopup = ref(false)
const singleResult = ref(null)
const multipleResult = ref([])

function showSingleSelect() {
  showSinglePopup.value = true
}

function showMultipleSelect() {
  showMultiplePopup.value = true
}

function handleSingleConfirm(data) {
  console.log('单选确认:', data)
  singleResult.value = data
  showSinglePopup.value = false
  
  uni.showToast({
    title: `已选择：${data.name}`,
    icon: 'success'
  })
}

function handleSingleChange(data) {
  console.log('单选变化:', data)
}

function handleMultipleConfirm(data) {
  console.log('多选确认:', data)
  multipleResult.value = data
  showMultiplePopup.value = false
  
  uni.showToast({
    title: `已选择 ${data.length} 个城市`,
    icon: 'success'
  })
}

function handleMultipleChange(data) {
  console.log('多选变化:', data)
}

function clearResult() {
  singleResult.value = null
  multipleResult.value = []
  uni.showToast({
    title: '已清空选择结果',
    icon: 'success'
  })
}
</script>

<style scoped lang="scss">
.test-page {
  min-height: 100vh;
  background: #F5F5F5;
}

.header {
  padding: 40rpx 32rpx 20rpx;
  background: #fff;
  border-bottom: 1rpx solid #E5E5E5;
  
  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.content {
  padding: 32rpx;
}

.result-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
  
  .result-item {
    margin-bottom: 16rpx;
    
    .label {
      font-size: 28rpx;
      color: #666;
      margin-right: 16rpx;
    }
    
    .city-name {
      font-size: 28rpx;
      color: #007AFF;
      margin-right: 16rpx;
      padding: 8rpx 16rpx;
      background: #E8F4FD;
      border-radius: 8rpx;
      display: inline-block;
      margin-bottom: 8rpx;
    }
    
    .cities {
      display: flex;
      flex-wrap: wrap;
      gap: 8rpx;
    }
  }
}

.button-section {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  
  .test-btn {
    height: 88rpx;
    background: #007AFF;
    color: #fff;
    border: none;
    border-radius: 16rpx;
    font-size: 32rpx;
    font-weight: 500;
    
    &.clear {
      background: #FF3B30;
    }
    
    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }
  }
}

.popup-content {
  height: 90vh;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #E5E5E5;
  
  .popup-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}
</style>
