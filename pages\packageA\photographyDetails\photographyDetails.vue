<template>
	<z-paging class="app-container" ref="paging" v-model="dataList" @query="queryList" :safe-area-inset-bottom="dataList.length > 0" :use-safe-area-placeholder="dataList.length > 0" :hide-empty-view="true">
		<template #top>
			<Navbar :type="1"></Navbar>
		</template>
		<view class="allPidding top">
			<dust-image-base wrapperRadius="20rpx" :src="info.cover" :local="false" aspect-ratio="16:9"></dust-image-base>
			<view class="topBox">
				<text class="name">{{ info.name }}</text>
				<view class="topBoxRight">
					<view class="nameBox">
						<view @click="toUser">
							<image :src="prefixFilePath(info.user_avatar)" style="width: 56rpx; height: 56rpx; border-radius: 50%"></image>
							<image src="/static/my/<EMAIL>" mode="aspectFill" class="sex" v-if="info.user_gender == 1"></image>
							<image src="/static/my/<EMAIL>" mode="aspectFill" class="sex" v-else-if="info.user_gender == 2"></image>
						</view>
						<text>{{ info.user_nickname }}</text>
					</view>
					<view class="evaluate">
						<view style="width: 68rpx"></view>
						<uv-rate size="32rpx" gutter="1" :value="info.score / 20" allowHalf active-color="#FE58B7" inactive-color="#FE58B7" readonly></uv-rate>
						<text style="margin-left: 8rpx" v-if="(info.score / 20).toFixed(1) > 0">{{ (info.score / 20).toFixed(1) }}</text>
						<text style="margin-left: 2rpx" v-if="(info.score / 20).toFixed(1) > 0">分</text>
						<text style="margin-left: 4rpx" v-if="(info.score / 20).toFixed(1) > 0">推荐</text>
						<text style="margin-left: 8rpx" v-else>暂无评分</text>
						<text style="margin-left: 56rpx" class="grey">{{ info.review_count }}</text>
						<text style="margin-left: 4rpx" class="grey">条评价</text>
					</view>
					<view class="label">
						<view style="width: 68rpx"></view>
						<!-- info.tags -->
						<view class="labelBox" v-for="(item, index) in info.tags" :key="index2">
							<text>{{ item }}</text>
						</view>
						<image class="item_icon" src="@/static/home/<USER>" @tap="reportHandle" />
					</view>
				</view>
			</view>
		</view>
		<view class="allPidding" v-if="info.detail">
			<editorDate :data="info.detail" :type="info.editor" :artworks="info.artworks" />
		</view>
		<view class="appraise allPidding">
			<view>
				<text>评价 ({{ info.review_count }})</text>
			</view>
		</view>
		<uni-list :border="false" v-if="dataList.length > 0">
			<uni-list-item v-for="(item, index) in dataList" :key="item.id" :border="false">
				<template v-slot:body>
					<view class="appraiseList">
						<view class="appraiseListTop">
							<image class="userImg" :src="prefixFilePath(item.user_avatar)"></image>
							<view class="appraiseListRight">
								<text class="userName">{{ item.user_nickname }}</text>
								<text class="userAppraise" v-if="item.editor == 'orderComment'">{{ item.content.txt }}</text>
								<text class="userAppraise" v-else>{{ item.content }}</text>
								<image :src="prefixFilePath(item.content.url)" v-if="item.editor == 'orderComment' && item.content.url" style="margin-top: 10rpx"></image>
								<view class="appraiseBottom">
									<view>
										<text>{{ timeToString(item.created_at) }}</text>
									</view>
								</view>
							</view>
						</view>
						<view class="appraiseListBottom">
							<image class="userImg" style="width: 64rpx; height: 64rpx" :src="prefixFilePath(info.user_avatar)" mode=""></image>
							<view class="appraiseListRight" v-if="item.reply">
								<text class="userName">{{ info.name }}</text>
								<text class="userAppraise">{{ item.reply.content }}</text>
								<view class="appraiseBottom">
									<view>
										<text>{{ timeToString(item.reply.created_at) }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</template>
			</uni-list-item>
		</uni-list>
		<empty-list v-else>
			<text>暂无评论数据</text>
		</empty-list>
		<template #bottom>
			<view class="bottom">
				<view class="bottomLeft" @tap="favorite()">
					<image v-if="!info.is_collected" src="../static/photographyDetails/<EMAIL>"></image>
					<image v-else src="../static/photographyDetails/<EMAIL>"></image>
					<text>收藏</text>
				</view>
				<view
					v-if="store.state.userInfo.id != info.user_id"
					class="bottomRight"
					@click="
						() => {
							overlayShow = true;
						}
					"
				>
					<text>选择服务</text>
				</view>
				<!-- <uv-safe-bottom></uv-safe-bottom> -->
			</view>
		</template>
	</z-paging>
	<uv-overlay :show="overlayShow">
		<view class="serve" @tap.stop>
			<view class="serveTitle">
				<text>推荐服务</text>
				<image @click="overlayShow = false" src="../static/photographyDetails/<EMAIL>" mode=""></image>
			</view>
			<view style="margin-top: 108rpx"></view>
			<scroll-view style="height: 490rpx" scroll-y="true" class="scroll-Y">
				<view @click="activeServe(index)" class="serveList" v-for="(item, index) in list" :class="active == index ? 'serveActive' : ''">
					<view class="imageView">
						<dust-image-base :src="item.cover" :local="false" aspect-ratio="1:1"></dust-image-base>
					</view>
					<view class="serveListRight">
						<view style="margin-top: 12rpx"></view>
						<text class="serveName txtellipsis">{{ item.name }}</text>
						<view class="serveNumber">
							<text>商家保证金{{ item.bond }}%</text>
							<text>已售 {{ item.sold }}</text>
						</view>
						<text class="priceText">
							<text class="price">￥</text>
							{{ item.price / 100 }}
						</text>
					</view>
				</view>
			</scroll-view>
			<view class="overlayShowBottom" @tap.stop>
				<view @click="toOrders()"><text>立即咨询</text></view>
			</view>
			<uv-safe-bottom></uv-safe-bottom>
		</view>
	</uv-overlay>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onLoad, onBackPress, onUnload } from '@dcloudio/uni-app';
import { getDimensionServiceDetail, collectDimensionService, getDimensionServiceReviews, getDimensionServiceItems } from '@/api/dimensionService';
import { createConsult, putConsult } from '@/api/notification';
import { useStore } from 'vuex';
import { timeToString, toLog } from '@/common/utils.js';
const store = useStore();
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);
import useContentHeight from '@/composables/useContentHeight.ts'

const { contentHeight } = useContentHeight({
  baseSpacing: '80rpx', // 可调整的值
});

function prefixFilePath(url) {
	if (!url) return '';
	return cloudFileUrl.value + url;
}
const paging = ref(null);
const dataList = ref([]);
const label = ref(['全城约拍', '夜景', '外景', '带妆']);
const collectionShow = ref(false);
const overlayShow = ref(false);
let id = ref(null);
let info = ref({});
let list = ref([]);
let pageType = ref(null);
// let consultid = ref(null);
onLoad((e) => {
	id.value = e.id;
	if (e.pageType) {
		pageType.value = e.pageType;
	}
	console.log(333333, store);
	getInfo();
	getDimensionServiceItems(id.value).then((res) => {
		if (res.code == 20000) {
			list.value = res.data.results;
		}
	});
});
function ckimg() {
	uni.previewImage({
		urls: [prefixFilePath(info.value.cover)],
		longPressActions: {
			itemList: ['发送给朋友', '保存图片', '收藏'],
			success: function (data) {},
			fail: function (err) {}
		}
	});
}
//获取评论
function getComment() {
	getDimensionServiceReviews().then((res) => {});
}
function collection() {
	collectionShow.value = !collectionShow.value;
}
const active = ref(0);
function activeServe(index) {
	active.value = index;
}
//获取详情
function getInfo() {
	getDimensionServiceDetail(id.value).then((res) => {
		info.value = res.data;
	});
}
function favorite() {
	toLog();
	collectDimensionService(info.value.id).then((res) => {
		if (res.code == 20000) {
			info.value.is_collected = !info.value.is_collected;
		}
	});
}
//跳转咨询页
function toOrders() {
	toLog();
	//创建咨询
	createConsult(info.value.items[active.value].id).then((res) => {
		if (res.code == 20000) {
			putConsult(list.value[active.value].id, res.data.id).then((res2) => {
				if (res2.code == 20000) {
					overlayShow.value = false;
					if (pageType.value == 'notification') {
						uni.navigateBack();
					} else {
						uni.navigateTo({
							url: `/pages/tabbar/notification/ordersNotificationDetails?type=1&consultid=${res.data.id}&from=photographyDetail`
						});
					}
				}
			});
		}
	});
}
async function queryList(pageNo, pageSize) {
	const params = {
		page: pageNo,
		size: 10
	};
	let res = await getDimensionServiceReviews(id.value, params);
	if (res.code === 20000) {
		info.value.review_count = res.data.count;
		for (let item of res.data.results) {
			if (item.editor == 'orderComment') {
				item.content = JSON.parse(item.content);
				console.log(3333, item);
			}
		}
		paging.value.complete(res.data.results);
	} else {
		info.value.review_count = 0;
		paging.value.complete(false);
	}
}
// 跳转用户主页
function toUser() {
	uni.navigateTo({
		url: `/pages/home-pages/user-mine?id=${info.value.user_id}`
	});
}

// 举报
function reportHandle() {
	// 需要传递举报是服务举报(type为1) 还是 漫展举报（type为2）且需要传递对应漫展或者服务id
	uni.navigateTo({
		url: "/pages/packageA/report/first-type?" + `id=${id.value}&type=${1}`
	})
}
</script>

<style lang="scss">
.allPidding {
	padding-left: 24rpx;
	padding-right: 24rpx;
}

.textHide {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.serve {
	position: fixed;
	bottom: 0;
	padding-bottom: 30rpx;
	border-radius: 32rpx 32rpx 0rpx 0rpx;
	width: 100%;
	background-color: #fff;
	font-size: 32rpx;
	color: #3d3d3d;
	height: 698rpx;
	// overflow-x: hidden;

	.serveActive {
		background: rgba(254, 88, 183, 0.12);
		border-radius: 20rpx;
		border: 4rpx solid #fe58b7 !important;
	}
	.serveList {
		margin-bottom: 32rpx;
		margin-left: 32rpx;
		display: flex;
		width: 686rpx;
		height: 208rpx;
		border: 4rpx solid rgba(0, 0, 0, 0);
		.imageView {
			width: 208rpx;
			height: 208rpx;
			border-radius: 20rpx;
			margin-right: 32rpx;
			overflow: hidden;
			background-color: #1f1f1f;
			// image {
			// 	width: 208rpx;
			// 	height: 208rpx;
			// 	// border-radius: 20rpx;
			// 	// margin-right: 32rpx;
			// }
		}

		.serveListRight {
			width: 446rpx;
			.serveName {
				font-weight: 700;
				font-size: 32rpx;
				color: #3d3d3d;
			}
			.serveNumber {
				font-size: 28rpx;
				color: #85878d;
				margin-top: 10rpx;
				margin-bottom: 6rpx;
				text {
					margin-right: 40rpx;
				}
			}
			.priceText {
				font-weight: 700;
				font-size: 36rpx;
				color: #fe58b7;
				.price {
					font-size: 24rpx;
				}
			}
		}
	}
	.serveTitle {
		border-radius: 32rpx 32rpx 0rpx 0rpx;
		width: 100%;
		height: 108rpx;
		background-color: #fff;
		z-index: 999;
		position: fixed;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 28rpx;
		image {
			position: fixed;
			width: 48rpx;
			height: 48rpx;
			right: 32rpx;
		}
	}
}
.overlayShowBottom {
	display: flex;
	justify-content: center;
	view {
		margin-top: 16rpx;
		width: 400rpx;
		height: 80rpx;
		background: #fe58b7;
		border-radius: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 36rpx;
		color: #fff;
	}
}

.top {
	.bar {
		width: 702rpx;
		height: 396rpx;
		border-radius: 32rpx 32rpx 0rpx 0rpx;
		margin-bottom: 20rpx;
	}

	.address {
		margin-bottom: 32rpx;
		height: 56rpx;
		padding-right: 8rpx;
		font-weight: 400;
		font-size: 28rpx;
		color: #aaaaaa;
		display: flex;
		align-items: center;

		image:nth-child(1) {
			margin-right: 8rpx;
			width: 28rpx;
			height: 28rpx;
		}

		.addressImage {
			width: 56rpx;
			height: 56rpx;
		}
	}

	.topBox {
		display: flex;
		flex-direction: column;
		margin-top: 20rpx;
		
		.name {
			font-weight: 700;
			font-size: 32rpx;
			color: #3d3d3d;
		}

		.topBoxRight {
			display: flex;
			flex-direction: column;
			.nameBox {
				display: flex;
				align-items: center;
				margin-top: 10rpx;
				margin-bottom: 10rpx;
				view {
					position: relative;
					margin-right: 12rpx;
					.sex {
						width: 24rpx !important;
						height: 24rpx !important;
						position: absolute;
						bottom: 0;
						right: 0;
					}
				}
			}
			.evaluate {
				display: flex;
				align-items: center;
				font-weight: 400;
				font-size: 28rpx;
				color: #fe58b7;
				margin-bottom: 24rpx;
				.grey {
					color: #85878d;
				}
			}

			.label {
				position: relative;
				display: flex;
				margin-left: 6rpx;
				margin-bottom: 28rpx;
				min-height: 40rpx;

				.labelBox {
					margin-right: 16rpx;
					padding: 0 16rpx;
					border-radius: 8rpx;
					border: 2rpx solid #fab001;
					font-weight: 350;
					font-size: 24rpx;
					color: #fab001;
					line-height: 34rpx;
				}
				.item_icon {
					position: absolute;
					top: -6rpx;
					right: 0;
					height: 50rpx;
					width: 50rpx;
				}
			}
		}
	}
}

// .describes {
// 	border-top: 2rpx solid #d8d8d8;
// 	border-bottom: 2rpx solid #d8d8d8;
// 	display: flex;
// 	flex-direction: column;

// 	text:nth-child(1) {
// 		font-size: 32rpx;
// 		color: #3d3d3d;
// 		margin-top: 32rpx;
// 		margin-bottom: 12rpx;
// 	}

// 	text:nth-child(2) {
// 		font-size: 28rpx;
// 		color: #85878d;
// 	}

// }

.appraise {
	padding-top: 32rpx;

	text {
		font-size: 28rpx;
		color: #3d3d3d;
	}
}
.appraiseList {
	margin-bottom: 24rpx;
	width: 100%;
	// display: flex;
	.appraiseListTop {
		display: flex;
	}

	.appraiseListBottom {
		display: flex;
		margin-left: 108rpx;
		margin-top: 30rpx;
	}
	.userImg {
		width: 84rpx;
		height: 84rpx;
		margin-right: 24rpx;
	}

	.appraiseListRight {
		width: 574rpx;
		display: flex;
		flex-direction: column;

		.userName {
			font-size: 32rpx;
			color: #1f1f1f;
			margin-bottom: 20rpx;
		}

		.userAppraise {
			color: #1f1f1f;
		}

		.appraiseBottom {
			margin-top: 16rpx;
			display: flex;
			align-items: center;

			view {
				text:nth-child(1) {
					font-size: 24rpx;
					color: #85878d;
					margin-right: 40rpx;
				}
			}

			image {
				width: 32rpx;
				height: 32rpx;
				margin-left: 40rpx;
			}
		}
	}
}

.bottom {
	height: 168rpx;
	box-sizing: border-box;
	padding: 0 32rpx 50rpx;
	background-color: #fff;
	border-radius: 32rpx 32rpx 0rpx 0rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 -8rpx 32rpx rgba(0,0,0,.12);

	.bottomLeft {
		margin-left: 40rpx;
		display: flex;
		align-items: center;
		font-size: 32rpx;
		color: #1f1f1f;

		image {
			width: 48rpx;
			height: 48rpx;
			margin-right: 8rpx;
		}
	}

	.bottomRight {
		margin-right: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 256rpx;
		height: 80rpx;
		background: #fe58b7;
		border-radius: 40rpx;
		color: #fff;
		// font-weight: 500;
		font-size: 36rpx;
	}
}

.overlayShowBottom {
	
}
</style>
