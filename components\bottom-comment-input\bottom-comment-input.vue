<template>
  <view class="container" @click.stop>
    <view class="input-wrapper">
      <view class="toolbar" :class="{ focus: isFocus }">
        <view class="textarea-wrapper" style="flex: 1 1 auto;">
          <textarea :adjust-position="false" :maxlength="-1" :auto-focus="autoFocus" :placeholder="placeholder"
            @keyboardheightchange="onKeyboardHeightChange" v-model="comment" :show-confirm-bar="false" @focus="onFocus"
            @blur="onBlure"></textarea>
        </view>


        <view class="toolbar-btn-group" :class="{ focus: isFocus }">
          <image class="icon" src="/static/icon/dimension/picture.png" @click="onUploadImage"></image>
          <image class="icon" src="/static/icon/dimension/emoji.png" @click="showEmoji = !showEmoji"></image>
        </view>
      </view>

      <view class="btn-submit" :class="{ active: showSendBtn }" @click="sendComment">
        <text>发送</text>
      </view>
    </view>

    <view class="image-wrapper" v-if="image">
      <image class="preview" :src="image" @click="onUploadImage"></image>
      <image class="close-icon" src="/static/icon/close-fill.png" @click="image = ''"></image>
    </view>


    <view class="emoji-wrapper" :class="{ active: showEmoji }" @click.stop>
      <view class="scroll">
        <view class="emoji" v-for="(emoji, index) in emojiList" :key="index" @click="() => onEmojiClick(emoji)">
          {{ emoji }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { uploadFile } from '@/api/request'
import emojiList from '/static/emoji/emoji.json'

const props = defineProps({
  autoFocus: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: '说点什么...'
  }
})

const emit = defineEmits('onSubmit', 'keyboardHeightChange', "focus", "blure", "linechange")
const comment = ref('')
const showEmoji = ref(false)
const image = ref('')
const isFocus = ref(false)

const showSendBtn = computed(() => {
  if (comment.value.length > 0 || image.value) return true

  return isFocus.value
})

const onFocus = () => {
  isFocus.value = true
  showEmoji.value = false
  emit("focus")
}
const onBlure = () => {
  isFocus.value = false
  emit("blure")
}

const sendComment = async () => {
  try {
    const commentValue = {
      image: '',
      text: ''
    }
    if (image.value) {
      const res = await uploadFile(image.value)
      if (res.code === 20000) {
        image.value = ''
        commentValue.image = res.data.url
      }
    }
    if (comment.value) {
      commentValue.text = comment.value
      comment.value = ''
    }
    emit('onSubmit', commentValue)
  } catch (error) {
    console.log(error, 'sendComment')
  }
}

const onEmojiClick = (emoji) => {
  comment.value += emoji
}

const onKeyboardHeightChange = (e) => {
  emit('keyboardHeightChange', e.detail.height)
  if (e.detail.height <= 0 && comment.value.length > 0) {
    isFocus.value = true
  }
}

const onUploadImage = (e) => {
  showEmoji.value = false
  uni.chooseImage({
    count: 1,
    success: (res) => {
      image.value = res.tempFilePaths[0]
    }
  })
}
</script>

<style scoped lang='scss'>
.container {
  width: 100%;
  height: 100%;
  padding: 24rpx 32rpx;
  box-sizing: border-box;

  .input-wrapper {
    width: 100%;
    display: flex;

    .btn-submit {
      width: 0rpx;
      height: 80rpx !important;
      line-height: 80rpx !important;
      border-radius: 32rpx;
      text-align: center;
      align-self: flex-end;
      transition: width .3s;

      &.active {
        width: 136rpx;
      }
    }
  }

  .toolbar {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 32rpx;
    box-sizing: border-box;
    background-color: #F3F3F3;
    border-radius: 40rpx;
    padding-right: 40rpx;
    height: 80rpx;

    &.focus {
      height: fit-content;
      flex-direction: column;
      align-items: flex-start;
    }

    .textarea-wrapper {
      width: 100%;
      font-size: 32rpx;

      &>textarea {
        box-sizing: border-box;
        padding-top: 16rpx;
        padding-left: 16rpx;
        width: 100%;
        height: 100%;
        min-height: 80rpx;
        max-height: 320rpx;
      }

      &>textarea:placeholder {
        font-weight: 400;
        color: #85878d;
      }
    }

    .toolbar-btn-group {
      display: flex;
      flex: 0 0 auto;
      align-items: center;
      gap: 32rpx;

      &.focus {
        width: 100%;
        justify-content: flex-end;
      }

      .icon {
        width: 56rpx;
        height: 56rpx;
      }
    }
  }

  .emoji-wrapper {
    position: relative;
    margin-top: 24rpx;
    height: 0;
    transition: height .3s;
    opacity: 0;

    &.active {
      opacity: 1;
      height: 400rpx;
      padding-bottom: 24rpx;
    }
  }

  .scroll {
    display: flex;
    flex-wrap: wrap;
    gap: 24rpx;
    height: 100%;
    padding: 24rpx;
    box-sizing: border-box;
    background-color: #f4f4f4;
    border-radius: 32rpx;
    font-size: 40rpx;
    overflow-y: auto;
  }

  .emoji-btn-group {
    position: absolute;
    display: flex;
    right: 32rpx;
    bottom: 32rpx;
    gap: 20rpx;
    font-size: 12px;

    &>button {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0px 5px;
      color: white;
      text-align: center;
      width: 40px;
    }

    .back>image {
      height: 16px;
      width: 16px;
    }

  }

  .footer {
    margin-top: 28rpx;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>view {
      gap: 56rpx;
      align-items: center;
      flex: 1;

      .icon {
        width: 56rpx;
        height: 56rpx;
      }
    }
  }

  .image-wrapper {
    margin-top: 24rpx;
    width: 128rpx;
    height: 128rpx;
    position: relative;

    .preview {
      width: 100%;
      height: 100%;
      border-radius: 16rpx;
    }

    .close-icon {
      position: absolute;
      top: -10rpx;
      right: -10rpx;
      width: 32rpx;
      height: 32rpx;
      background-color: #fff;
      border-radius: 50%;
    }
  }

  .btn {
    width: 136rpx;
    height: 64rpx;
    background: #FE58B7;
    border-radius: 32rpx;
    line-height: 64rpx;
    text-align: center;
    color: #fff;
    font-size: 32rpx;
  }
}
</style>