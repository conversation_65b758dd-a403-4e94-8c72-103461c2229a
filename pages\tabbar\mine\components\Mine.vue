<template>
	<view class="app-container">
		<view class="bg-wrap">
			<image class="bg-img"
				:src="userInfo?.background_url ? prefixFilePath(userInfo.background_url) : '/static/my/<EMAIL>'"
				mode="widthFix"></image>
			<view class="bg-mask" :style="{ opacity: isTopHeight ? 0 : 1 }" />
		</view>
		<z-paging ref="pagePaging" refresher-only @onRefresh="onRefresh" @scrolltolower="scrolltolower"
			@scroll="scroll">
			<template #top>
				<uni-nav-bar v-if="!isNoTabbarPages" :fixed="true" backgroundColor="rgba(255,255,255,0)" :border="false"
					:statusBar="true" />
				<Navbar v-else :type="1" backgroundColor="transparent" />
			</template>
			<view class="topHeigt" :style="{ height: isTopHeight ? topHeight : defTopHeight }" @click="onBg" />
			<view class="my-header">
				<view class="info">
					<view class="flex items-center">
						<text class="user-name" v-if="userInfo.token">
							{{ userInfo?.nickname ? userInfo.nickname : '用户' + userInfo.uid }}
						</text>
						<!-- <text class="user-name" v-else="userInfo.token" @tap="handleLogin">点击登录</text> -->
						<text class="user-name" v-else="userInfo.token" @tap="toLogin">点击登录</text>
						<!-- <uni-icons class="ml-10" :size="20" color="#AAAAAA" type="arrowright" /> -->
					</view>
					<view class="user-info">
						<view>
							<text>UID: {{ userInfo.uid || '-' }}</text>
							<view v-if="userInfo.token" class="qrcodeBtn" @click="QRcode">
								<image style="width: 40rpx; height: 40rpx;" src="/static/icon/qrcode.png" mode="">
								</image>
							</view>
						</view>
						<text class="mt-20">{{ userInfo.bio || '这家伙什么都没留下~' }}</text>
					</view>
					<!-- <view class="emblem-mx">
						<image class="icon-mx" src="/static/my/<EMAIL>" />
						<text class="emblem-mx-txt">萌新</text>
					</view> -->
					<view class="gender">
						<image v-if="userInfo.gender === 1" class="gender-icon" src="/static/my/<EMAIL>" />
						<image v-if="userInfo.gender === 2" class="gender-icon" src="/static/my/<EMAIL>" />
						<view class="addr" v-if="!!userInfo.city">
							<text>{{ userInfo.city.name }}{{ userInfo.district ? userInfo.district.name : '' }}</text>
						</view>
					</view>
				</view>

				<view class="avatar-bar">
					<uv-avatar :src="userInfo.avatar ? prefixFilePath(userInfo.avatar) : null" size="176rpx"
						mode="aspectFill" default-url="/static/Default_Avatar_boy3.png"></uv-avatar>
					<!-- <view class="no-info" v-if="userInfo.token">
						<text>完善资料</text>
					</view> -->
				</view>
			</view>
			<view class="box-info">
				<view class="info-data">
					<view class="flex flex-1 flex-col items-center" @tap="onFollow">
						<text class="num">{{ userInfo.follow_count || 0 }}</text>
						<text class="name">关注</text>
					</view>
					<view class="flex flex-1 flex-col items-center" @tap="onFans">
						<text class="num">{{ userInfo.fans_count || 0 }}</text>
						<text class="name">粉丝</text>
					</view>
					<view class="flex flex-1 flex-col items-center">
						<text class="num">{{ userInfo.thumbs_up_count || 0 }}</text>
						<text class="name">获赞</text>
					</view>
					<view class="flex flex-1 flex-col items-center" @click="toFriend">
						<text class="num">{{ userInfo.friend_count || 0 }}</text>
						<text class="name">好友</text>
					</view>
				</view>
				<view class="flex">
					<view class="edit" v-if="userInfo.token" @tap="toMyInfo">
						<text>编辑资料</text>
					</view>
					<view class="setting" @tap="toSetting">
						<image class="icon-setting" src="/static/my/<EMAIL>" />
					</view>
				</view>
			</view>

			<view class="my-order">
				<view v-for="(item, index) in quickEntry" :key="item.value"
					:class="`quick-entry-item quick-entry-item-${index}`" :style="{ opacity: item.disabled ? 0.5 : 1 }"
					@click="() => handleTo(item)">
					<image :src="item.icon"></image>
					<text class="text">{{ item.name }}</text>

					<!--<view class="my-order-hd">
					<text class="title">我的订单</text>
					<view class="more" @click="toOrder">
						<text class="more-txt">查看全部</text>
						<image class="icon-jt" src="/static/my/<EMAIL>" />
					</view>-->
				</view>
			</view>

			<view class="my-tabs">
				<view class="tabs-container" style="z-index: 100; position: sticky; top: 0">
					<dust-tabs v-model="current" :list="tabList" tabType="primary"></dust-tabs>

					<view class="tag-container" v-if="tableTags.length">
						<view class="tag" v-for="item in tableTags" :key="item.value"
							:class="{ 'selected': item.checked }" @click="tagClick(item)">
							<text>{{ item.lable }}</text>
						</view>

					</view>
					<view class="type-container" v-if="selectedTag">
						<text class="type" v-for="item in selectedTag.types" :key="item.value"
							:class="{ 'selected': item.checked }" @click="tagTypeClick(item)">{{ item.lable }}</text>
					</view>
				</view>
				<swiper class="swiper" :style="[{ height: swiperHeight + 'px' }]" :current="current"
					@transition="swiperTransition" @animationfinish="swiperAnimationfinish">
					<swiper-item class="swiper-item" v-for="(item, index) in tabList" :key="index">
						<my-tab-list-view ref="swiperList" :tabIndex="index" :currentIndex="current"
							:selectedInfo="selectedInfo" @heightChanged="heightChanged"></my-tab-list-view>
					</swiper-item>
				</swiper>
			</view>

			<uv-popup ref="qrCodePopupRef">
				<view class="qrcode-wrap" v-if="!isDrawing">
					<image :src="canvasQRCodeURL" style="width: 100vw;" lazy-load mode="widthFix" show-menu-by-longpress
						@tap="closeQrCodePopup"></image>
				</view>
			</uv-popup>

			<template #bottom v-if="!isNoTabbarPages">
				<cc-myTabbar :tabBarShow="4"></cc-myTabbar>
			</template>
		</z-paging>
		<quick-login-dialog></quick-login-dialog>
		<!-- 隐藏的 Canvas -->
		<canvas id="myCanvas" canvas-id="myCanvas"
			:style="{ width: canvasWidth + 'px', height: canvasHeight + 'px', position: 'fixed', top: '-9999px' }" />
	</view>
</template>

<script setup>
import { ref, computed, onMounted, toRaw, markRaw, watch, getCurrentInstance } from 'vue';
import { mapState, mapActions, useStore } from 'vuex';
import { myInviteCode, myQRInviteFriendCode } from '@/api/user';
import Config from '@/common/config.js'
import * as auth from '@/common/auth';
import { QUICK_ENTRY } from './const'
const instance = getCurrentInstance(); // 获取当前组件实例
defineProps(['isNoTabbarPages']);
const defTopHeight = `calc(100vw / 750 * 974 - ${uni.getSystemInfoSync().statusBarHeight + 44 + 142 + 46.5 + 40}px)`;
const topHeight = `calc(100vw / 750 * 974 - ${uni.getSystemInfoSync().statusBarHeight + 44}px)`;
const store = useStore();
let swiperHeight = ref(0);
let current = ref(0); // tabs组件的current值，表示当前活动的tab选项
let quickEntry = ref(QUICK_ENTRY);
let tabList = ref([
	{
		name: '次元',
		value: 0,
		tags: []

	},
	{
		name: '相册',
		value: 1,
		tags: []
	},
	{
		name: '赞过',
		value: 2,
		tags: []
	},
	{
		name: '收藏',
		value: 3,
		tags: [
			{
				lable: "服务", value: "3-0", checked: true, types: [
					{ lable: "全部", value: "3-0-0", checked: true },
					{ lable: "约拍", value: "3-0-1", checked: false },
					{ lable: "约妆", value: "3-0-2", checked: false }
				]
			},
			{
				lable: "活动", value: "3-1", checked: false, types: [
					{ lable: "漫展", value: "3-1-0", checked: true },
				]
			},
		]
	}
],)
let initRef = ref(false);
let swiperList = ref([]);
let pagePaging = ref(null);
let inviteCode = ref(null);
let isTopHeight = ref(false);
let qrCodePopupRef = ref(null);
let footerIcons = ref([
	{
		icon: '/static/icon/bc.png',
		text: '保存图片到相册'
	},
	{
		icon: '/static/icon/jt.png',
		text: ''
	},
	{
		icon: '/static/icon/sys.png',
		text: '系统相册'
	}
]);
let canvasQRCodeURL = ref('');
let isDrawing = ref(false); // 防止重复绘制
let canvasWidth = ref(0);
let canvasHeight = ref(0);
let footerBottom = ref('40rpx'); // 动态底部距离

const userInfo = computed(() => store.state.userInfo);
const commonStore = computed(() => store.state.common);
const sysInfo = computed(() => store.state.common.sysInfo);
const tableTags = computed(() => tabList.value[current.value]?.tags || []);
const selectedTag = computed(() => tableTags.value.find(item => item.checked));
const selectedInfo = computed(() => {
	const result = { tag: "", type: "" };
	const selected = selectedTag.value;
	if (!selected) return result;

	result.tag = selected.value;
	const selectedType = selected.types.find(item => item.checked);
	selectedType && (result.type = selectedType.value);
	console.log("选中的tag", result)
	return result;
});

const handleTo = (item) => {
	let url = '';
	if (item.value === 0) {
		url = '/pages/packageA/order/order';
	}
	if (item.value === 1) {
		url = '/pages/packageA/wallet/wallet';
	}
	if (item.value === 3) {
		url = '/pages/packageA/service/service';
	}

	url &&
		uni.navigateTo({
			url
		});
}
const showYQ = () => {
	myInviteCode().then((res) => {
		if (res.code == 20000) {
			inviteCode.value = res.data.invite_code;
		}
	});
}
const QRcode = () => {
	if (userInfo.value?.qrcode_url) {
		generateImage()
		return;
	}

	const params = {
		page: 'pages/tabbar/home/<USER>',
		scene: `inviteCode=${inviteCode.value}`,
		check_path: false,
		env_version: Config.envVersion
	};

	myQRInviteFriendCode(params, true)
		.then(async res => {
			await store.dispatch('userInfo/getUserInfo')
			qrCodePopupRef.value.open()
			generateImage()
		})
		.catch(err => {
			console.error('生成二维码失败:', err);
			uni.showToast({
				title: '生成二维码失败',
				icon: 'none'
			});
		});
}
const closeQrCodePopup = () => {
	qrCodePopupRef.value.close()
}
//跳转好友页
const toFriend = () => {
	uni.navigateTo({
		url: '/pages/tabbar/notification/contacts'
	});
}
const replacePhoneNumber = (phoneNumber) => {
	// 获取手机号的后四位
	const lastFourDigits = phoneNumber.slice(-4);
	return '用户' + lastFourDigits;
}
const handleLogin = () => {
	uni.$emit('changeLogin', true);
}
const tagClick = (tagItem) => {
	if (tagItem.checked) return;

	tagItem.checked = true;
	tagItem.types.forEach((typeItem, index) => {
		if (index === 0) return typeItem.checked = true;
		typeItem.checked = false;
	});

	tabList.value.forEach(item => item.tags.forEach(inner => {
		if (inner.value === tagItem.value) return;
		inner.checked = false;
		inner.types.forEach(typeItem => typeItem.checked = false);
	}));
	swiperList.value[current.value]?.reload();
}
const tagTypeClick = (typeItem) => {
	if (typeItem.checked) return;
	tabList.value.forEach((item, index) => {
		item.tags.forEach(inner => inner.types.forEach(tempItem => tempItem.checked = tempItem.value === typeItem.value));
	});
	swiperList.value[current.value]?.reload();
}
const _setCurrent = (currents) => {
	if (currents !== current.value) {
		//切换tab时，将上一个tab的数据清空
		swiperList.value[current.value]?.clear();
	}
	current.value = currents;
}
// 下拉刷新时，通知当前显示的列表进行reload操作
const onRefresh = () => {
	swiperList.value[current.value]?.reload(() => {
		//当当前显示的列表刷新结束，结束当前页面的刷新状态
		pagePaging.value.endRefresh();
	});
	store.dispatch('userInfo/getUserInfo')
}
// 当滚动到底部时，通知当前显示的列表加载更多
const scrolltolower = () => {
	swiperList.value[current.value]?.doLoadMore();
}
// swiper滑动中
const swiperTransition = (e) => {
	// this.$refs.tabs.setDx(e.detail.dx);
}
// swiper滑动结束
const swiperAnimationfinish = (e) => {
	_setCurrent(e.detail.current);
	// this.$refs.tabs.unlockDx();
}
// 设置swiper的高度
const heightChanged = (height) => {
	if (height === 0) {
		// 默认swiper高度为屏幕可用高度-tabsView高度-slot="top"内view的高度
		// 注意：uni.upx2px(80)不是固定的，它等于slot="top"内view的高度，如果slot="top"内view的高度不为80rpx，则需要修改这个值
		height = uni.getSystemInfoSync().windowHeight - uni.upx2px(700);
	}
	swiperHeight.value = height
}
const toLogin = () => {
	uni.navigateTo({
		url: '/pages/packageA/login-pwd/login-pwd',
		backUrl: '/pages/tabbar/mine/mine'
	});
}
const toSetting = () => {
	uni.navigateTo({
		url: '/pages/packageA/my-settings/my-settings'
	});
}
const toMyInfo = () => {
	uni.navigateTo({
		url: '/pages/packageA/my-info/my-info'
	});
}
const onFollow = () => {
	uni.navigateTo({
		url: '/pages/tabbar/notification/newFriend?tab=1'
	})
};
const onFans = () => {
	uni.navigateTo({
		url: '/pages/tabbar/notification/newFriend?tab=2'
	})
};
const prefixFilePath = (url) => {
	if (!url) return '';
	return commonStore.value.cloudFileUrl + url;
}
// 点击蒙版
const onBg = () => {
	isTopHeight.value = !isTopHeight.value;
	pagePaging.value.scrollToTop();
}
// 监听滚动
const scroll = (e) => {
	console.log('监听滚动', e.detail.scrollTop);
	if (e.detail.scrollTop > 100 && isTopHeight.value) {
		isTopHeight.value = false
		pagePaging.value.scrollToTop();
	}
}
const generateImage = async () => {
	uni.showLoading({
		title: '二维码生成中...'
	})
	if (isDrawing.value) {
		uni.hideLoading()
		return
	};
	isDrawing.value = true;
	try {
		const ctx = uni.createCanvasContext('myCanvas', instance);
		console.log('ctx:', ctx)
		await drawContent(ctx); // 绘制内容
		const imagePath = await saveCanvasToImage(); // 保存为图片
		console.log('imagePathimagePath:', imagePath)
		canvasQRCodeURL.value = imagePath
		uni.hideLoading()
		qrCodePopupRef.value.open()
	} catch (error) {
		uni.hideLoading()
		console.error('生成失败:', error);
	}
	isDrawing.value = false;
}
// 核心绘制逻辑
const drawContent = async (ctx) => {
	const rpxRatio = sysInfo.value.windowWidth / 750;
	// 1. 获取二维码实际尺寸
	const qrPath = await getImagePath(prefixFilePath(userInfo.value.qrcode_url));
	const qrSize = await calculateImageSize(qrPath, canvasWidth.value);
	canvasWidth.value = qrSize.width;
	canvasHeight.value = qrSize.height
	// 清除可能的历史绘制
	// ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value);
	// 初始化画布
	ctx.drawImage(qrPath, 0, 0, qrSize.width, qrSize.height);
	console.log('qrPath:', qrSize.width, qrSize.height)
	// 2. 绘制底部栏
	const footerHeight = 190 / 2;
	const _footerBottom = (footerBottom.value.includes('130') ? 130 : 40) / 2;
	const footerY = canvasHeight.value / 2 + _footerBottom + footerHeight
	// 半透明背景
	ctx.setFillStyle('rgba(255, 255, 255, 0.5)');
	ctx.fillRect(0, footerY, canvasWidth.value, footerHeight);
	// 3. 绘制图标和文字
	const icons = [
		'/static/icon/bc.png',
		'/static/icon/jt.png',
		'/static/icon/sys.png'
	];

	// 等待所有图标加载完成（关键）
	const iconPaths = await Promise.all(
		icons.map(async (src) => {
			const res = await uni.getImageInfo({
				src
			});
			return res.path;
		})
	);
	const iconSize = 80 / 2;
	const spacing = calculateItemSpacing(iconSize);
	const startY = footerY + 20 / 2;
	// 遍历绘制三个元素
	icons.forEach((icon, index) => {
		ctx.save(); // 保存上下文状态
		const x = spacing * (index + 1) + iconSize * index;
		// 绘制图标
		try {
			ctx.drawImage(icon, x, startY, iconSize, iconSize);
		} catch (e) {
			console.error(`图标${index}绘制失败:`, e);
		}
		// 绘制文字
		if (index !== 1) {
			// 文字样式配置
			ctx.beginPath();
			ctx.setFillStyle('#1f1f1f'); // 深灰色文字
			ctx.font = `normal normal ${32 / 2}px sans-serif`; // 完整字体定义
			ctx.setTextAlign('center');
			ctx.setTextBaseline('top');
			// 计算文字位置
			const textX = x + iconSize / 2;
			const textY = startY + iconSize / 2 + 35;
			// 边界检查
			if (textY > canvasHeight.value - 10) {
				console.warn('文字位置接近画布边缘');
			}
			ctx.fillText(
				index === 0 ? '保存图片到相册' : '打开微信扫一扫',
				textX,
				textY
			);
			ctx.closePath();
		}
		ctx.restore(); // 恢复上下文状态
	});
	// 执行绘制
	await new Promise(resolve => ctx.draw(false, resolve));
}
// 工具方法：获取图片路径（兼容网络/本地）
const getImagePath = async (src) => {
	if (src.startsWith('http')) {
		const res = await uni.downloadFile({
			url: src
		});
		return res.tempFilePath;
	} else {
		const res = await uni.getImageInfo({
			src
		});
		return res.path;
	}
}
// 工具方法：计算图片适配尺寸
const calculateImageSize = async (imgPath, maxWidth) => {
	// 获取图片原始尺寸
	const {
		width: originWidth,
		height: originHeight
	} = await uni.getImageInfo({
		src: imgPath
	});

	// 计算等比缩放后的尺寸
	const aspectRatio = originHeight / originWidth;
	const targetWidth = Math.min(maxWidth, originWidth); // 不超过原始尺寸
	const targetHeight = Math.round(targetWidth * aspectRatio);

	return {
		width: targetWidth,
		height: targetHeight,
		originWidth, // 保留原始尺寸用于调试
		originHeight
	};
}
// 工具方法：计算元素间距（space-evenly 效果）
const calculateItemSpacing = (iconSize) => {
	const totalWidth = 3 * iconSize;
	return (canvasWidth.value - totalWidth) / 4;
}
// 保存 Canvas 为图片
const saveCanvasToImage = async () => {
	return new Promise((resolve, reject) => {
		uni.canvasToTempFilePath({
			canvasId: 'myCanvas',
			success: res => resolve(res.tempFilePath),
			fail: reject
		}, instance);
	});
}

watch(() => current.value, (newVal) => {
	_setCurrent(newVal)
}, { immediate: true });
watch(() => userInfo.value.token, (newVal) => {
	if (!newVal) {
		swiperList.value[current.value]?.clearData();
	}
}, { deep: true });
watch(() => commonStore.value.adminSetting, (newVal) => {
	if (newVal?.isAudit === '1') {
		quickEntry.value = [{
			name: '我的订单',
			value: 0,
			icon: '/static/my/<EMAIL>',
			disabled: false
		},
		{
			name: '我的钱包',
			value: 1,
			icon: '/static/my/<EMAIL>',
			disabled: false
		},
		{
			name: '我的服务',
			value: 3,
			icon: '/static/my/<EMAIL>',
			disabled: false
		}
		];
	} else {
		quickEntry.value = QUICK_ENTRY;
	}
}, { immediate: true, deep: true });

onMounted(() => {
	store.dispatch('common/getAdminSetting')
	const token = auth.getToken();
	if (!!token) {
		showYQ();
	}
	canvasWidth.value = sysInfo.value.windowWidth
	canvasHeight.value = sysInfo.value.windowHeight
	footerBottom.value = sysInfo.value.screenHeight >= 812 ? '130rpx' : '40rpx'
})
</script>

<style lang="scss" scoped>
.bg-wrap {
	position: absolute;
	z-index: 0;
	top: 0;
	left: 0;
	right: 0;
	width: 100vw;

	.bg-img {
		width: 100vw;
	}

	.bg-mask {
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		width: 100%;
		transition: all 0.5s;
		background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.7) 54%, #FFFFFF 100%);
	}
}

.topHeigt {
	//动画
	transition: all 0.5s;
}

.my-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	padding: 28rpx 48rpx 0 40rpx;

	.user-name {
		color: #1f1f1f;
		font-weight: 700;
		font-size: 48rpx;
	}

	.info {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
	}

	.user-info {
		position: relative;
		display: flex;
		flex-direction: column;
		font-size: 28rpx;
		color: rgba(31, 31, 31, 0.7);
		line-height: 28rpx;
		padding-top: 20rpx;

		view {
			display: flex;
			align-items: center;

			.qrcodeBtn {
				padding-left: 20rpx;
				// margin-left: 24rpx;
				// background: rgba(255, 255, 255, 0.6);
				// border: 1rpx solid rgba(255, 255, 255, 0.5);
				// border-radius: 48rpx;
				// display: flex;
				// align-items: center;
				// justify-content: center;
				// height: 40rpx;
				// padding: 0 14rpx;
				// font-size: 20rpx;
				// color: #4e5969;
			}
		}
	}

	.mt-20 {
		margin-top: 20rpx;
	}

	.gender {
		display: flex;
		align-items: center;
		margin-top: 28rpx;
	}

	.gender-icon {
		width: 48rpx;
		height: 48rpx;
	}

	.addr {
		background: rgba(255, 255, 255, 0.6);
		border: 1rpx solid rgba(255, 255, 255, 0.5);
		border-radius: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 48rpx;
		padding: 0 18rpx;
		font-size: 24rpx;
		color: #4e5969;
		margin-left: 18rpx;
	}
}

.emblem-mx {
	position: relative;
	margin-top: 20rpx;

	.icon-mx {
		width: 104rpx;
		height: 48rpx;
	}

	.emblem-mx-txt {
		color: #ffffff;
		font-weight: 700;
		font-size: 20rpx;
		text-shadow: 0px 2px 2px rgba(192, 138, 8, 0.5);
		position: absolute;
		top: 8rpx;
		right: 20rpx;
	}
}

.info-data {
	display: flex;
	align-items: center;
	// gap: 80rpx;
	gap: 70rpx;

	.num {
		font-weight: 700;
		font-size: 40rpx;
		color: #3d3d3d;
	}

	.name {
		font-weight: 500;
		font-size: 24rpx;
		color: #3d3d3d;
		margin-top: 10rpx;
	}
}

.box-info {
	display: flex;
	align-items: center;
	justify-content: space-around;
	// gap: 40rpx;
	margin-top: 30rpx;

	.edit {
		background: rgba(255, 255, 255, 0.6);
		border: 1rpx solid rgba(255, 255, 255, 0.5);
		border-radius: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 48rpx;
		padding: 0 18rpx;
		font-size: 24rpx;
		color: #4e5969;
		margin-left: 18rpx;
	}

	.setting {
		margin-left: 32rpx;
	}

	.icon-setting {
		width: 56rpx;
		height: 56rpx;
	}
}

.avatar-bar {
	border: 2rpx solid #fff;
	border-radius: 1000px;
	position: relative;

	.no-info {
		background-color: #1f1f1f;
		border-radius: 24rpx;
		color: #fff;
		font-weight: 500;
		font-size: 20rpx;
		position: absolute;
		left: 50%;
		bottom: -24rpx;
		transform: translateX(-50%);
		display: flex;
		align-items: center;
		justify-content: center;
		width: 116rpx;
		height: 48rpx;
	}
}

.my-order {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 24rpx;
	padding: 0 24rpx;
	margin-top: 45rpx;
	overflow-x: auto;

	.quick-entry-item {
		border-radius: 32rpx;
		flex: 1 0 208rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.text {
			color: #1f1f1f;
			font-size: 28rpx;
		}

		image {
			width: 48rpx;
			height: 48rpx;
			margin-right: 8rpx;
		}
	}

	.quick-entry-item-0 {
		background: rgba(253, 249, 226, 1);
	}

	.quick-entry-item-1 {
		background: rgba(247, 253, 224, 1);
	}

	.quick-entry-item-2,
	.quick-entry-item-3 {
		background: rgba(253, 226, 242, 1);
	}
}

.tabs-container {
	background: #ffffff;
	border-radius: 32rpx 32rpx 0rpx 0rpx;
	padding: 32rpx;
	position: relative;
	z-index: 1;
}

.my-tabs {
	background-color: #fff;
	border-radius: 32rpx 32rpx 0rpx 0rpx;
	box-shadow: 0rpx -8rpx 24rpx 0rpx rgba(78, 89, 105, 0.1);
	margin-top: 22rpx;
}

.tag-container {
	display: flex;
	gap: 32rpx;
	margin-top: 44rpx;
	font-size: 28rpx;
	font-weight: 600;

	.tag {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 56rpx;
		width: 112rpx;
		border-radius: 28rpx;
		background-color: rgba(247, 248, 250, 1);
		border: 2rpx solid rgba(247, 248, 250, 1);
		box-sizing: border-box;
	}

	.tag.selected {
		color: rgba(72, 218, 234, 1);
		border-color: rgba(72, 218, 234, 1);
		background-color: white;
	}
}

.type-container {
	display: flex;
	gap: 48rpx;
	margin-top: 44rpx;
	font-size: 28rpx;
	font-weight: 600;
	color: rgba(133, 135, 141, 1);

	.type.selected {
		color: rgba(31, 31, 31, 1);
	}
}

.swiper {
	height: 100px;
}

.qrcode-wrap {
	position: relative;
}

.qrcode-footer {
	background-color: rgba(255, 255, 255, 0.5);
	width: 100vw;
	height: 190rpx;
	position: fixed;
	bottom: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-evenly;
}

.qrcode-footer-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.icon-bc {
	width: 80rpx;
	height: 80rpx;
}

.qrcode-footer-text {
	font-size: 32rpx;
	color: #1F1F1F;
}
</style>