<template>
  <z-paging class="app-container" ref="paging" fixed v-model="dataList" @query="queryFn" :safe-area-inset-bottom="true"
    :use-safe-area-placeholder="true">
    <template #top>
      <Navbar :type="1" leftText="所有评论"></Navbar>
    </template>

    <view class="comment-container">
      <view class="header">
        <view class="title">
          <text>评论</text>
          <text>({{ allCommentsCount || 0 }})</text>
        </view>
      </view>
      <uni-list class="comment-list" :border="false">
        <uni-list-item class="dust-uni-list-item" v-for="comment in formatComments" :key="comment.id" :border="false">
          <template v-slot:body>
            <comment-block :item="comment" :show-footer="true" @on-more="() => handleMoreAction(comment.id)"
              @on-reply="() => handleReply(comment)" />

            <view v-if="comment.children && comment.children.results && comment.children.results.length > 0"
              class="children-comments">
              <view v-for="child in comment.children.results" :key="child.id" class="child-comment">
                <comment-block :item="child" :show-footer="true" @on-more="() => handleMoreAction(child.id)"
                  @on-reply="() => handleReply(child, comment.id)" />
              </view>
              <view v-if="comment.children.count > comment.children.results.length" class="load-more"
                @click="loadMoreReplies(comment.id)">
                <text>查看更多回复</text>
              </view>
            </view>
          </template>
        </uni-list-item>
      </uni-list>
    </view>

    <uv-popup ref="popup" mode="bottom" bgColor="none">
      <!-- <view class="popup-wrapper" v-if="popupType === 'more'">
      <view class="status-item red" @click="">删除评论</view>
      <view class="status-item" @click="closePopup">取消</view>
    </view> -->
      <Reply v-if="popupType === 'reply'" :reply-parent-id="replyParentId" :reply-comment="currentComment"
        :expo-id="announcementId" @submitCb="closePopup" @refreshComments="handleRefreshComments" />
    </uv-popup>

    <template #bottom>
      <view class="bottom-wrapper" :style="{ paddingBottom: keyboardHeight + 'px' }">
        <bottom-comment-input @keyboardHeightChange="keyboardHeightChange" @onSubmit="handleSubmitComment" />
      </view>
    </template>
  </z-paging>
</template>
<!-- 参考 pages/packageA/exhibition-comment/exhibition-comment.vue实现 有变动-->
<script setup lang="ts">
import { ref, computed } from 'vue'
import { onLoad } from "@dcloudio/uni-app";
import { useStore } from 'vuex'
import * as auth from '@/common/auth';
import {
	getAnnouncementComments,
	publishAnnouncementComment,
} from "@/api/announcement";
import Reply from './components/reply.vue'

const store = useStore()
const dataList = ref([])
const allCommentsCount = ref(0)
const paging = ref(null)

const keyboardHeight = ref(0)
const announcementId = ref("");
onLoad((query: {id: string}) => {
	if (!query.id) return uni.showToast({
		icon: "error",
		title: "跳转时参数错误"
	});
	announcementId.value = query.id;
});
const keyboardHeightChange = (height) => {
  keyboardHeight.value = height
}

const queryFn = async (page) => {
	const token = auth.getToken();
	if (!token) {
		paging.value.complete([])
		return
	}
  try {
    const res = await getAnnouncementComments(+announcementId.value, {
      page,
      size: 10
    })
    if (res.code === 20000) {
      paging.value.complete(res.data.results)
      allCommentsCount.value = res.data.count
    }
  } catch (error) {
    console.log(error, 'getExhibitionComments')
  }
}

const formatComments = computed(() => {
  const formatCommentItem = (item) => {
    const isJson = item.editor === 'json'
    const content = isJson && item.content ? JSON.parse(item.content) : item.content
    const comment = isJson ? content.text : content
    const image = isJson ? content.image : ''
    return {
      ...item,
      avatar: item.user_avatar,
      name: item.user_nickname,
      comment,
      time: formatTime(item.created_at * 1000),
      image
    }
  }

  const list = dataList.value?.map(item => {
    const formattedItem = formatCommentItem(item)

    // 格式化子评论
    if (item.children && item.children.results && item.children.results.length > 0) {
      formattedItem.children = {
        ...item.children,
        results: item.children.results.map(child => formatCommentItem(child))
      }
    }

    return formattedItem
  })

  return list
})

const handleSubmitComment = async (comment) => {
  // comment: { image: '', text: '' }
  try {
    const res = await publishAnnouncementComment(+announcementId.value, {
      editor: 'json',
      content: JSON.stringify(comment),
      parent: 0
    })
    if (res.code === 20000) {
      // 确保重置到第一页，并且清空之前的数据
      paging.value.reload()
    }
  } catch (error) {
    console.log(error)
  }
}

const loadMoreReplies = async (commentId) => {
  // 查找当前父评论
  const parentComment = dataList.value?.find(item => item.id === commentId)

  if (!parentComment || !parentComment.children) return

  try {
    // TODO：更多子评论分页未处理
    const res = await getAnnouncementComments(+announcementId.value, {
      parent: commentId,
      page: 1,
      size: 10
    })
    if (res.code === 20000) {
      const comment = dataList.value.find(item => item.id === commentId)
      if (comment) {
        // 确保comment.children存在
        if (!comment.children) {
          comment.children = { results: [], count: 0 }
        }

        // 合并新的回复并去重
        const existingIds = new Set(comment.children.results.map(item => item.id))
        const newReplies = res.data.results.filter(item => !existingIds.has(item.id))

        // 更新评论中的子评论
        comment.children = {
          ...comment.children,
          results: comment.children.results.concat(newReplies),
          count: res.data.count
        }
      }
    }
  } catch (error) {
    console.log(error, ' fetchExpoCommentReplies')
  }
}

const handleRefreshComments = (data) => {
  if (data.type === 'all') {
    // 刷新所有评论，使用reload而不是refresh
    paging.value.reload()
  } else if (data.type === 'reply' && data.parentId) {
    // 刷新特定父评论的子评论
    loadMoreReplies(data.parentId)
  }
}

/** 回复评论，删除?评论 */
const popup = ref(null)
const popupType = ref('')
const currentComment = ref(null)
const replyParentId = ref(null)
function handleMoreAction(id) {
  popupType.value = 'more'
  popup.value.open()
}

function handleReply(comment, parentId?: number) {
  currentComment.value = comment
  if (parentId) {
    replyParentId.value = parentId
  }
  popupType.value = 'reply'
  popup.value.open()
}

function closePopup() {
  popup.value.close()
  popupType.value = ''
  replyParentId.value = null
}

/**
 * 格式化时间戳
 * @param {number} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 * 今天：hh:mm
 * 今年非今天：MM月DD日 hh:mm
 * 非今年：YYYY年MM月DD日 hh:mm
 */
function formatTime(timestamp) {
  if (!timestamp) return '';

  const date = new Date(timestamp);
  const now = new Date();

  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  const nowYear = now.getFullYear();
  const nowMonth = now.getMonth() + 1;
  const nowDay = now.getDate();

  // 判断是否是今天
  if (year === nowYear && month === nowMonth && day === nowDay) {
    return `${hours}:${minutes}`;
  }
  // 判断是否是今年
  else if (year === nowYear) {
    return `${month}月${day}日 ${hours}:${minutes}`;
  }
  // 非今年
  else {
    return `${year}年${month}月${day}日 ${hours}:${minutes}`;
  }
}
</script>

<style scoped lang='scss'>
.comment-container {
  box-sizing: border-box;
  padding: 0 24rpx;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      display: flex;
      align-items: center;
      gap: 12rpx;
      font-size: 28rpx;
      color: #3D3D3D;
    }

    .action {
      font-size: 28rpx;
      color: #48DAEA;
    }

  }
}

.comment-container {
  margin-top: 32rpx;
  box-sizing: border-box;
  padding: 0 24rpx;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      display: flex;
      align-items: center;
      gap: 12rpx;
      font-size: 28rpx;
      color: #3D3D3D;
    }

    .action {
      font-size: 28rpx;
      color: #48DAEA;
    }

  }

  .comment-list {
    margin-top: 24rpx;

    .dust-uni-list-item {
      margin: 24rpx 0;
      padding-bottom: 24rpx;
      border-bottom: 1px solid #F4F4F4;
    }

    .children-comments {
      box-sizing: border-box;
      padding-left: 88rpx;
      margin-top: 24rpx;
      display: flex;
      flex-direction: column;
      gap: 24rpx;

      .load-more {
        font-size: 28rpx;
        color: #48DAEA;
      }
    }
  }
}

.popup-wrapper {
  width: 100%;
  min-height: 220rpx;
  overflow-y: auto;
  box-sizing: border-box;
  padding: 24rpx;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 24rpx;

  .status-item {
    width: 100%;
    text-align: center;
    padding-bottom: 24rpx;
    border-bottom: 2rpx solid #EEEEEE;
    font-size: 32rpx;
    color: #1F1F1F;

    &.red {
      color: $uni-color-error;
    }

    &:last-child {
      border: none;
    }
  }
}
</style>