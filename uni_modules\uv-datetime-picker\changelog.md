## 1.0.15（2024-06-14）
1. 修复上次更改引出的BUG
## 1.0.14（2024-05-31）
1. 修复设置maxDate后存在选择不准确的BUG
## 1.0.13（2024-03-22）
1. 修复VUE3中出现的BUG
## 1.0.12（2023-11-27）
1. 增加round圆角属性
## 1.0.11（2023-10-11）
1. 修复设置minDate出现选择错乱的BUG
## 1.0.10（2023-09-01）
1. 增加clearDate参数，是否清除上次选择，默认false
## 1.0.9（2023-08-31）
1. 增加mode="year"，方便只选择年
## 1.0.8（2023-07-17）
1. 优化文档
2. 优化其他
## 1.0.7（2023-07-13）
1. 修复 uv-datetime-picker 设置value属性不生效的BUG 
## 1.0.6（2023-07-05）
修复vue3模式下，动态修改v-model绑定的值无效的BUG
## 1.0.5（2023-07-02）
uv-datetime-picker  由于弹出层uv-popup的修改，打开和关闭方法更改，详情参考文档：https://www.uvui.cn/components/datetimePicker.html
## 1.0.4（2023-06-29）
1. 修复抖音小程序报错的BUG
## 1.0.3（2023-06-07）
1.  取消defaultIndex参数，传该值没实际意义，后续更新文档
## 1.0.2（2023-06-02）
1. 修复v-model重新赋值不更新的BUG
## 1.0.1（2023-05-16）
1. 优化组件依赖，修改后无需全局引入，组件导入即可使用
2. 优化部分功能
## 1.0.0（2023-05-10）
uv-datetime-picker 时间选择器
