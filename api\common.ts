import { requestApi } from './request'

/** 获取省份信息 Result */
interface GetSystemProvinceResult {
	/** 省份 ID */
	id : string,
	/** 省份编码 */
	code : string,
	/** 省份编码全称 */
	full_code : string,
	/** 省份名称 */
	name : string,
	/** 省份名称全称 */
	full_name : string,
	/** 省份简称 */
	abbreviation : string,
	/** 省份拼音/英文 */
	abbr_pinyin : string,
	/** 省份缩写 */
	abbr : string,
}
/** 获取省份信息 */
export const getSystemProvince = () => {
	return requestApi<GetSystemProvinceResult>({
		url: `/api/v1/system/province`,
		method: 'GET',
		isToken: false,
		loading: false,
	})
}
/** 获取省份信息 end */

/** 获取城市信息 Result */
interface GetSystemCityResult {
	/** 城市 ID */
	id : string,
	/** 城市编码 */
	code : string,
	/** 城市编码全称 */
	full_code : string,
	/** 城市名称 */
	name : string,
	/** 城市名称全称 */
	full_name : string,
}
/** 获取城市信息 */
export const getSystemCity = (id: string) => {
	return requestApi<GetSystemCityResult>({
		url: `/api/v1/system/city/${id}`,
		method: 'GET',
		isToken: false,
		loading: false,
	})
}
/** 获取城市信息 end */

/** 获取区县信息 Result */
interface GetSystemDistrictResult {
	/** 区县 ID */
	id : string,
	/** 区县编码 */
	code : string,
	/** 区县编码全称 */
	full_code : string,
	/** 区县名称 */
	name : string,
	/** 区县名称全称 */
	full_name : string,
}
/** 获取区县信息 */
export const getSystemDistrict = (id: string) => {
	return requestApi<GetSystemDistrictResult>({
		url: `/api/v1/system/district/${id}`,
		method: 'GET',
		isToken: false,
		loading: false,
	})
}
/** 获取区县信息 end */

/** 根据 code 获取省市区县信息 Params */
interface GetSystemPcdParams {
	/** 省 code */
	province : string,
	/** 市 city */
	city : string,
	/** 区县 code */
	district : string,
}
/** 根据 code 获取省市区县信息 Result */
interface GetSystemPcdResult {
	/** 省份 ID */
	id : string,
	/** 省份编码 */
	code : string,
	/** 省份编码全称 */
	full_code : string,
	/** 省份名称 */
	name : string,
	/** 省份名称全称 */
	full_name : string,
}
/** 根据 code 获取省市区县信息 */
export const getSystemPcd = (params: GetSystemPcdParams) => {
	return requestApi<GetSystemPcdResult>({
		url: `/api/v1/system/pcd`,
		method: 'GET',
		data: params,
		isToken: false,
		loading: false,
	})
}
/** 根据 code 获取省市区县信息 end */

/** 获取系统临时配置 */
export const getAdminSettingApi = () => {
	return requestApi({
		url: '/api/v1/administrator/setting',
		method: 'GET',
		isToken: false,
		loading: false,
	})
}

/** 敏感操作发送验证码 */
export const smsCodeApi = (phone: string) => {
	return requestApi<{
		code_id: string
	}>({
		url: '/api/v1/sms/code',
		data: { phone },
		loading: false,
	})
}

// 通用搜索
export const getSearchData = (params: {
	keyword: string,
	type: string, // user用户、expo漫展、dimension次元、service服务、subject话题
	page: number,
	size?: number,
}) => {
	const { size = 20, ...other } = params
	return requestApi<{
		users: any[],
		expos: any[],
		services: any[],
	}>({
		url: '/api/v1/foreground/search',
		method: 'GET',
		data: {
			...other,
			size,
		},
		isToken: false,
	})
}