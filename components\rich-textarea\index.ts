// 特殊样式字符串需要用contentCode包裹，便于区分是否是特殊样式字符。
// 开始和结束字符不能交换，当输入框最后为\u200F时聚焦时光标不是在最后
export const WrappedBeginCode = "\u200F";
export const WrappedEndCode = "\u200E";
export function createTextNode(text: string) {
	const result = [];
	for (var index = 0; index < text.length; index++) {
		var item = text[index];
		result.push({name: 'span', children: [{ type: 'text', text: `${item}` }]});
	}
	return result
}

export function createTextStyleNode(text: string, style: string) {
	const result = [];
	for (var index = 0; index < text.length; index++) {
		var item = text[index];
		result.push({
			name: 'span', 
			children: [{ type: 'text', text: `${item}` }],
			attrs: {
				style: style
			},
		});
	}
	return result;
}
// 计算连续文本变化前后的差值[开始，结束)
// 0 1 2 3
// 0 1 2 
export function findChangeRange(oldStr: string, newStr: string) {
	const oldLength = oldStr.length, newLength = newStr.length;
    let start = 0, oldEnd = oldLength - 1, newEnd = newLength - 1;

	let minLen = Math.min(oldEnd, newEnd);
    // 定位首个差异点
    while (start <= minLen && oldStr[start] === newStr[start]) {
        start++;
    }

	// 定位结束差异点
	while (oldEnd >= start && newEnd >= start && oldStr[oldEnd] === newStr[newEnd]) {
		oldEnd--;
		newEnd--;
	}
	
	// 无变化情况
	if (start > oldEnd && start > newEnd) return {};
 // 判断操作类型
	let type;
	if (newLength > oldLength) {
		type = "add";
	} else if (newLength < oldLength) {
		type = "delete";
	} else {
		type = "replace";
	}
	
	// 返回变化详情
	return {
		type,
		start,
		oldEnd: oldEnd + 1,
		newEnd: newEnd + 1,
		oldContent: oldStr.slice(start, oldEnd + 1),
		newContent: newStr.slice(start, newEnd + 1)
	};
}
// 删除时根据特殊格式字符串中找出待删除的开始位置和结束位置，
// 特殊字符串格式为WrappedBeginCode + @用户 + WrappedEndCode
// 1. 如果删除位置在WrappedBeginCode和WrappedEndCode之间需要把特殊显示区域整体删除
// 2. 如果不在之间时则不做修改。
export function findDeleteWrappedRange(text: string, startindex: number, endIndex: number): [number, number, boolean] {
	let start = startindex, end = endIndex - 1;
	let isNotSpecial = true;
	// 往前找到第一个特殊符号是WrappedBeginCode，说明区间在之前
	if (text[start] === WrappedEndCode) {
		end = start + 1;
		start -= 1;
		while(start >= 0 && text[start] !== WrappedBeginCode) {
			start -= 1;
		}
		isNotSpecial = false;
		return [start, end, !isNotSpecial];
	}
	
	const specialCodes = [WrappedBeginCode, WrappedEndCode];
	while(start >= 0) {
		if (specialCodes.includes(text[start])) {
			isNotSpecial = false;
			break;
		} else {
			start -= 1;
		}
	}
	if (isNotSpecial || text[start] === WrappedEndCode) return [startindex, endIndex, !isNotSpecial];
	
	while(end < text.length) {
		if (specialCodes.includes(text[end])) {
			isNotSpecial = false;
			break;
		} else {
			end += 1;
		}
	}
	// 往前找到第一个特殊符号不是WrappedBeginCode，说明不在区间内
	if (isNotSpecial || text[end] === WrappedBeginCode) return [startindex, endIndex, !isNotSpecial];
	
	return [start, end + 1, !isNotSpecial];
}
/**
 * 添加时 添加的字符串刚好在WrappedBeginCode和WrappedEndCode之间
 * 则需要把添加的字符串放到WrappedEndCode之后防止输入破坏特殊显示区域。
 * 
 */ 
export function findAddIndex(text: string, startindex: number) {
	let start = startindex;
	if (text[start] === WrappedEndCode) {
		return start + 1;
	}

	// 如果在特殊格式中间需要放到特殊格式后面
	const specialCodes = [WrappedBeginCode, WrappedEndCode];
	let isNotFound = true;
	while(start >= 0) {
		if (specialCodes.includes(text[start])) {
			isNotFound = false;
			break;
		} else {
			start -= 1;
		}
	}
	if (isNotFound || text[start] === WrappedEndCode) return startindex;
	
	let end = startindex;
	
	while(end < text.length) {
		if (specialCodes.includes(text[end])) {
			isNotFound = false;
			break;
		} else {
			end += 1;
		}
	}
	if (isNotFound || text[end] === WrappedBeginCode) return startindex;
	return end + 1;
}
// 在字符串中找出匹配特殊子串数组中的位置
export function getSpeciaMatchIndex(str: string, special: string) {
	const regex = new RegExp(`${WrappedBeginCode}.*?${WrappedEndCode}`, "g");
	const maths = str.match(regex) || [];
	if (!maths.length) return -1;
	const deleteIndex = maths.findIndex(item => item === special);
	return deleteIndex;
}

// 从子串结尾出匹配已有特殊子串的个数
export function getSpeciaMatchCount(str: string, end = -1) {
	const mathStr = str.slice(0, end);
	const regex = new RegExp(`${WrappedBeginCode}.*?${WrappedEndCode}`, "g");
	const maths = mathStr.match(regex) || [];
	return maths.length;
}
