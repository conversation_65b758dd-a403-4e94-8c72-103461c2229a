<template>
  <z-paging ref="paging" class="app-container" :fixed="false">
    <template #top>
      <Navbar :type="1" :leftText="`${categoryTitle || ''}`"></Navbar>
    </template>

    <view class="container  mt-32">
      <view class="feature-item-wrapper" @click="() => handleTo('日常')">
        <view class="label">日常{{ categoryTitle || '' }}</view>
        <image class="icon" src="/static/icon/<EMAIL>"></image>
      </view>
      <view class="feature-item-wrapper mt-24" @click="() => handleTo('漫展')">
        <view class="label">漫展{{ categoryTitle || '' }}</view>
        <image class="icon" src="/static/icon/<EMAIL>"></image>
      </view>
    </view>

  </z-paging>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { onLoad } from '@dcloudio/uni-app'

const store = useStore()
const userInfo = computed(() => store.state.userInfo)
// 约妆 / 约拍
const categoryTitle = ref('')
onLoad((option) => {
  categoryTitle.value = option.type
})

const handleTo = type => {
  if (userInfo.value.cert_level <= 0) {
    uni.showToast({ title: '需要实名, 即将跳转至实名页面', icon: 'none' })
    setTimeout(() => {
      uni.navigateTo({
        url: '/pages/packageA/verify-idcard/verify-idcard'
      })
    }, 1000)
    return
  }
  let url = `/pages/packageA/service-create/detail-expo?categoryTitle=${categoryTitle.value}&typeTitle=${type}`
  if (type === '日常') {
    url = `/pages/packageA/service-create/detail-daliy?categoryTitle=${categoryTitle.value}&typeTitle=${type}`
  }
  url && uni.navigateTo({
    url
  })
}
</script>

<style scoped lang='scss'>
.mt-24 {
  margin-top: 24rpx;
}

.mt-32 {
  margin-top: 32rpx;
}

.container {
  width: 100%;
  box-sizing: border-box;
  padding: 0 24rpx;
}

.feature-item-wrapper {
  width: 100%;
  height: 80rpx;
  box-sizing: border-box;
  padding: 24rpx;
  background: #F7F8FA;
  border-radius: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .label {
    font-size: 32rpx;
    color: #3D3D3D;
    line-height: 32rpx;
  }

  .icon {
    width: 40rpx;
    height: 40rpx;
  }
}
</style>