import {
  getExhibitionDetail,
  getExhibitionComments
} from '@/api/exhibitionExpo'

// 漫展详情模块
const expoDetailStore = {
  namespaced: true, // 添加命名空间
  state: {
    expoDetail: null,
    expoComments: {
      count: 0,
      results: []
    }
  },
  mutations: {
    updateExpoDetail(state, expoDetail) {
      state.expoDetail = expoDetail
    },
    updateExpoComments(state, expoComments) {
      state.expoComments = expoComments
    },
  },
  actions: {
    async fetchExpoDetail({
      commit,
    }, expoId) {
      try {
        const res = await getExhibitionDetail(expoId)
        if (res.code === 20000) {
          const expoDetail = res.data

          // format detail
          expoDetail.detail = JSON.parse(expoDetail.detail)

          commit('updateExpoDetail', expoDetail)
        }
      } catch (error) {
        console.log(error, ' fetchExpoDetail')
      }
    },
    async fetchExpoComments({
      commit,
    }, { expoId, page = 1, size = 10 }) {
      try {
        const res = await getExhibitionComments(expoId, { page, size })
        if (res.code === 20000) {
          commit('updateExpoComments', res.data)
        }
      } catch (error) {
        console.log(error, ' fetchExpoComments')
      }
    }
  }
}

export default expoDetailStore