<template>
	<z-paging class="app-container" ref="paging" :safe-area-inset-bottom="true" :use-safe-area-placeholder="true">
		<template #top>
			<Navbar :type="1" :leftText="navTitle"></Navbar>
			<view class="allPidding" style="margin-top: 32rpx">
				<NotificationNavbar :state="true" placeholder="消息内容"></NotificationNavbar>
			</view>
		</template>
		<template #empty>
			<uv-empty />
		</template>
		<uni-list style="margin-top: 24rpx" :border="false">
			<uni-list-item v-for="(item, index) in orderList" :key="index" :border="false">
				<template v-slot:body>
					<view class="messageBox" @click="toOrdersNotification(item.id)">
						<image :src="prefixFilePath(item[type === '2' ? 'user' : 'vendor'].avatar)" mode="" @tap.stop="toUser(item)"></image>
						<view class="messageBoxRight">
							<view>
								<text>{{ item[type === '2' ? 'user' : 'vendor'].nickname || '' }}</text>
								<text>{{ formatMessage(item.id, item.last_message).time }}</text>
							</view>
							<view>
								<text class="textHide">{{ formatMessage(item.id, item.last_message).content }}</text>
								<view v-if="item?.new_message_count">{{ item?.new_message_count }}</view>
							</view>
						</view>
					</view>
				</template>
			</uni-list-item>
		</uni-list>
	</z-paging>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { getConsult } from '../../../api/notification';
import { useStore } from 'vuex';
import { timeToString } from '@/common/utils';

const store = useStore();
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);
function prefixFilePath(url) {
	if (!url) return '';
	return cloudFileUrl.value + url;
}

const message = computed(() => store.state.message.message)
const type = ref(null);
const navTitle = ref('订单消息')
onLoad((e) => {
	type.value = e.type;
	if (e.title) {
		navTitle.value = decodeURIComponent(e.title)
	}
	getInfo();
});

uni.$on('ordersNotification:refresh-data', getInfo)

function formatMessage(id, lastItem) {
	function formatContent(item) {
		if (!item?.msg_type) return ''

		if (item.msg_type === 'text') {
			return item.message
		} else if (item.msg_type === 'notice') {
			return ''
		} else {
			return '[图片]'
		}
	}
	if (!lastItem) {
		if (message.value[id]) {
			const { list } = message.value[id];
			if (!list.length) {
				return {
					content: '',
					time: ''
				}
			}
			const item = list[list.length - 1]
			return {
				content: formatContent(item),
				time: timeToString(list[list.length - 1].created_at, 5)
			};
		} else {
			return {
				content: '',
				time: ''
			}
		}

	} else {
		return {
			content: formatContent(lastItem),
			time: timeToString(lastItem.created_at, 5)
		}
	}
}

function toOrdersNotification(id) {
	if (!!message.value[id]) {
		message.value[id].newMsgCount = 0
		store.commit('message/updateMessage', message.value)
	}

	uni.navigateTo({
		url: `/pages/tabbar/notification/ordersNotificationDetails?type=${type.value}&consultid=${id}`
	});
}
let orderList = ref([]);

uni.$on('ordersNotification:refresh-newMsgCount', (id, count, lastMessage) => {
	let targetIndex = -1;
	orderList.value.forEach((item, index) => {
		if (item.id === id) {
			item.new_message_count += count
			item.last_message = lastMessage
			targetIndex = index;
		}
	})
	// 将目标数据移到第一位
	if (targetIndex > 0) {
		const targetItem = orderList.value.splice(targetIndex, 1)[0];
		orderList.value.unshift(targetItem);
	} else {
		// 如果已经在第一位或未找到，仍按照消息数量排序
		orderList.value.sort((a, b) => b.new_message_count - a.new_message_count)
	}
})

//获取咨询项目内容
function getInfo() {
	let role = type.value == 1 ? 'user' : 'vendor';
	getConsult(role).then((res) => {
		if (res.code === 20000) {
			orderList.value = res.data.sort((a, b) => b.last_fetch_message - a.last_fetch_message).sort((a, b) => b.new_message_count - a.new_message_count);
		}
	});
}
// 跳转用户主页
function toUser(item) {
	const id = item[type.value === '2' ? 'user' : 'vendor'].id
	uni.navigateTo({
		url: `/pages/home-pages/user-mine?id=${id}`
	});
}
</script>
<style lang="scss" scoped>
.allPidding {
	padding: 0 24rpx;
}

.messageBox {
	height: 120rpx;
	display: flex;
	margin-bottom: 24rpx;

	image {
		width: 96rpx;
		height: 96rpx;
	}

	.messageBoxRight {
		position: relative;
		width: 582rpx;
		height: 100%;
		background: #f7f8fa;
		border-radius: 24rpx;
		margin-left: 24rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;

		.type {
			position: absolute;
			width: 64rpx;
			height: 40rpx;
			background: #ffe540;
			border-radius: 0rpx 24rpx 0rpx 16rpx;
			right: 0;
			top: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 24rpx;
			color: #3d3d3d;
		}

		view {
			display: flex;
			align-items: center;
			font-size: 28rpx;
			color: #85878d;
			justify-content: space-between;
		}

		view:nth-child(1) {
			margin-bottom: 20rpx;

			text:nth-child(1) {
				font-size: 32rpx;
				color: #1f1f1f;
				padding-left: 20rpx;
			}

			text:nth-child(2) {
				padding-right: 20rpx;
			}
		}

		view:nth-child(2) {
			text {
				padding-left: 20rpx;
				width: 476rpx;
			}

			view {
				display: flex;
				justify-content: center;
				align-items: center;
				width: fit-content;
				padding: 0 8rpx;
				height: 32rpx;
				background-color: #fe1e42;
				border-radius: 50%;
				font-size: 24rpx;
				color: #ffffff;
				margin-right: 20rpx;
			}
		}
	}
}
</style>
