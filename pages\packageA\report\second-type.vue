<!-- 活动发布主界面 -->
<template>
	<view class="container">
		<Navbar :type="1"  leftText="举报"></Navbar>
		<view class="main">
			<type-select style="height: 100%;" :data="data" @select="selectHandle"/>
		</view>
	</view>
	
</template>
<script setup lang="ts">
	import { shallowRef, toRaw, onUnmounted, watchEffect, computed } from 'vue';
	import { reportService, reportComiket } from "@/api/report";
	import typeSelect from "./components/type-select.vue";
	import violationData from "./const"
	import { onLoad } from "@dcloudio/uni-app";
	import { ReportType, QueryParma } from "./const";
	
	interface DataItem {
		text: string;
		value: string;
	}
	
	const data = shallowRef<DataItem>();
	
	let templateQuery: QueryParma;
	onLoad((query: QueryParma) => {
		templateQuery =  query;
		const firtstTypeCode = query.firtstTypeCode;
		if (!firtstTypeCode) return;
		const selectItem = violationData.find(item => item.value == firtstTypeCode);
		if (!selectItem || !selectItem.children) return;
		data.value = selectItem.children as any;
	})
	const selectHandle = (item: DataItem) => {
		uni.navigateTo({
			url: `/pages/packageA/report/report?id=${templateQuery.id}&type=${templateQuery.type}&firtstTypeCode=${templateQuery.firtstTypeCode}&&secondTypeCode=${item.value}`
		})
	}
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		height: 100%;
		box-sizing: border-box;
		
		padding-bottom: constant(safe-area-inset-bottom);;
		padding-bottom: env(safe-area-inset-bottom);
	}
	
	.main {
		flex: 1 1 auto;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		box-sizing: border-box;
		padding: 32rpx 32rpx 24rpx;
		
		overflow-y: hidden;
	}
</style>
