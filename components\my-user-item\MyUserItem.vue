<template>
    <view class="messageItem" >
        <uv-avatar :src="prefixFilePath(data?.friend_avatar || data?.user_avatar || data?.avatar)" size="96rpx" mode="aspectFill"/>
        <view class="messageRight">
            <view class="messageItemContent">
                <view class="itemName">{{ data.friend_nickname || data.user_nickname || data.nickname }}</view>
                <view class="itemTips">{{ data.bio }}</view>
            </view>
			<template v-if="type === '1'">
				<view v-if="isBtn && !data.is_friend" class="messageBtn" @tap.stop="() => onBtn(data, type)">申请</view>
			</template>
			<template v-if="type === '2'">
				<view v-if="isBtn && !data.is_followed_back" class="messageBtn" @tap.stop="() => onBtn(data, type)">回关</view>
				<view v-else-if="isBtn && data.is_followed_back" class="messageBtn" @tap.stop="() => onBtn(data, type)">互关</view>
			</template>
        </view>
    </view>
</template>

<script setup>
import { ref } from 'vue'
import { useStore } from 'vuex';

const store = useStore();
defineProps(['data','isBtn','onBtn', 'type'])

// 当前环境域名和资源地址拼接
const prefixFilePath = (url) => {
	if (!url) return '';
	return store.state.common.cloudFileUrl + url;
}
</script>

<style lang="scss" scoped>
.messageItem {
    display: flex;
    align-items: center;
    .messageRight {
        display: flex;
        align-items: center;
        flex: 1;
        min-height: 96rpx;
        padding: 32rpx 0;
        border-bottom: 2rpx solid #eeeeee;
    }
    .messageItemContent {
        margin: 0 24rpx;
        flex: 1;
    }
    .itemName {
        font-size: 32rpx;
        color: #1F1F1F;
        line-height: 32rpx;
    }
    .itemTips {
        margin-top: 16rpx;
        font-size: 28rpx;
        color: #85878D;
        line-height: 28rpx;
    }
    .messageBtn {
        width: 144rpx;
        height: 72rpx;
        text-align: center;
        font-size: 32rpx;
        color: #FFFFFF;
        line-height: 72rpx;
        background: #FE58B7;
        border-radius: 48rpx;
    }
}
</style>