<template>
	<z-paging ref="paging" v-model="dataList" @query="queryList" :fixed="false">
		<template #empty>
		<!-- 	<view class="empty-list">
				<image class="icon" src="/static/<EMAIL>"></image>
				<text>暂无漫展</text>
			</view> -->
			<empty-list>
				<text>这里还暂无漫展哦 \n 点击发布，来添加第一个漫展吧！</text>
			</empty-list>
		</template>
		<!-- <uni-swiper-dot :info="swiperList" :current="slideIndex" field="content">
			
		</uni-swiper-dot> -->
		<swiper class="swiper-container" :style="{ height: swiperHeight }" circular :autoplay="true" :interval="3000"
			@change="swiperChange">
			<swiper-item v-for="item in swiperList" :key="item.id">
				<view class="image-container" @tap="navigateToAnnouncement(item)">
					<image class="img" :src="item.url"></image>
				</view>
			</swiper-item>
			<!-- #ifdef MP-WEIXIN -->
			<!-- <swiper-item key="anime-expo-swiper-item-ad">
				<view class="swiper-ad-container">
					<ad-custom unit-id="adunit-6f95bf46a3d577aa"></ad-custom>
				</view>
			</swiper-item> -->
			 <!-- #endif -->
		</swiper>

		<view class="tabs-container">
			<dust-tabs v-model="activeTab" :list="tabList" tabType="secondary" lineGradientBorder showActiveStar :customStyle="{gap: '30px'}"></dust-tabs>
		</view>
		<expos-items v-if="dataList.length > 0" :dataList="dataList" type="home"></expos-items>
	</z-paging>
</template>

<script setup>
import { ref, computed, watch, watchEffect } from 'vue';
import { useStore } from 'vuex';
import { calculateHeight } from '@/common/utils';
import { getExhibitions } from '@/api/exhibitionExpo';
import { getAnnouncementList } from "@/api/announcement";
import dayjs from 'dayjs';

const store = useStore();
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);
const location = computed(() => store.state.location)

function prefixFilePath(url) {
	if (!url) return '';
	return cloudFileUrl.value + url;
}

const slideIndex = ref(0);
const swiperList = ref([{url: '/static/img/swiper/swiper-pic1.jpg'}, {url: '/static/img/swiper/swiper-pic2.jpg'}]);
const swiperHeight = ref(calculateHeight(30).height);

const paging = ref(null);
const dataList = ref([]);

const activeTab = ref(0);
const tabList = ref([
	{
		name: '推荐',
		value: 0
	},
	{
		name: '所有',
		value: 1
	},
	{
		name: '收藏',
		value: 2
	},
	{
		name: '已参加',
		value: 3
	},
	{
		name: '已结束',
		value: 4
	}
]);

watch(activeTab, () => {
	paging.value.reload();
});

uni.$on('location-change-refresh-expos', () => {
	paging.value && paging.value.reload();
})

function swiperChange(e) {
	slideIndex.value = e.detail.current;
}

getAnnouncementList().then(res => {
	const isSucessed = res.code === 20000;
	if (!isSucessed) return uni.showToast({
		icon: "error",
		title: "获取公告页失败"
	});
	const userList = res.data.results || [];
	if (!userList.length) return;
	console.log("请求的公告数据:", JSON.stringify(userList));
	swiperList.value = userList.map(item => ({url: item.cover, id: item.id}));
});

function navigateToAnnouncement(item) {
	if (!item.id) return;
	uni.navigateTo({url: "/pages/announcement/detail?id=" + item.id});
}

async function queryList(pageNo, pageSize) {
	const flagList = ['recommend', 'all', 'collection', 'joinin'];
	const params = {
		page: pageNo,
		size: 10,
		flag: flagList[activeTab.value],
	};
	if (location.value.province.id) {
		params.province = location.value.province.id
	}
	if (location.value.city.id) {
		params.city = location.value.city.id
	}

	// 参加 / 收藏 不传省份和城市
	if ([2, 3].includes(activeTab.value)) {
		delete params.province;
		delete params.city;
	}

	if (activeTab.value === 4) {
		delete params.flag;
		params.end_time = dayjs().startOf('day').valueOf();
	} else {
		// params.start_time = -dayjs().startOf('day').valueOf();
		params.end_time = -dayjs().endOf('day').valueOf();
	}

	let res = await getExhibitions(params);
	if (res.code === 20000) {
		paging.value.complete(res.data.results);
	} else {
		paging.value.complete(false);
	}
}
</script>

<style lang="scss" scoped>
.empty-list {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	color: #85878D;

	.icon {
		width: 380rpx;
		height: 380rpx;
	}
}

.swiper-container {
	width: 100%;
	margin-top: 20rpx;
	border-radius: 20rpx;
	overflow: hidden;

	.swiper-item-image {
		width: 100%;
	}

	.swiper-ad-container {
		width: 100%;
	}
}

.tabs-container {
	margin-top: 28rpx;
	margin-bottom: 16rpx;
	width: 100%;
	height: 72rpx;
	display: flex;
	align-items: center;
}
</style>