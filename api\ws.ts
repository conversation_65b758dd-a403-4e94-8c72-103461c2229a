const TypeLogin = 'login'
const TypeCheck = 'check'
const TypeSendConsult = 'sendConsult'
const TypeReceiveConsult = 'receiveConsult'
const TypeSendChat = 'sendChat'
const TypeReceiveChat = 'receiveChat'

interface Options {
    url: string
    onOpen?: (event: Event) => void
    onClose?: (event: CloseEvent) => void
    onMessage?: (data: JSON) => void
    onConsultMessage?: (data: JSON) => void
    onChatMessage?: (data: JSON) => void
    onError?: (event: Event) => void
    gid?: () => string
}

class MessageClient {
    options: Options
    messages: { [id: string]: any }
    gid: () => string
    ws: WebSocket | undefined

    /**
     * @param {object} options
     * @param {string} options.url websocket 地址
     * @param {function(Event)} [options.onOpen] websocket 打开回调
     * @param {function(CloseEvent)} [options.onClose] websocket 关闭回调
     * @param {function(JSON)} [options.onMessage] websocket 接收消息回调
     * @param {function(JSON)} [options.onConsultMessage] websocket 接收咨询消息回调
     * @param {function(JSON)} [options.onChatMessage] websocket 接收聊天消息回调
     * @param {function(Event)} [options.onError] websocket 错误回调
     * @param {function(): string} [options.gid] 生成唯一 id 的函数
     */
    constructor(options: Options) {
        this.options = options
        this.messages = {}
        if (this.options.gid) {
            this.gid = this.options.gid
        } else {
            this.gid = () => {
                return Math.random().toString(36).substr(2)
            }
        }
    }

    connect() {
        return new Promise((resolve, reject) => {
            this.ws = new WebSocket(this.options.url)
            this.ws.onopen = (ev) => {
                resolve(ev)
                this.onOpen(ev)
            }
            this.ws.onclose = this.onClose.bind(this)
            this.ws.onmessage = this.onMessage.bind(this)
            this.ws.onerror = (ev) => {
                reject(ev)
                this.onError(ev)
            }
        })
    }

    onOpen(event: Event) {
        if (this.options.onOpen) {
            this.options.onOpen(event)
        }
    }

    onClose(event: CloseEvent) {
        if (this.options.onClose) {
            this.options.onClose(event)
        }
    }

    onMessage(event: MessageEvent) {
        const data = JSON.parse(event.data)
        const id = data.id
        if (this.messages[id]) {
            this.messages[id].resolve(data)
            delete this.messages[id]
        } else {
            if (this.options.onMessage) {
                this.options.onMessage(data)
            }
        }
    }

    onError(event: Event) {
        if (this.options.onError) {
            this.options.onError(event)
        }
    }

    send(data: string | ArrayBufferLike | Blob | ArrayBufferView) {
        if (this.ws?.readyState !== WebSocket.OPEN) {
            throw new Error('WebSocket is not open')
        }
        this.ws.send(data)
    }

    sendJson(data: any) {
        if (this.ws?.readyState !== WebSocket.OPEN) {
            throw new Error('WebSocket is not open')
        }
        this.ws.send(JSON.stringify(data))
    }

    /**
     * 关闭连接
     */
    close() {
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.ws.close()
        }
    }

    /**
     * @param {string} token
     * @param {number} timeout
     */
    login(token: string, timeout: number = 1000 * 10) {
        const id = this.gid()
        this.sendJson({
            id: id,
            type: TypeLogin,
            data: {
                token: token,
                terminal: 'wxmp',
            },
        })
        const timer = setTimeout(() => {
            if (this.messages[id]) {
                this.messages[id].reject(new Error('login timeout'))
                delete this.messages[id]
            }
            if (timer) {
                clearTimeout(timer)
            }
        }, timeout)
        return new Promise((resolve, reject) => {
            this.messages[id] = {
                resolve,
                reject,
                timer,
            }
        })
    }

    /**
     * 检查登录状态
     * @param {number} timeout
     */
    check(timeout = 1000 * 10) {
        const id = this.gid()
        this.sendJson({
            id: id,
            type: TypeCheck,
            data: {},
        })
        const timer = setTimeout(() => {
            if (this.messages[id]) {
                this.messages[id].reject(new Error('login timeout'))
                delete this.messages[id]
            }
            clearTimeout(timer)
        }, timeout)
        return new Promise((resolve, reject) => {
            this.messages[id] = {
                resolve,
                reject,
            }
        })
    }

    /**
     * @param {number} to 接收方用户 id
     * @param {string} content 消息内容
     * @param {number} ref 引用消息 id
     * @param {number} timeout 超时时间
     */
    textConsult(to: number, content: string, ref: number = 0, timeout: number = 1000 * 10) {
        const id = this.gid()
        this.sendJson({
            id: id,
            type: TypeSendConsult,
            data: {
                to: to,
                type: 'text',
                ref: ref,
                content: content,
            },
        })
        const timer = setTimeout(() => {
            if (this.messages[id]) {
                this.messages[id].reject(new Error('login timeout'))
                delete this.messages[id]
            }
            clearTimeout(timer)
        }, timeout)
        return new Promise((resolve, reject) => {
            this.messages[id] = {
                resolve,
                reject,
            }
        })
    }

    /**
     * @param {number} to 接收方用户 id
     * @param {string} url 文件地址
     * @param {number} ref 引用消息 id
     * @param {number} timeout 超时时间
     */
    fileConsult(to: number, url: string, ref: number = 0, timeout: number = 1000 * 10) {
        const id = this.gid()
        this.sendJson({
            id: id,
            type: TypeSendConsult,
            data: {
                to: to,
                type: 'file',
                ref: ref,
                content: url,
            },
        })
        const timer = setTimeout(() => {
            if (this.messages[id]) {
                this.messages[id].reject(new Error('login timeout'))
                delete this.messages[id]
            }
            clearTimeout(timer)
        }, timeout)
        return new Promise((resolve, reject) => {
            this.messages[id] = {
                resolve,
                reject,
            }
        })
    }

    /**
     * @param {number} to 接收方用户 id
     * @param {Array<{type: number, content: string}>} multi 多个消息内容
     * @param {number} ref 引用消息 id
     * @param {number} timeout 超时时间
     */
    multiConsult(
        to: number,
        multi: Array<{ type: number; content: string }>,
        ref: number = 0,
        timeout: number = 1000 * 10,
    ) {
        const id = this.gid()
        this.sendJson({
            id: id,
            type: TypeSendConsult,
            data: {
                to: to,
                type: 'multi',
                ref: ref,
                content: JSON.stringify(multi),
            },
        })
        const timer = setTimeout(() => {
            if (this.messages[id]) {
                this.messages[id].reject(new Error('login timeout'))
                delete this.messages[id]
            }
            clearTimeout(timer)
        }, timeout)
        return new Promise((resolve, reject) => {
            this.messages[id] = {
                resolve,
                reject,
            }
        })
    }

    /**
     * @param {number} to 接收方用户 id
     * @param {string} content 消息内容
     * @param {number} ref 引用消息 id
     * @param {number} timeout 超时时间
     */
    textChat(to: number, content: string, ref: number = 0, timeout: number = 1000 * 10) {
        const id = this.gid()
        this.sendJson({
            id: id,
            type: TypeSendChat,
            data: {
                to: to,
                type: 'text',
                ref: ref,
                content: content,
            },
        })
        const timer = setTimeout(() => {
            if (this.messages[id]) {
                this.messages[id].reject(new Error('login timeout'))
                delete this.messages[id]
            }
            clearTimeout(timer)
        }, timeout)
        return new Promise((resolve, reject) => {
            this.messages[id] = {
                resolve,
                reject,
            }
        })
    }

    /**
     * @param {number} to 接收方用户 id
     * @param {string} url 文件地址
     * @param {number} ref 引用消息 id
     * @param {number} timeout 超时时间
     */
    fileChat(to: number, url: string, ref: number = 0, timeout: number = 1000 * 10) {
        const id = this.gid()
        this.sendJson({
            id: id,
            type: TypeSendChat,
            data: {
                to: to,
                type: 'file',
                ref: ref,
                content: url,
            },
        })
        const timer = setTimeout(() => {
            if (this.messages[id]) {
                this.messages[id].reject(new Error('login timeout'))
                delete this.messages[id]
            }
            clearTimeout(timer)
        }, timeout)
        return new Promise((resolve, reject) => {
            this.messages[id] = {
                resolve,
                reject,
            }
        })
    }

    /**
     * @param {number} to 接收方用户 id
     * @param {Array<{type: number, content: string}>} multi 多个消息内容
     * @param {number} ref 引用消息 id
     * @param {number} timeout 超时时间
     */
    multiChat(
        to: number,
        multi: Array<{ type: number; content: string }>,
        ref: number = 0,
        timeout: number = 1000 * 10,
    ) {
        const id = this.gid()
        this.sendJson({
            id: id,
            type: TypeSendChat,
            data: {
                to: to,
                type: 'multi',
                ref: ref,
                content: JSON.stringify(multi),
            },
        })
        const timer = setTimeout(() => {
            if (this.messages[id]) {
                this.messages[id].reject(new Error('login timeout'))
                delete this.messages[id]
            }
            clearTimeout(timer)
        }, timeout)
        return new Promise((resolve, reject) => {
            this.messages[id] = {
                resolve,
                reject,
            }
        })
    }

    /**
     * @param {string} token
     */
    loginSend(token: string) {
        const id = this.gid()
        this.sendJson({
            id: id,
            type: TypeLogin,
            data: {
                token: token,
                terminal: 'wxmp',
            },
        })
        return id
    }

    /**
     * 检查登录状态
     */
    checkSend() {
        const id = this.gid()
        this.sendJson({
            id: id,
            type: TypeCheck,
            data: {},
        })
        return id
    }

    /**
     * @param {number} to 接收方用户 id
     * @param {string} content
     * @param {number} ref 引用消息 id
     */
    textConsultSend(to: number, content: string, ref: number = 0) {
        const id = this.gid()
        this.sendJson({
            id: id,
            type: TypeSendConsult,
            data: {
                to: to,
                type: 'text',
                ref: ref,
                content: content,
            },
        })
        return id
    }

    /**
     * @param {number} to 接收方用户 id
     * @param {string} url 文件地址
     * @param {number} ref 引用消息 id
     */
    fileConsultSend(to: number, url: string, ref: number = 0) {
        const id = this.gid()
        this.sendJson({
            id: id,
            type: TypeSendConsult,
            data: {
                to: to,
                type: 'file',
                ref: ref,
                content: url,
            },
        })
        return id
    }

    /**
     * @param {number} to 接收方用户 id
     * @param {Array<{type: number, content: string}>} multi 多个消息内容
     * @param {number} ref 引用消息 id
     */
    multiConsultSend(to: number, multi: Array<{ type: number; content: string }>, ref: number = 0) {
        const id = this.gid()
        this.sendJson({
            id: id,
            type: TypeSendConsult,
            data: {
                to: to,
                type: 'multi',
                ref: ref,
                content: multi,
            },
        })
        return id
    }

    /**
     * @param {number} to 接收方用户 id
     * @param {string} content
     * @param {number} ref 引用消息 id
     */
    textChatSend(to: number, content: string, ref: number = 0) {
        const id = this.gid()
        this.sendJson({
            id: id,
            type: TypeSendChat,
            data: {
                to: to,
                type: 'text',
                ref: ref,
                content: content,
            },
        })
        return id
    }

    /**
     * @param {number} to 接收方用户 id
     * @param {string} url 文件地址
     * @param {number} ref 引用消息 id
     */
    fileChatSend(to: number, url: string, ref: number = 0) {
        const id = this.gid()
        this.sendJson({
            id: id,
            type: TypeSendChat,
            data: {
                to: to,
                type: 'file',
                ref: ref,
                content: url,
            },
        })
        return id
    }

    /**
     * @param {number} to 接收方用户 id
     * @param {Array<{type: number, content: string}>} multi 多个消息内容
     * @param {number} ref 引用消息 id
     */
    multiChatSend(to: number, multi: Array<{ type: number; content: string }>, ref: number = 0) {
        const id = this.gid()
        this.sendJson({
            id: id,
            type: TypeSendChat,
            data: {
                to: to,
                type: 'multi',
                ref: ref,
                content: multi,
            },
        })
        return id
    }
}

export { TypeLogin, TypeCheck, TypeSendConsult, TypeReceiveConsult, TypeSendChat, TypeReceiveChat }

export default MessageClient
