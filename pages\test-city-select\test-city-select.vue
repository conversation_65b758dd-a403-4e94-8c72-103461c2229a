<template>
  <view class="container">
    <uni-nav-bar title="城市选择测试" :border="true"></uni-nav-bar>

    <view class="content">
      <!-- 模式选择 -->
      <view class="mode-section">
        <view class="mode-title">选择模式：</view>
        <view class="mode-buttons">
          <button
            class="mode-btn"
            :class="{ active: currentMode === 'province' }"
            @click="changeMode('province')"
          >
            仅省份
          </button>
          <button
            class="mode-btn"
            :class="{ active: currentMode === 'province-city' }"
            @click="changeMode('province-city')"
          >
            省市
          </button>
          <button
            class="mode-btn"
            :class="{ active: currentMode === 'province-city-district' }"
            @click="changeMode('province-city-district')"
          >
            省市区
          </button>
        </view>
      </view>

      <!-- 结果显示 -->
      <view class="result-section" v-if="selectedResult.province?.name">
        <view class="result-title">已选择的地区：</view>
        <view class="result-text">
          <text>{{ selectedResult.province?.name }}</text>
          <text v-if="selectedResult.city?.name"> - {{ selectedResult.city?.name }}</text>
          <text v-if="selectedResult.district?.name"> - {{ selectedResult.district?.name }}</text>
        </view>
      </view>

      <!-- 城市选择组件 -->
      <city-select
        :mode="currentMode"
        :custom-nav-height="88"
        :value="selectedResult"
        @confirm="onCityConfirm"
        @change="onCityChange">
      </city-select>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import CitySelect from '@/components/city-select/city-select.vue'

// 当前选择模式
const currentMode = ref('province-city-district')

// 选择结果
const selectedResult = ref({
  province: {},
  city: {},
  district: {}
})

// 切换模式
const changeMode = (mode) => {
  currentMode.value = mode
  // 清空选择结果
  selectedResult.value = {
    province: {},
    city: {},
    district: {}
  }

  uni.showToast({
    title: `已切换到${getModeText(mode)}模式`,
    icon: 'none'
  })
}

// 获取模式文本
const getModeText = (mode) => {
  const modeMap = {
    'province': '仅省份',
    'province-city': '省市',
    'province-city-district': '省市区'
  }
  return modeMap[mode] || '未知'
}

const onCityConfirm = (result) => {
  selectedResult.value = result
  console.log('确认选择:', result)

  // 构建提示文本
  let message = ''
  if (result.province?.name) {
    message += result.province.name
  }
  if (result.city?.name) {
    message += ' - ' + result.city.name
  }
  if (result.district?.name) {
    message += ' - ' + result.district.name
  }

  uni.showToast({
    title: message || '选择成功',
    icon: 'success',
    duration: 2000
  })
}

const onCityChange = (result) => {
  console.log('选择变化:', result)
}
</script>

<style scoped lang="scss">
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.mode-section {
  padding: 32rpx;
  background: #fff;
  margin-bottom: 20rpx;

  .mode-title {
    font-size: 28rpx;
    color: #606266;
    margin-bottom: 24rpx;
  }

  .mode-buttons {
    display: flex;
    gap: 20rpx;

    .mode-btn {
      flex: 1;
      padding: 20rpx;
      font-size: 26rpx;
      color: #606266;
      background-color: #f5f7fa;
      border: 1px solid #dcdfe6;
      border-radius: 8rpx;

      &.active {
        color: #409eff;
        background-color: #ecf5ff;
        border-color: #b3d8ff;
      }
    }
  }
}

.result-section {
  padding: 32rpx;
  background: #fff;
  margin-bottom: 20rpx;

  .result-title {
    font-size: 28rpx;
    color: #606266;
    margin-bottom: 16rpx;
  }

  .result-text {
    font-size: 32rpx;
    color: #303133;
    font-weight: 500;
  }
}
</style>
