<template>
  <z-paging class="app-container" :safe-area-inset-bottom="true" :use-safe-area-placeholder="true">
    <template #top>
      <Navbar :type="1" leftText="展会详情"></Navbar>
    </template>

    <view class="content">
      <dust-image-base wrapperRadius="20rpx" aspectRatio="16:9" :local="false"
        :src="detailInfo.cover"></dust-image-base>

      <view class="expo-title">
        {{ detailInfo.name }}
      </view>

      <view class="service-wrapper">
        <view class="title">服务详情</view>

        <uni-list class="dust-uni-list" :border="false">
          <uni-list-item :border="false" class="dust-uni-list-item" v-for="(item, index) in detailInfo.items"
            :key="item.id">
            <template v-slot:body>
              <view class="item-wrapper" :class="{ active: selectedServiceItemId === item.id }" @click="handleServiceItemClick(item)">
                <view class="title">{{ item.name }}</view>
                <view class="combo-description">商家保证金{{ item.bond }}%</view>
                <view class="combo-description">
                  <text>参考价：</text>
                  <view class="price-wrapper">
                    <text class="prefix">￥</text>
                    <text class="price">{{ item.price / 100 }}</text>
                  </view>
                </view>
              </view>
            </template>
          </uni-list-item>
        </uni-list>
      </view>

      <view class="time-title">{{ typeMap[type] || '' }}时间</view>
      <view class="time-trigger" @click="popup.open()">
        <view class="time-trigger-text" :class="{ 'selected': selectedTimeText }">{{ selectedTimeText || '请选择' }}</view>
        <image class="time-trigger-icon" src="/static/icon/<EMAIL>"></image>
      </view>

      <uv-popup ref="popup" mode="bottom" bgColor="none">
        <view class="popup-wrapper">
          <view class="header">{{ typeMap[type] || '' }}时间</view>
          <dust-date-picker :timeSlotCannotNull="false" :showTimeSlot="true" confirmText="确定"
            @confirm="popupConfirm" mode="range" :dateSlot="selectedDate"></dust-date-picker>
        </view>
      </uv-popup>
    </view>

    <template #bottom>
      <view class="footer-wrapper">
        <view class="btn-submit" @click="handleSubmit">
          立即咨询
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app'
import { ref, computed } from 'vue'
import { getDimensionServiceDetail } from '@/api/dimensionService'
import dayjs from 'dayjs'
import { createConsult, putConsult } from '@/api/notification';

const typeMap = {
  'photography': '约拍',
  'makeup': '约妆',
}

// 时间段映射，与组件中的定义保持一致
const timeSlotMap = [
  {
    name: '上午',
    time: '8:00-12:00',
    value: 1
  },
  {
    name: '下午',
    time: '12:00-18:00',
    value: 2
  },
  {
    name: '晚上',
    time: '18:00-23:00',
    value: 4
  },
  {
    name: '夜间',
    time: '23:00-8:00',
    value: 8
  }
]

const type = ref('')
const detailInfo = ref({})
const popup = ref(null)
const serviceId = ref('')
const selectedDate = ref(null)
const selectedTimeSlot = ref(0)
const selectedServiceItemId = ref('')

// 计算选中时间的显示文案
const selectedTimeText = computed(() => {
  if (!selectedDate.value) return ''
  
  let dateText = ''
  if (Array.isArray(selectedDate.value)) {
    if (selectedDate.value.length === 1) {
      dateText = dayjs(selectedDate.value[0]).format('MM月DD日')
    } else if (selectedDate.value.length > 1) {
      // 对日期进行排序
      const sortedDates = [...selectedDate.value].sort((a, b) => dayjs(a).valueOf() - dayjs(b).valueOf())
      
      // 按月份分组
      const monthGroups = {}
      sortedDates.forEach(date => {
        const month = dayjs(date).format('MM')
        const day = dayjs(date).format('DD')
        if (!monthGroups[month]) {
          monthGroups[month] = []
        }
        monthGroups[month].push(day)
      })
      
      // 生成显示文本
      const monthTexts = []
      Object.keys(monthGroups).forEach(month => {
        const days = monthGroups[month].join('、')
        monthTexts.push(`${month}月${days}日`)
      })
      
      dateText = monthTexts.join('，')
    }
  } else {
    dateText = dayjs(selectedDate.value).format('MM月DD日')
  }
  
  let timeText = ''
  if (selectedTimeSlot.value > 0) {
    const selectedTimes = []
    timeSlotMap.forEach(slot => {
      if (selectedTimeSlot.value & slot.value) {
        selectedTimes.push(slot.name)
      }
    })
    timeText = selectedTimes.join('、')
  }
  
  return timeText ? `${dateText} ${timeText}` : dateText
})

onLoad((options) => {
  type.value = options.type
  serviceId.value = options.serviceId
  fetchDimensionServiceDetail()
})

async function fetchDimensionServiceDetail() {
  try {
    const res = await getDimensionServiceDetail(serviceId.value)
    if (res.code === 20000) {
      detailInfo.value = res.data
    }
  } catch (error) {
    console.log(error, 'fetchDimensionServiceDetail error')
  }
}

function handleServiceItemClick(item) {
  selectedServiceItemId.value = item.id
}

function popupConfirm(date) {
  console.log('popupConfirm:', date)
  selectedDate.value = date.dateSlot
  selectedTimeSlot.value = date.timeSlot || 0
  popup.value.close()
}

function handleSubmit() {
  const serviceItemId = selectedServiceItemId.value || detailInfo.value.items[0].id
  const message = selectedTimeText.value ? `你好，问下${selectedTimeText.value}这些天的套餐服务情况` : `你好，问下这边的套餐服务情况`

  if(serviceItemId) {
    createConsult(serviceItemId).then((res) => {
      if(res.code === 20000) {
        putConsult(serviceItemId, res.data.id).then((res2) => {
          if(res2.code === 20000) {
            uni.navigateTo({
              url: `/pages/tabbar/notification/ordersNotificationDetails?type=1&consultid=${res.data.id}&message=${encodeURIComponent(message)}`
            })
          }
        })
      }
    })
  }
}

</script>

<style scoped lang="scss">
.content {
  flex: 1;
  box-sizing: border-box;
  padding: 0 24rpx 50rpx;

  .expo-title {
    font-weight: 700;
    font-size: 36rpx;
    color: #3D3D3D;
    margin-top: 40rpx;
  }

  .service-wrapper {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    margin-top: 40rpx;

    .title {
      font-weight: 500;
      font-size: 32rpx;
      color: #3D3D3D;
    }

    .item-wrapper {
      width: 100%;
      background: #F7F8FA;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      padding: 24rpx;
      box-sizing: border-box;
      border: 2rpx solid transparent;

      &.active {
        border: 2rpx solid #FE58B7;
      }

      .combo-description {
        font-size: 28rpx;
        color: #85878D;
        line-height: 40rpx;
        display: flex;
        align-items: center;
        margin-top: 12rpx;

        .price-wrapper {
          display: flex;
          align-items: flex-end;
          font-weight: 700;
          line-height: 34rpx;
          color: #FE58B7;

          .prefix {
            font-size: 24rpx;
          }

          .price {
            font-size: 36rpx;
          }
        }
      }
    }
  }

  .time-title {
    font-weight: 500;
    font-size: 32rpx;
    color: #3D3D3D;
    margin-top: 40rpx;
  }

  .time-trigger {
    box-sizing: border-box;
    padding: 24rpx;
    margin-top: 28rpx;
    width: 100%;
    min-height: 80rpx;
    background: #F7F8FA;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 32rpx;
    color: #85878D;

    .time-trigger-text {
      flex: 1;

      &.selected {
        color: #3D3D3D;
        font-weight: 500;
      }
    }

    .time-trigger-icon {
      width: 40rpx;
      height: 40rpx;
    }
  }
}

.popup-wrapper {
  width: 100%;
  height: fit-content;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;

  .header {
    width: 100%;
    padding: 28rpx 0;
    text-align: center;
    font-weight: 500;
    font-size: 32rpx;
    color: #3D3D3D;
  }
}

.footer-wrapper {
  box-sizing: border-box;
  padding: 0 24rpx;

  .btn-submit {
    text-align: center;
  }
}
</style>
