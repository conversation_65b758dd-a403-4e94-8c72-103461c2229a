import { requestApi } from './request';

interface GetMineServiceDetailParamsV2 {
	is_daily: boolean
	// 1: 约妆 2：约拍
	category_id: number
}
export const getMineServiceDetailV2 = (params: GetMineServiceDetailParamsV2) => {
	return requestApi({
		url: `/api/v1/foreground/mine/svc`,
		method: 'GET',
		data: params
	})
}

interface GetMineServiceDetailParams {
	// 漫展id， 0 为日常
	expo_id: number
	// 1: 约妆 2：约拍
	// category_id: number
	categories: number[]
}

/** 获取服务 */
export const getMineServiceDetail = (params: GetMineServiceDetailParams) => {
	return requestApi({
		url: `/api/v1/foreground/mine/service`,
		method: 'GET',
		data: params,
		loading: false,
	})
}

/** 创建服务 Params */
interface CreateDimensionServiceParams {
	/** 服务封面 */
	cover: string
	/** 详情编辑器 */
	editor: string
	/** 详情 */
	detail: string
	/** 服务分类 1: 约妆 2：约拍 */
	category_id: number
	categories: number[]
	/** 1: 日常服务 2: 漫展服务 */
	type: number
}

/** 创建服务 */
export const createDimensionService = (params: CreateDimensionServiceParams) => {
	return requestApi({
		url: `/api/v1/foreground/mine/service`,
		data: params,
	})
}

/** 更新服务 */
export const updateDimensionService = (service_id: number, params: CreateDimensionServiceParams) => {
	return requestApi({
		url: `/api/v1/foreground/mine/service/${service_id}`,
		data: params,
		method: 'PUT'
	})
}

/** 删除服务 */
export const deleteDimensionService = (service_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/mine/service/${service_id}`,
		method: 'DELETE'
	})
}

/** 创建服务子项 Params */
interface CreateDimensionServiceItemParams {
	/** 服务项名称 */
	name: string
	/** 服务项封面 */
	cover: string
	/** 服务项价格 */
	price: number
	/** 商家保证金 */
	bond: number
	/** 是否上架 */
	is_on_sale: boolean
}

/** 获取服务套餐列表 */
export const getMineServiceDetailItems = (service_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/mine/service/${service_id}/item`,
		method: 'GET',
	})
}

/** 创建服务套餐 */
export const createMineServiceDetailItems = (service_id: number, params: CreateDimensionServiceItemParams) => {
	return requestApi({
		url: `/api/v1/foreground/mine/service/${service_id}/item`,
		data: params
	})
}

/** 更新服务套餐 */
export const updateMineServiceDetailItems = (service_id: number, item_id: number, params: CreateDimensionServiceItemParams) => {
	return requestApi({
		url: `/api/v1/foreground/mine/service/${service_id}/item/${item_id}`,
		method: 'PUT',
		data: params
	})
}

/** 删除服务套餐 */
export const deleteMineServiceDetailItems = (service_id: number, item_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/mine/service/${service_id}/item/${item_id}`,
		method: 'DELETE'
	})
}

/** 获取服务评论 */
export const getMineServiceReview = () => {
	return requestApi({
		url: `/api/v1/foreground/mine/service/review`,
		method: 'GET',
	})
}

// 上架服务
export const publishService = (service_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/mine/service/${service_id}/onsale`,
		method: 'PUT',
	})
}

// 下架服务
export const desaleService = (service_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/mine/service/${service_id}/desale`,
		method: 'PUT',
	})
}