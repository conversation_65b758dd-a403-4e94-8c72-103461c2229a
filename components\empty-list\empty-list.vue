<template>
	<view class="container">
		<image v-if="!hideIcon" class="icon" src="/static/<EMAIL>"></image>
		<view class="content">
			<slot></slot>
		</view>
		<view class="footer" v-if="props.footer">
			<slot name="footer">
				<view class="btn" @tap="onBtnClick">{{ props.footer }}</view>
			</slot>
		</view>
	</view>
</template>
<script lang="ts" setup>
interface Props {
	footer?: String;
	hideIcon?: boolean
}
const props = defineProps<Props>();
interface Emits {
	(e: "onBtnClick"): void;
}
const emits = defineEmits<Emits>();
function onBtnClick() {
	emits("onBtnClick");
}
</script>
<style scoped lang="scss">
.container {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: stretch;

	.icon {
		align-self: center;
		width: 380rpx;
		height: 380rpx;
	}

	.content {
		font-size: 28rpx;
		line-height: 44rpx;
		color: #85878D;
		text-align: center;
		padding-bottom: 30rpx;
	}

	.footer {
		margin-top: 20rpx;
		flex: 0 0 auto;
		padding: 0 24rpx 30rpx;

		.btn {
			width: 100%;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 36rpx;
			font-weight: 500;
			color: white;
			text-align: center;
			border-radius: 40rpx;
			background-color: rgba(254, 88, 183, 1);
		}
	}
}
</style>