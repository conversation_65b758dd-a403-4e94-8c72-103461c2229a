<template>
	<z-paging class="app-container" ref="paging" refresher-only @onRefresh="onRefresh" :safe-area-inset-bottom="true"
		:use-safe-area-placeholder="true">
		<template #top>
			<Navbar :type="1" leftText="剧团详情"></Navbar>
		</template>
		<view class="listBox" v-if="!isLoading">
			<view class="listBox-top" style="position: relative">
				<dust-image-base :src="detail.cover" :local="false" aspect-ratio="16:9"></dust-image-base>
				<view class="look-bar">
					<image class="icon-hq" src="/static/icon/dust_icon_view.svg"></image>
					<!-- <text>{{formatCountToString(detail.view_count)}}</text> -->
				</view>
			</view>
			<view class="header-title">
				<text class="title">{{detail.title}}</text>
			</view>
			<view class="user-bar">
				<view class="time-bar">
					<view class="flex items-center">
						<image class="icon-sj" src="/static/img/exhibition-details/icon_sj.png"></image>
						<text>{{dayjs(detail.created_at).format('YYYY.MM.DD')}}</text>
					</view>
					<view class="flex items-center">
						<image class="icon-jd" src="/static/icon/<EMAIL>"></image>
						<text>{{detail.members.length}}/{{detail.max_member_count}}</text>
					</view>
				</view>
				<view class="user-info" @tap="handleToUserMine(detail.creator)">
					<image :src="prefixFilePath(detail.creator.avatar)" class="userImg"></image>
					<text>{{detail.creator.nickname || detail.creator.name || detail.creator.account}}</text>
				</view>
			</view>
			<view class="labels">
				<view class="label-item" v-for="(itm, idx) in detail.tags" :key="idx">
					<text>{{itm}}</text>
				</view>
			</view>
			<view class="line" />
			<view class="characters">
				<view class="characters-item" v-for="(itm, idx) in groupedMembers" :key="idx">
					<text>{{itm.role.name}}{{itm.members.length > 0 ? ` | ${itm.members.length}` : ''}}</text>
				</view>
			</view>
		</view>
		<view class="box-header">
			<view class="box-header-title">
				<view class="box-header-item">
					<text>剧组说明</text>
					<image src="/static/icon/dust_icon_tabs_active_star.svg" class="active-star"></image>
				</view>
			</view>
		</view>
		<view class="box-detail-wrapper">
			<text v-if="!!detail.description">{{detail.description}}</text>
			<view class="empty-ui" v-else>
				<text>暂无剧组说明</text>
			</view>
		</view>
		<view class="box-header">
			<view class="box-header-title">
				<view class="box-header-item">
					<text>已过审名单</text>
					<image src="/static/icon/dust_icon_tabs_active_star.svg" class="active-star"></image>
				</view>
			</view>
		</view>
		<view v-for="(item, index) in groupedMembers" :key="index">
			<view class="characters">
				<view class="characters-item">
					<text>{{item.role.name}}{{item.members.length > 0 ? ` | ${item.members.length}` : ''}}</text>
				</view>
			</view>
			<template v-if="item.members.length > 0">
				<view v-for="user in item.members" :key="user.id" class="member-list">
					<view class="">
						<view class="user-info" @tap="handleToUserMine(user)">
							<image :src="prefixFilePath(user.avatar)" class="userImg"></image>
							<view class="user-name">
								<text>{{user.nickname || user.name}}</text>
								<text class="account">{{user.account}}</text>
							</view>
						</view>
					</view>
					<view class="pic-list">
						<view class="img" :style="`width:${itemWidth}rpx;height:${itemWidth}rpx;`"
							v-for="(img, idx) in user.images" :key="idx">
							<image :src="prefixFilePath(img)" mode="widthFix" @tap="showBig(user.images, idx)"></image>
						</view>
					</view>
				</view>
			</template>
			<view class="empty-ui" v-else>
				<text>该角色暂无人报名，请点击下方报名吧~</text>
			</view>
		</view>
		<view class="box-header mt-20">
			<view class="box-header-title">
				<view class="box-header-item">
					<text>更多剧组推荐</text>
					<image src="/static/icon/dust_icon_tabs_active_star.svg" class="active-star"></image>
				</view>
			</view>
		</view>
		<view class="more-list" v-if="moreList.length > 0">
			<view class="more-list-item" v-for="(item, index) in moreList" :key="index" @tap="toOther(item)">
				<image class="more-list-img" :src="prefixFilePath(item.cover)" mode="widthFix"></image>
				<text class="more-list-title">{{item.title}}</text>
			</view>
		</view>
		<view class="empty-ui" v-else>
			<text>暂无更多剧组推荐</text>
		</view>
		<template #bottom>
			<view class="bottom-container">
				<view class="collection-wrapper" @tap="handleCollect">
					<image class="collect-icon"
						:src="detail?.is_collected ? '/static/icon/star_selected.png' : '/static/icon/star_noselected.png'">
					</image>
					<text class="font-size-small">收藏</text>
				</view>
				<view class="primary-btn" v-if="!detail.has_joined" @tap="() => popup.open()">报名</view>
				<view class="primary-btn" v-else>已报名</view>
			</view>
		</template>
	</z-paging>
</template>

<script setup>
	import {
		ref,
		computed,
		watch,
	} from 'vue';
	import {
		useStore
	} from 'vuex';
	import {
		onLoad
	} from '@dcloudio/uni-app';
	import dayjs from 'dayjs';
	import {
		getHomeTroupeDetailApi
	} from '@/api/troupe'
	import {
		formatCountToString
	} from '@/common/utils'

	const store = useStore();
	const userInfo = computed(() => store.state.userInfo)
	const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);
	const paging = ref(null)
	const gid = ref('')
	const detail = ref({})
	const isLoading = ref(true)
	// 按角色分组用户
	const groupedMembers = ref([])
	const itemWidth = ref(0)
	const moreList = [{
		id: 1,
		title: '1【火影忍者】 火隐忍者剧场团招募火隐忍者剧火隐忍者剧场团招募火隐忍者剧',
		cover: '114/c392eec671bf0455fbbaad5815c40030.png',
	}, {
		id: 2,
		title: '2【火影忍者】 火隐忍者剧场团招募火隐忍者剧火隐忍者剧场团招募火隐忍者剧',
		cover: '114/160a56d21429efa785bb0380eaa5a2dc.png',
	}]

	onLoad((option) => {
		if (option.gid) {
			gid.value = option.gid
			getHomeTroupeDetail()
		} else {
			console.error('约团 id 不能为空！！！')
		}
		calculateItemWidth()
	})

	watch(() => detail.value, (newValue) => {
		// {
		// 	"id": 2,
		// 	"cover": "114/c392eec671bf0455fbbaad5815c40030.png",
		// 	"title": "2023国际动漫展cosplay团",
		// 	"description": "一起来感受二次元的魅力，扮演你喜爱的动漫角色！",
		// 	"view_count": 2458,
		// 	"created_at": *************,
		// 	"creator": {
		// 		"id": 101,
		// 		"name": "Username1",
		// 		"avatar": "avatar/1.svg",
		// 		"account": "110001",
		// 		"nickname": "动漫达人"
		// 	},
		// 	"max_member_count": 50,
		// 	"members": [{
		// 			"id": 101,
		// 			"role": {
		// 				"id": 1,
		// 				"name": "鸣人",
		// 				"aliases": ["是鸣人啊"]
		// 			},
		// 			"avatar": "avatar/1.svg",
		// 			"images": ["114/c392eec671bf0455fbbaad5815c40030.png", "114/160a56d21429efa785bb0380eaa5a2dc.png", "114/98127932dcd3d9cb714f1442f7918152.png"],
		// 			"status": 1,
		// 			"account": "110001",
		// 			"nickname": "鸣人粉丝一号",
		// 			"username": "UserNaruto1"
		// 		},
		// 		{
		// 			"id": 102,
		// 			"role": {
		// 				"id": 1,
		// 				"name": "鸣人",
		// 				"aliases": ["是鸣人啊"]
		// 			},
		// 			"avatar": "avatar/1.svg",
		// 			"images": ["114/c392eec671bf0455fbbaad5815c40030.png"],
		// 			"status": 1,
		// 			"account": "110002",
		// 			"nickname": "鸣人真爱粉",
		// 			"username": "UserNaruto2"
		// 		},
		// 		{
		// 			"id": 103,
		// 			"role": {
		// 				"id": 2,
		// 				"name": "佐助",
		// 				"aliases": ["是佐助啊"]
		// 			},
		// 			"avatar": "avatar/1.svg",
		// 			"images": ["114/c392eec671bf0455fbbaad5815c40030.png"],
		// 			"status": 1,
		// 			"account": "110003",
		// 			"nickname": "佐助军团",
		// 			"username": "UserSasuke"
		// 		}
		// 	],
		// 	"tags": ["火影忍者", "COSPLAY", "动漫展", "二次元", "角色扮演"],
		// 	"roles": [{
		// 			"id": 1,
		// 			"name": "鸣人",
		// 			"aliases": ["是鸣人啊"]
		// 		},
		// 		{
		// 			"id": 2,
		// 			"name": "佐助",
		// 			"aliases": ["是佐助啊"]
		// 		},
		// 		{
		// 			"id": 3,
		// 			"name": "小樱",
		// 			"aliases": ["是小樱啊"]
		// 		},
		// 		{
		// 			"id": 4,
		// 			"name": "卡卡西",
		// 			"aliases": ["拷贝忍者"]
		// 		}
		// 	],
		// 	"has_joined": true,
		// 	"has_collected": false
		// }
		try {
			const result = groupMembersByRole(newValue)
			console.log('groupedMembers:', result)
			groupedMembers.value = result
		} catch (error) {
			console.error('分组处理出错:', error)
			groupedMembers.value = [] // 出错时设为空数组
		}
	}, {
		deep: true,
	})

	const groupMembersByRole = (data) => {
		if (!data?.roles || !Array.isArray(data.roles)) {
			console.warn('没有角色数据')
			return []
		}

		return data.roles.map(role => {
			// 确保members是数组，即使为空
			const members = (data.members || []).filter(member => {
				// 安全访问嵌套属性
				return member?.role?.id === role.id
			})

			return {
				role: {
					...role
				},
				members: members || [] // 确保members始终是数组
			}
		})
	}

	// 计算item宽度
	const calculateItemWidth = () => {
		uni.getSystemInfo({
			success: (res) => {
				// 获取设备宽度（单位px）
				const screenWidth = res.screenWidth

				// 将px转换为rpx（1rpx = screenWidth / 750 px）
				const screenWidthRpx = 750 // 因为设计稿通常是750rpx宽

				// 计算可用宽度（总宽度 - 左右边距 - 间距）
				const padding = 20 // 左右边距各20rpx
				const gap = 20 // item间距20rpx
				const columns = 3 // 每行显示3个

				// 计算公式：(总宽度 - 左右边距 - (列数-1)*间距) / 列数
				const width = (screenWidthRpx - padding * 2 - (columns - 1) * gap) / columns

				itemWidth.value = Math.floor(width) // 取整
			}
		})
	}

	function onRefresh() {
		getHomeTroupeDetail()
	}

	async function getHomeTroupeDetail() {
		const res = await getHomeTroupeDetailApi(gid.value)
		if (res.code === 20000) {
			detail.value = res.data
			isLoading.value = false
			console.log('***********:', JSON.stringify(res.data))
		}
	}

	function prefixFilePath(url) {
		if (!url) return '';
		if (/^https?:\/\//i.test(url)) {
			return url;
		}
		if (url.startsWith('//')) {
			return window.location.protocol + url;
		}
		if (url.startsWith('/')) {
			return window.location.origin + url;
		}
		return cloudFileUrl.value + url;
	}

	function handleToUserMine(item) {
		uni.navigateTo({
			url: '/pages/home-pages/user-mine?id=' + item.id,
		})
	}

	function showBig(url, index) {
		const urlList = url.map(item => {
			return prefixFilePath(item)
		})
		uni.previewImage({
			current: index,
			urls: urlList,
		})
	}
	
	function toOther(item) {
		uni.navigateTo({
			url: '/pages/packageA/group-booking/group-booking-detail?gid=' + item.id,
		})
	}
</script>

<style lang="scss" scoped>
	.font-size-small {
		font-size: 28rpx;
		font-weight: 400;
		line-height: 40rpx;
		font-family: Alibaba PuHuiTi 3-55 Regular;
		color: #3d3d3d;
	}

	.listBox-top {
		position: relative;
	}

	.look-bar {
		position: absolute;
		right: 0;
		bottom: 0;
		padding: 0 16rpx;
		height: 32rpx;
		display: flex;
		align-items: center;
		gap: 8rpx;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 20rpx 0rpx 0rpx 0rpx;
		color: #fff;
		font-size: 20rpx;

		.icon-hq {
			width: 24rpx;
			height: 24rpx;
		}
	}

	.listBox {
		.header-title {
			padding: 20rpx 24rpx 24rpx;
		}

		.title {
			font-weight: 700;
			font-size: 36rpx;
			color: #1F1F1F;
			line-height: 50rpx;
		}

		.line {
			height: 0rpx;
			border: 2rpx solid #D8D8D8;
			margin: 24rpx 24rpx 0;
		}
	}

	.user-bar {
		padding: 0 24rpx 12rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;

		.time-bar {
			display: flex;
			flex-direction: column;
			gap: 8rpx;
			font-size: 28rpx;
			color: #3D3D3D;

			.icon-sj {
				width: 25.98rpx;
				height: 28rpx;
				margin-right: 8rpx;
			}

			.icon-jd {
				width: 25.87rpx;
				height: 28rpx;
				margin-right: 8rpx;
			}
		}
	}

	.user-info {
		display: flex;
		align-items: center;
		font-weight: 500;
		font-size: 32rpx;
		color: #1F1F1F;

		.userImg {
			width: 56rpx;
			height: 56rpx;
			margin-right: 8rpx;
			border-radius: 50%;
		}
	}

	.labels {
		display: flex;
		align-items: center;
		gap: 16rpx;
		padding: 12rpx 24rpx 0;

		.label-item {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 52rpx;
			padding: 0 16rpx;
			background: #FE58B7;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			font-size: 24rpx;
			color: #FFFFFF;
		}
	}

	.characters {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 20rpx;
		padding: 24rpx 24rpx 32rpx;

		.characters-item {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 52rpx;
			padding: 0 16rpx;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			border: 2rpx solid #48DAEA;
			font-size: 24rpx;
			color: #48DAEA;
		}
	}

	.bottom-container {
		height: 168rpx;
		box-sizing: border-box;
		padding: 0 32rpx 50rpx;
		background-color: #fff;
		border-radius: 32rpx 32rpx 0rpx 0rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0rpx -8rpx 32rpx 0rpx rgba(0, 0, 0, 0.12);

		.collection-wrapper {
			display: flex;
			align-items: center;
			gap: 8rpx;
			font-size: 32rpx;
			color: #1f1f1f;
			line-height: 44rpx;

			.collect-icon {
				width: 48rpx;
				height: 48rpx;
			}
		}
	}

	.primary-btn {
		width: 356rpx;
		height: 80rpx;
		background: #fe58b7;
		border-radius: 40rpx 40rpx 40rpx 40rpx;
		color: #fff;
		text-align: center;
		line-height: 80rpx;
		font-weight: 500;
		font-size: 36rpx;
	}

	.box-header {
		display: flex;
		align-items: center;
		padding: 20rpx 16rpx;

		.box-header-title {
			gap: 56rpx;
			width: 100%;
			height: 32rpx;
			display: flex;
			align-items: center;
		}

		.box-header-item {
			font-size: 32rpx;
			position: relative;
			font-weight: 700;

			&::after {
				width: 80%;
				content: "";
				position: absolute;
				bottom: -2px;
				z-index: -1;
				display: block;
				height: 8px;
				border-top-left-radius: 8px;
				border-top-right-radius: 8px;
				background-color: #48daea;
				background-image: linear-gradient(to right, #48daea, #fff);
			}
		}

		.active-star {
			position: absolute;
			top: 0;
			right: -20rpx;
			width: 20rpx;
			height: 20rpx;
		}
	}

	.box-detail-wrapper {
		font-size: 24rpx;
		padding: 20rpx;
	}

	.empty-ui {
		padding: 0 20rpx;
		color: #999;
		font-size: 26rpx;
	}

	.member-list {
		padding: 0 20rpx;

		.user-info {
			display: flex;
			align-items: center;
			font-size: 24rpx;
			color: #1F1F1F;

			.userImg {
				background-color: #f9f9f9;
				width: 70rpx;
				height: 70rpx;
				margin-right: 8rpx;
				border-radius: 50%;
			}

			.user-name {
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.account {
					color: #999;
				}
			}
		}
	}

	.pic-list {
		display: flex;
		gap: 20rpx;

		.img {
			display: flex;
			justify-content: center;
			align-items: center;
			background-color: #f9f9f9;

			image {
				width: 100%;
			}
		}
	}

	.more-list {
		display: flex;
		gap: 20rpx;
		padding: 0 20rpx;

		.more-list-item {
			width: 49%;
		}

		.more-list-img {
			width: 100%;
		}

		.more-list-title {
			font-size: 28rpx;
		}
	}
</style>