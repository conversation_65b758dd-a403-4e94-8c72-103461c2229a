<template>
  <uv-index-list :index-list="indexList">
    <template v-for="(item, index) in itemArr">
      <uv-index-item>
        <uv-index-anchor :text="indexList[index]"></uv-index-anchor>
        <view class="list-cell" v-for="(cell, index) in item">
          {{ cell }}
        </view>
      </uv-index-item>
    </template>
  </uv-index-list>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import pinyin from 'js-pinyin'

const store = useStore()
const provinces = computed(() => store.state.location.provinces)


const indexList = computed(() => provinces.value.map((item) => item.abbreviation))
const itemArr = computed(() => provinces.value.map((item) => item.cities))

function getFirstLetter(name) {
  if (!name) return '#'

  try {
    // 使用 js-pinyin 库获取拼音
    const pinyinArray = pinyin.getFullChars(name)
    if (pinyinArray && pinyinArray.length > 0) {
      const firstChar = pinyinArray[0].charAt(0).toUpperCase()
      // 检查是否为有效的英文字母
      return /^[A-Z]$/.test(firstChar) ? firstChar : '#'
    }
  } catch {
  }

  // 如果都不是，返回 #
  return '#'
}

function initPinyin() {
  try {
    pinyin.setOptions({
      checkPolyphone: false,
      charCase: 0,
      type: 'array'
    })
  } catch (error) {
    console.error('拼音库初始化失败:', error)
  }
}

// 初始化拼音库
onMounted(() => {
  initPinyin()
  console.log('拼音库初始化完成')
})

</script>

<style lang="scss" scoped>
.list-cell {
  display: flex;
  box-sizing: border-box;
  width: 100%;
  padding: 10px 24rpx;
  overflow: hidden;
  color: #323233;
  font-size: 14px;
  line-height: 24px;
  background-color: #fff;
}
</style>
