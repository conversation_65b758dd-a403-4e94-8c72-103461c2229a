// 通知模块
// import {
// 	repaymentReminder
// } from '@/api/user'

const NOTICE_STORE_STATE = {
	showNotice: false,
	initNotice: false,
	noticeList: [],
}

// 公共状态模块
const noticeStore = {
	namespaced: true, // 添加命名空间
	state: {
		...NOTICE_STORE_STATE,
	},
	getters: {},
	mutations: {
		updateState(state, payload) { // 更新
			for (let key in payload) {
				state[key] = payload[key]
			}
		}
	},
	actions: {
		async getNoticeList({
			state,
			commit
		}, payload) {
			commit('updateState', {
				initNotice: true,
				showNotice: false, // 先确保清空，再请求渲染数据
			})
			// 临时数据，需要替换成接口获取
			const noticeList = [
				{
					title: '20分钟前，小胖桔子预约了一套摄影服务',
				}, {
					title: '28分钟前，小胖桔子预约了一套摄影服务',
				}, {
					title: '25分钟前，小胖桔子预约了一套摄影服务',
				}
			]
			commit('updateState', {
				showNotice: noticeList.length > 0,
				noticeList,
			})
			return state
		},
		// 关闭通知
		closeNotice(context) {
			context.commit('updateState', {
				showNotice: false,
			})
		},
		// 重置通知
		resetNotice(context) {
			context.commit('updateState', {
				initNotice: false,
				showNotice: false,
			})
		}
	}
}

export default noticeStore