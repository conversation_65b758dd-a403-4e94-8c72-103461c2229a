<template>
	<view class="container" @click.stop>
		<!-- <image class="backgound" src="/static/logo.png"  mode="aspectFit"></image> -->
		<view class="main">
			<view class="title">
				<text>发布</text>
				<image class="close" src="@/static/icon/publish/close.png" mode="aspectFit" @click="closeHandle">
				</image>
			</view>
			<view class="body">
				<view class="item-wrapper" v-for="item in menuData" :key="item.icon" @click="goToPage(item)">
					<image class="item-icon" :src="item.icon" mode="aspectFit"></image>
					<view class="item-main">
						<view class="item-title">
							<text>{{item.title}}</text>
						</view>
						<view class="item-desc">
							<text>{{item.desc}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="safe-bottom"></view>
	</view>

	<!-- <cc-myTabbar :tabBarShow="2"></cc-myTabbar> -->
</template>
<script setup lang="ts">
	import { ref, computed, onMounted, watch } from 'vue';
	import { useStore } from 'vuex';
	import { onShow, onHide } from '@dcloudio/uni-app';

	const store = useStore();
	const userInfo = computed(() => store.state.userInfo)

	interface MenuItem {
		key : string;
		icon : string | number;
		title : string;
		desc : string;
		path : string;
		isHide ?: boolean; // 控制是否隐藏
		requiredPermission ?: string; // 需要的权限
	}

	// 菜单项配置
	const menuConfigs : MenuItem[] = [
		{
			key: "dimension",
			icon: "/static/icon/publish/<EMAIL>",
			title: "发次元",
			desc: "发布次元瞬间",
			path: "/pages/packages-publish/dimension/publish",
			requiredPermission: "dimension:create" // 需要此权限才显示
		},
		{
			key: "activity",
			icon: "/static/icon/publish/<EMAIL>",
			title: "发活动",
			desc: "发布漫展活动",
			path: "/pages/packages-publish/activity/publish"
		},
		{
			key: "require",
			icon: "/static/icon/publish/<EMAIL>",
			title: "发需求",
			desc: "发布定制需求",
			path: "/pages/packageA/report/first-type",
			isHide: true // 默认隐藏
		},
		{
			key: "service",
			icon: "/static/icon/publish/<EMAIL>",
			title: "发服务",
			desc: "发布上架服务",
			path: "/pages/packageA/service-create/basic"
		}
	]

	// 动态计算菜单项
	const menuData = computed<MenuItem[]>(() => {
		const permissions = userInfo.value.permissions || []

		return menuConfigs.filter(item => {
			// 如果配置为隐藏，直接过滤掉
			if (item.isHide) return false

			// 检查是否需要特定权限
			if (item.requiredPermission && !permissions.includes(item.requiredPermission)) {
				return false
			}

			// 其他情况默认显示
			return true
		})
	})

	interface Emits {
		(e : "close") : void;
	}
	const emits = defineEmits<Emits>();

	const closeHandle = () => {
		emits("close");
	};
	const goToPage = (item : MenuItem) => {
		uni.navigateTo({
			url: item.path
		})
	};
</script>

<style lang="scss" scoped>
	.container {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.4);
	}

	.safe-bottom {
		position: absolute;
		background-color: white;
		width: 100%;
		left: 0;
		bottom: 0;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	.main {
		position: absolute;
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 616rpx;
		// bottom: 0;
		bottom: constant(safe-area-inset-bottom);
		bottom: env(safe-area-inset-bottom);
		left: 0;
		background-color: white;
		border-radius: 32rpx 32rpx 0rpx 0rpx;
		overflow: hidden;

		.title {
			position: relative;
			flex: 0 0 auto;
			height: 96rpx;
			font-size: 32rpx;
			line-height: 96rpx;
			font-weight: 400;
			text-align: center;

			.close {
				position: absolute;
				right: 32rpx;
				top: 16rpx;
				height: 48rpx;
				width: 48rpx;
			}
		}

		.body {
			display: flex;
			flex: 1 1 auto;
			flex-wrap: wrap;
			column-gap: 38rpx;
			row-gap: 56rpx;
			padding: 0 40rpx 44rpx 40rpx;

			.item-wrapper {
				position: relative;
				width: 40%;
				flex: 1 1 auto;
				max-height: 50%; // 后续需要删除
				max-width: 50%;

				.item-icon {
					position: absolute;
					top: -24rpx;
					left: -16rpx;
					height: 112rpx;
					width: 112rpx;
				}

				&:nth-child(1) .item-main {
					color: #48DAEA;
					background-color: rgba(72, 218, 234, 0.12);
				}

				&:nth-child(2) .item-main {
					color: #FE58B7;
					background-color: rgba(254, 88, 183, 0.12);
				}

				&:nth-child(3) .item-main {
					color: #2BE574;
					background-color: rgba(43, 229, 116, 0.12);
				}

				&:nth-child(4) .item-main {
					color: #654AF8;
					background-color: rgba(101, 74, 248, 0.12);
				}

			}

			.item-main {
				height: 100%;
				width: 100%;
				border-radius: 24px;
				overflow: hidden;

				.item-title {
					padding: 32rpx;
					font-size: 40rpx;
					font-weight: 700;
					text-align: center;
				}

				.item-desc {
					padding: 0 32rpx;
					font-size: 24rpx;
					font-weight: 400;
				}
			}
		}


		.arrow-down {
			position: relative;

			&:after {
				position: absolute;
				content: '';
				bottom: -20rpx;
				left: 0;
				right: 0;
				margin: auto;
				width: 50rpx;
				height: 50rpx;
				transform: rotate(45deg);
				background-color: inherit;
				z-index: 0;
				box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.1);
				border-radius: 2px;
			}

			&::before {
				position: absolute;
				content: '';
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: inherit;
				border-radius: 20rpx;
				z-index: 1;
			}

		}

		.tabbar-box-item {
			// position: relative;
			// 设置最多2行
			flex: 1 1 35%;
			width: 100%;
			z-index: 3;
			margin: 10upx;
			color: $uni-color-subtitle;
			text-align: center;
			font-size: $uni-font-size-base;

			.box-image {
				width: 100%;
				height: $uni-img-size-lg;
			}
		}

	}
</style>