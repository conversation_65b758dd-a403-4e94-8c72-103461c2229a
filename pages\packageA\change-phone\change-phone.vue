<template>
	<view class="app-container">
		<Navbar :type="1" :leftText="form.isEdit === '1' ? '修改手机号' : '绑定手机号'" leftWidth="400rpx"></Navbar>
		<view class="edit-container">
			<uv-form labelPosition="left" :model="form">
				<uv-form-item prop="phone">
					<view class="form-item-input">
						<uv-input v-model="form.phone" border="none" placeholder="请输入手机号" :clearable="true" />
					</view>
				</uv-form-item>
				<uv-form-item prop="code">
					<view class="form-item-input">
						<uv-input v-model="form.code" border="none" type="number" placeholder="请输入验证码"
							:clearable="false" customStyle="padding:14rpx 0;" maxlength="6" fontSize="32rpx"
							color="#666">
							<template v-slot:suffix>
								<uv-code ref="uCode" @change="codeChange" seconds="60" changeText="X 秒后重新发送" />
								<uv-button @click="getCode" :text="tips" :hairline="false" />
							</template>
						</uv-input>
					</view>
				</uv-form-item>
			</uv-form>
			<button class="btn-submit" :loading="loading" type="primary" @tap="handleSubmit">保存</button>
		</view>
	</view>
</template>

<script setup>
	import {
		computed,
		reactive,
		ref
	} from 'vue'
	import {
		useStore
	} from 'vuex';
	import {
		onLoad,
		onShow,
	} from "@dcloudio/uni-app";
	import {
		bindPhone
	} from '@/api/user'
	import {
		smsCodeApi
	} from '@/api/common'

	const store = useStore();
	const userInfo = computed(() => store.state.userInfo)
	const form = reactive({
		phone: userInfo.value.phone,
		code: '',
		code_id: '',
		isEdit: '1',
	})

	const loading = ref(false)
	const tips = ref('');
	const uCode = ref(null);
	
	onLoad((query) => {
		console.log('query:', query)
		if (query.isEdit) {
			form.isEdit = query.isEdit
		}
	})

	function codeChange(text) {
		tips.value = text;
	}

	function getCode() {
		// 验证输入是否为手机号
		const reg = /^[1][3-9]\d{9}$/;
		if (!reg.test(form.phone)) {
			// 如果不是手机号格式，清空输入
			form.phone = '';
			uni.showToast({
				title: '手机号码格式错误',
				icon: 'none'
			});
			return;
		}
		uni.showLoading({
			title: '正在获取验证码'
		});
		smsCodeApi(form.phone).then((res) => {
			uni.hideLoading();
			if (res.code === 20000) {
				// 这里此提示会被this.start()方法中的提示覆盖
				uni.showToast({
					title: '验证码已发送',
					icon: 'none'
				});
				form.code_id = res.data.code_id
				// 通知验证码组件内部开始倒计时
				uCode.value.start();
			}
		}).catch(e => {
			uni.hideLoading();
		})
	}

	function handleSubmit() {
		console.log('form.phone:', form)
		// 验证输入是否为手机号
		const reg = /^[1][3-9]\d{9}$/;
		if (!reg.test(form.phone)) {
			// 如果不是手机号格式，清空输入
			form.phone = '';
			uni.showToast({
				title: '手机号码格式错误',
				icon: 'none'
			});
			return;
		}
		if (!form.code_id) {
			uni.showToast({
				title: '请先获取验证码',
				icon: 'none'
			});
			return;
		}
		if (!form.code) {
			uni.showToast({
				title: '请输入验证码',
				icon: 'none'
			});
			return;
		}
		loading.value = true
		bindPhone({
			phone: form.phone,
			code_id: form.code_id,
			code: form.code,
		}).then(res => {
			loading.value = false
			if (res.code === 20000) {
				store.commit('userInfo/UPDATE_USER_INFO', {
					phone: form.phone
				})
				store.dispatch('userInfo/getUserInfo')
				uni.navigateBack({
					success() {
						uni.showToast({
							title: form.isEdit === '1' ? '手机号保存成功' : '手机号绑定成功',
							icon: 'none',
						})
					}
				})
			} else if (res.code > 500) {
				uni.showToast({
					title: res.msg,
					icon: 'none',
				})
			}
		}).catch(e => {
			loading.value = false
		})
	}
</script>

<style lang="scss" scoped>
	:deep(.uv-button--info) {
		color: #85878d !important;
		border-width: 0 !important;
		font-size: 28rpx !important;
		height: 40rpx !important;
		padding: 0 !important;
	}

	.app-container {
		background-color: #F5F5F5;
	}

	.edit-container {
		flex: 1;
		padding: 10rpx 30rpx 0;
	}

	.tips {
		color: #9D9EA6;
		font-size: 26rpx;
		margin-top: 24rpx;
	}

	.form-item-input {
		background-color: #fff;
		border-radius: 12rpx;
		height: 90rpx;
		display: flex;
		padding: 0 26rpx;
	}

	.btn-submit {
		margin-top: 50rpx;
	}
</style>