<template>
  <z-paging ref="paging" class="app-container" :fixed="false" :safe-area-inset-bottom="true" :use-safe-area-placeholder="true">
    <template #empty>
      <uv-empty />
    </template>
    <template #top>
      <Navbar :type="1" :leftText="`${typeTitle || ''}${categoryTitle || ''}`"></Navbar>
    </template>

    <view class="wrapper">
      <view class="header" @click="() => handleTo('description')">
        <view class="empty-info" v-if="isEmptyDetail">编辑我的服务信息</view>
        <dust-image-base wrapperRadius="20rpx" :src="serivceDetail.cover" :local="false" aspect-ratio="16:9"
          v-if="!isEmptyDetail"></dust-image-base>
        <view v-if="!isEmptyDetail" class="description">
          <view class="title">服务标题</view>
          <view class="description-text">{{ serivceDetail.name }}</view>
        </view>
        <view v-if="!isEmptyDetail" class="description">
          <view class="title">服务描述</view>
          <view class="description-text">{{ formatDescription(serivceDetail) }}</view>
        </view>
      </view>

      <view class="content">
        <view class="flex items-center justify-between">
          <text class="title">{{ comboTitleMap.title }}</text>
          <view class="flex items-center add-combo-btn" @click="() => handleTo('add-combo')">
            <image class="icon" src="../static/service/add-circle.svg"></image>
            <text>{{ comboTitleMap.addCombo }}</text>
          </view>
        </view>
        <view class="combolist-container">
			<empty-list v-if="dataList.length === 0">
				<text>{{ comboTitleMap.emptyCombo }}</text>
			</empty-list>
          <uni-list v-else class="dust-uni-list" :border="false">
            <uni-list-item :border="false" class="dust-uni-list-item" v-for="(item, index) in dataList" :key="item.id">
              <template v-slot:body>
                <view class="combo-item-wrapper" @click="() => handleTo('service-expo', item)">
                  <expo-combo-item :serviceId="item.id" :dateSlot="item.date_slot" :item="item.expo" />
                </view>
              </template>
            </uni-list-item>
          </uni-list>
        </view>
      </view>
    </view>
  </z-paging>
  <publish-notice :cutdown="cutdown" v-if="showNotice" @close="onCloseNotice" @confirm="onConfirmNotice" />
</template>

<script setup>
import { ref, computed, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { onLoad } from '@dcloudio/uni-app'
import {
  getMineServiceDetail,
} from '@/api/mineService'
import { useTitle } from './hooks/useTitle'
import useNoticeManager from "@/components/publish-notice/useNoticeManager.ts";
import ExpoComboItem from './components/expo-combo-item.vue'

const store = useStore()
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

function prefixFilePath(path) {
  if (!path) return ''
  return cloudFileUrl.value + path
}

const paging = ref(null)
const serivceDetail = ref(null)
const dataList = ref([])

const { typeTitle, categoryId, categoryTitle, comboTitleMap } = useTitle();
const {cutdown, showNotice, onConfirmNotice, onCloseNotice} = useNoticeManager("expo", categoryTitle, 10);


const isEmptyDetail = computed(() => {
  return !serivceDetail.value?.cover && !serivceDetail.value?.name
})

uni.$on('service-create-detail-expo:reload-data', async () => {
  const { detail, list: detailList } = await queryServiceDetail()
  serivceDetail.value = detail
  dataList.value = detailList
})

onLoad(async () => {
  const { detail, list: detailList } = await queryServiceDetail()
  serivceDetail.value = detail
  dataList.value = detailList
})

function formatDescription(item) {
  try {
    return JSON.parse(item.detail).instructions
  } catch (error) {
    return ''
  }
}

async function queryServiceDetail() {
  function findLastItem(arr) {
    return arr[arr.length - 1]
  }

  if (!categoryId.value) return null
  try {
    const res = await getMineServiceDetail({
      categories: [categoryId.value],
    })
    if (res.code === 20000) {
      const list = res.data.results.filter(item => !item.is_daily).filter(item => !!item.expo)
      const detail = findLastItem(res.data.results.filter(item => !item.is_daily))
      return {
        detail: detail,
        list: list
      }
    }
    return null
  } catch (error) {
    console.log(error, 'getMineServiceDetail service')
    return null
  }
}

const handleTo = (id, item) => {
  let url = ''
  switch (id) {
    case 'description':
      url = `/pages/packageA/service-create-description/basic?categoryTitle=${categoryTitle.value}&typeTitle=${typeTitle.value}`
      break
    case 'add-combo':
      if (!serivceDetail.value) {
        uni.showToast({ title: '请先编辑服务内容', icon: 'none' })
        return
      }
      const hadSelectedExpo = dataList.value.map(item => item.expo_id).join(',')
      url = `/pages/packageA/service-expo/add-combo?categoryTitle=${categoryTitle.value}&typeTitle=${typeTitle.value}&hadSelectedExpo=${hadSelectedExpo}`
      break
    case 'service-expo':
      url = `/pages/packageA/service-expo/service-expo?id=${item.expo_id}&serviceId=${item.id}&categoryTitle=${categoryTitle.value}&typeTitle=${typeTitle.value}&isOnSale=${item.is_on_sale ? '1' : '0'}`
      break
  }
  url && uni.navigateTo({
    url
  })
}
</script>

<style scoped lang='scss'>
.wrapper {
  box-sizing: border-box;
  margin-top: 24rpx;
  padding: 0 24rpx;
  flex: 1;
  display: flex;
  flex-direction: column;

  .header {
    width: 100%;

    .empty-info {
      width: 100%;
      height: 368rpx;
      background: #F7F8FA;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #FE58B7;
    }

    .cover-wrapper {
      border-radius: 20rpx 20rpx 20rpx 20rpx;
    }

    .description {
      margin-top: 32rpx;

      .title {
        font-weight: 500;
        font-size: 32rpx;
        color: #3D3D3D;
      }

      .description-text {
        margin-top: 16rpx;
        font-size: 28rpx;
        color: #85878D;
      }
    }
  }

  .content {
    margin-top: 40rpx;
    flex: 1;
    display: flex;
    flex-direction: column;

    .title {
      font-size: 32rpx;
      color: #3D3D3D;
    }

    .add-combo-btn {
      font-size: 28rpx;
      color: #FE58B7;
      gap: 4rpx;

      .icon {
        width: 28rpx;
        height: 28rpx;
      }
    }

    .combolist-container {
      margin-top: 24rpx;
      flex: 1;
      padding-bottom: 50rpx;

      .empty-list {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        color: #85878D;

        .icon {
          width: 380rpx;
          height: 380rpx;
        }
      }

      .combo-item-wrapper {
        width: 100%;
      }
    }
  }
}
</style>