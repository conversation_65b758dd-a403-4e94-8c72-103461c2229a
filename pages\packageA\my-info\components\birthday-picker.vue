<template>
  <view class="dust-calendar">
    <view class="header">
      <view class="item" @click="() => yearPicker.open()">
        <text>{{ date.getFullYear() }}</text>
        <image class="icon" src="/static/icon/<EMAIL>"></image>
      </view>
      <view class="item" @click="() => monthPicker.open()">
        <text>{{ date.getMonth() + 1 }}月</text>
        <image class="icon" src="/static/icon/<EMAIL>"></image>
      </view>
    </view>
    <uv-calendars :date="defaultDate" ref="calendar" insert mode="single" :showMonth="false" color="#FE58B7"
      @change="handleChange" />
  </view>

  <uv-picker closeOnClickOverlay ref="monthPicker" :columns="monthColumns" round="32rpx" confirmColor="#FE58B7"
    @confirm="monthConfirm"></uv-picker>
  <uv-picker closeOnClickOverlay ref="yearPicker" :columns="yearColumns" round="32rpx" confirmColor="#FE58B7"
    @confirm="yearConfirm"></uv-picker>
</template>

<script setup>
import { ref, onMounted } from 'vue'
const props = defineProps({
  defaultDate: {
    type: Date,
    default: new Date()
  }
})
const emit = defineEmits(['change'])
const date = ref(new Date())
const calendar = ref(null)
const defaultDate = ref([])

const monthPicker = ref(null)
const monthColumns = ref([[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]])
const monthConfirm = (e) => {
  calendar.value.setDate(`${date.value.getFullYear()}-${e.value[0]}`)
  date.value = new Date(`${date.value.getFullYear()}-${e.value[0]}`)
}

const yearPicker = ref(null)
const yearColumns = ref((() => {
  const currentYear = new Date().getFullYear();
  return [Array(50).fill(0).map((_, i) => currentYear - i)];
})())
const yearConfirm = (e) => {
  calendar.value.setDate(`${e.value[0]}-${date.value.getMonth() + 1}`)
  date.value = new Date(`${e.value[0]}-${date.value.getMonth() + 1}`)
}

onMounted(() => {
  const d = new Date(props?.defaultDate)
  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const date = d.getDate()
  defaultDate.value = `${year}-${month}-${date}`
})


const handleChange = (e) => {
  emit('change', e.fulldate)
}
</script>

<script>
export default {
  options: { styleIsolation: "shared" }
}
</script>

<style lang='scss' scoped>
.dust-calendar {
  width: 100%;

  .header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .item {
      width: 166rpx;
      height: 80rpx;
      display: flex;
      gap: 14rpx;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #1F1F1F;
      line-height: 48rpx;

      .icon {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
}

:deep() {
  .uv-calendar__header {
    display: none !important;
  }

  .uv-calendar-item__weeks-box {
    border-radius: 80rpx;
  }

  .uv-calendar-item__weeks-box-item {
    height: 80rpx !important;
    width: 96rpx !important;
  }

  .uv-calendar-item__weeks-lunar-text {
    display: none !important;
  }

  .uv-calendar__weeks-day {
    border: none;
  }
}
</style>