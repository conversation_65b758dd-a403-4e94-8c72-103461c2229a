<!-- 举报 -->
<template>
	<view class="container">
		<Navbar :type="1"  leftText="客服人员"></Navbar>
		<view class="main">
			<image src="@/static/my/<EMAIL>" mode="aspectFit"></image>
		</view>
	</view>
</template>
<script setup lang="ts">
	import { ref, toRaw, onUnmounted, watchEffect, computed } from 'vue';
	import { reportService, reportComiket } from "@/api/report";
	import { onLoad } from "@dcloudio/uni-app"

	const reason = ref("");
	const desc = ref("");
	onLoad(query => {
		reason.value = query.text;
		desc.value = query.value;
	});
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		height: 100%;
		box-sizing: border-box;
		
		padding-bottom: constant(safe-area-inset-bottom);;
		padding-bottom: env(safe-area-inset-bottom);
	}
	
	.main {
		flex: 1 1 auto;
		height: 100%;
		display: flex;
		flex-direction: column;
		gap: 15rpx;
		box-sizing: border-box;
		padding: 32rpx 32rpx 24rpx;
		overflow-y: hidden;
	}
	
	.reason-container {
		display: flex;
		align-items: center;
		gap: 34rpx;
		height: 80rpx;
		width: 100%;
		border-radius: 16rpx;
		padding: 0 24rpx;
		box-sizing: border-box;
		background-color:  rgba(247, 248, 250);
	}
	.desc-container {
		display: flex;
		flex: 0 0 auto;
		width: 100%;
		height: 292rpx;
		flex-direction: column;
		gap: 20rpx;
		background-color: rgba(247, 248, 250);
		border-radius: 16rpx;
		padding: 24rpx;
		
		box-sizing: border-box;
		color: rgba(31, 31, 31);
		.title {
			width: 100%;
			background-color: transparent;
			flex: 0 0 auto;
			height: 56rpx;
			font-size: 40rpx;
			line-height: 56rpx;
			font-weight: 700;
		}
		.title-placeholder {
			color: rgba(133, 135, 141);
		}
		.desc {
			width: 100%;
			flex: 1 1 auto;
			background-color: transparent;
			font-size: 36rpx;
			line-height: 56rpx;
			font-weight: 400;
		}
		.desc-placeholder {
			color: rgba(133, 135, 141);
		}
	}
</style>
