<!-- 参考 三方库zxz-uni-data-select实现 -->
<template>
	<view class="uni-stat__select">
		<!-- hide-on-phone -->
		<view class="uni-stat-box" :class="{'uni-stat__actived': selectedOptions.length}">
			<view class="uni-select" style="height: 100%;"
				:class="{'uni-select--disabled':disabled}">
				<view class="uni-select__input-box" style="height: 100%;" @tap.stop="navigateHandle">
					<view class="" style="display: flex;flex-wrap: wrap;width: 100%;" v-if="selectedOptions.length">
						<view class="tag-calss"
							v-for="item in selectedOptions"
							:key="item.value">
							<span class="text">{{item.text}}</span>
							<view class="" @tap.stop="delectItemHandle(item)">
								<uni-icons type="clear" style="margin-left: 4px;" color="#c0c4cc" />
							</view>
						</view>
					</view>
				
					<view v-else class="uni-select__input-text uni-select__input-placeholder">{{placeholder}}</view>
					<uni-icons v-if="(selectedOptions.length >0 && clear && !disabled)"
						type="clear" color="#c0c4cc" size="24" style="position: absolute;right: 0;" @tap.stop="clearAllHandle" />
					<uni-icons style="right: 0;position: absolute;" v-else type='right'
						size="14" color="#999" />
				</view>
			</view>
		</view>
	</view>
</template>
<script setup lang="ts">

import { computed, reactive, ref, PropType, watch, toRaw } from 'vue';
	
	interface LocalData {
		text: string,		// 显示文本
		value: string | number // 选项对应的key
	}
	
	const props = defineProps({
		localdata: {
			type: Array as PropType<LocalData[]>,
			default: () => []
		},
		
		modelValue: {
			type: Array as PropType<string[] | number[]>,
			default: () => []
		},
				
		placeholder: {
			type: String,
			default: '请选择'
		},
		
		clear: {
			type: Boolean,
			default: true
		},
		nav: {
			type: Boolean,
			default: true
		},
		
		disabled: {
			type: Boolean,
			default: false
		},
		
	});
	const emits = defineEmits(["update:modelValue", "navigate"]);
	
	// 监听props变化并显示选择项
	const selectedOptions = ref<LocalData[]>([]);
	watch(
		[() => props.localdata, () => props.modelValue],
		([localData, modelValue]) => {
		const selectedKeys = (modelValue || []) as Array<string | number>;
		((localData || []) as any).forEach((item: LocalData) => {
			const isSected = selectedKeys.findIndex(selectedValue => item.value == selectedValue) >= 0;
			const selectedIndex = selectedOptions.value.findIndex(option => item.value == option.value);
			if (isSected && selectedIndex < 0) { // 新增
				selectedOptions.value.push(item);
			} else if (!isSected && selectedIndex >= 0) { // 删除
				selectedOptions.value.splice(selectedIndex, 1);
			}
		})
	}, {immediate: true, deep: true});
	
	const delectItemHandle = function(item: LocalData) {
		if (props.disabled) return;
		const selectedKeys: Array<string | number> = [];
		selectedOptions.value.forEach(option => {
			option.value != item.value && selectedKeys.push(option.value)
		});
		emits("update:modelValue", selectedKeys);
	}
	
	const clearAllHandle = function() {
		if (props.disabled) return;
		emits("update:modelValue", []);
	}
	
	const navigateHandle = function() {
		if (props.disabled) return;
		emits("navigate");
	}

</script>

<style lang="scss">
	$uni-base-color: #6a6a6a !default;
	$uni-main-color: #333 !default;
	$uni-secondary-color: #909399 !default;
	$uni-border-3: #e5e5e5;


	/* #ifndef APP-NVUE */
	@media screen and (max-width: 500px) {
		.hide-on-phone {
			display: none;
		}
	}

	/* #endif */
	.uni-stat__select {
		display: flex;
		align-items: center;
		// padding: 15px;
		// cursor: pointer;
		width: 100%;
		flex: 1;
		box-sizing: border-box;
	}

	.uni-stat-box {
		width: 100%;
		flex: 1;
	}

	.uni-stat__actived {
		width: 100%;
		flex: 1;
		// outline: 1px solid #2979ff;
	}

	.uni-label-text {
		font-size: 14px;
		font-weight: bold;
		color: $uni-base-color;
		margin: auto 0;
		margin-right: 5px;
	}

	.uni-select {
		font-size: 14px;
		border: 1px solid $uni-border-3;
		box-sizing: border-box;
		border-radius: 4px;
		padding: 0 5px;
		padding-left: 10px;
		position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		user-select: none;
		/* #endif */
		flex-direction: row;
		align-items: center;
		border-bottom: solid 1px $uni-border-3;
		width: 100%;
		flex: 1;
		height: 35px;
		min-height: 35px;

		&--disabled {
			background-color: #f5f7fa;
			cursor: not-allowed;
		}
	}

	.uni-select__label {
		font-size: 16px;
		// line-height: 22px;
		min-height: 35px;
		height: 35px;
		padding-right: 10px;
		color: $uni-secondary-color;
	}

	.uni-select__input-box {
		width: 100%;
		height: 35px;
		position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex: 1;
		flex-direction: row;
		align-items: center;

		.tag-calss {
			font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;
			font-weight: 400;
			-webkit-font-smoothing: antialiased;
			-webkit-tap-highlight-color: transparent;
			font-size: 12px;
			border: 1px solid #d9ecff;
			border-radius: 4px;
			white-space: nowrap;
			height: 24px;
			padding: 0 4px 0px 8px;
			line-height: 22px;
			box-sizing: border-box;
			margin: 2px 0 2px 6px;
			display: flex;
			max-width: 100%;
			align-items: center;
			background-color: #f4f4f5;
			border-color: #e9e9eb;
			color: #909399;

			.text {
				font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;
				font-weight: 400;
				-webkit-font-smoothing: antialiased;
				-webkit-tap-highlight-color: transparent;
				font-size: 12px;
				white-space: nowrap;
				line-height: 22px;
				color: #909399;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}

	.uni-select__input {
		flex: 1;
		font-size: 14px;
		height: 22px;
		line-height: 22px;
	}

	.uni-select__input-plac {
		font-size: 14px;
		color: $uni-secondary-color;
	}

	.uni-select__selector__down {
		top: calc(100% + 12px);

		.uni-popper__arrow {
			transform: rotateX(0deg);
			top: -6px;
		}
	}

	.uni-select__selector__upwards {
		bottom: calc(100% + 12px);

		.uni-popper__arrow {
			transform: rotateX(180deg);
			bottom: -6px;
		}
	}

	.uni-select__selector {
		/* #ifndef APP-NVUE */
		box-sizing: border-box;
		/* #endif */
		position: absolute;

		left: 0;
		width: 100%;
		background-color: #FFFFFF;
		border: 1px solid #EBEEF5;
		border-radius: 6px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
		z-index: 3;
		padding: 4px 0;
	}

	.uni-select__selector-scroll {
		/* #ifndef APP-NVUE */
		max-height: 200px;
		box-sizing: border-box;
		/* #endif */
	}

	.uni-select__selector-empty,
	.uni-select__selector-item {
		/* #ifndef APP-NVUE */
		display: flex;
		// cursor: pointer;
		/* #endif */
		line-height: 35px;
		font-size: 14px;
		text-align: center;
		/* border-bottom: solid 1px $uni-border-3; */
		padding: 0px 10px;
	}

	.uni-select__selector-item:hover {
		background-color: #f9f9f9;
	}

	.uni-select__selector-empty:last-child,
	.uni-select__selector-item:last-child {
		/* #ifndef APP-NVUE */
		border-bottom: none;
		/* #endif */
	}

	.uni-select_selector-item_active {
		color: #409eff;
		font-weight: bold;
		background-color: #f5f7fa;
		border-radius: 3px;
	}

	.uni-select__selector__disabled {
		opacity: 0.4;
		cursor: default;
	}

	/* picker 弹出层通用的指示小三角 */
	.uni-popper__arrow,
	.uni-popper__arrow::after {
		position: absolute;
		display: block;
		width: 0;
		height: 0;
		border-color: transparent;
		border-style: solid;
		border-width: 6px;
	}

	.uni-popper__arrow {
		filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));

		left: 10%;
		margin-right: 3px;
		border-top-width: 0;
		border-bottom-color: #EBEEF5;
	}

	.uni-popper__arrow::after {
		content: " ";
		top: 1px;
		margin-left: -6px;
		border-top-width: 0;
		border-bottom-color: #fff;
	}

	.uni-select__input-text {
		// width: 280px;
		width: 90%;
		color: $uni-main-color;
		white-space: nowrap;
		text-overflow: ellipsis;
		-o-text-overflow: ellipsis;
		overflow: hidden;
	}

	.uni-select__input-placeholder {
		color: $uni-base-color;
		font-size: 12px;
	}

	.uni-select--mask {
		position: fixed;
		top: 0;
		bottom: 0;
		right: 0;
		left: 0;
		z-index: 2;
	}
</style>