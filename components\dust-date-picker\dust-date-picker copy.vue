<template>
  <view class="dust-calendar">
    <view class="header">
      <view class="item" @tap="() => yearPicker.open()">
        <text>{{ yearColumns[0]?.[defaultYearIndex] ?? currentYear }}年</text>
        <image class="icon" src="/static/icon/<EMAIL>"></image>
      </view>
      <view class="item" @tap="() => monthPicker.open()">
        <text>{{ monthColumns[0]?.[defaultMonthIndex] ?? currentMonth }}月</text>
        <image class="icon" src="/static/icon/<EMAIL>"></image>
      </view>
    </view>
    <uv-calendars :date="defaultDate" ref="calendar" insert :mode="mode" :showMonth="false" color="#FE58B7"
      @change="handleChange" />
    <view class="time-list" v-if="showTimeSlot">
      <view v-for="(item, index) in timeArr" :key="index" :class="activeTimeId[index] == item.value ? 'activeTiem' : ''"
        @tap="addTime(index)">
        <text>{{ item.name }}</text>
        <text>{{ item.time }}</text>
      </view>
    </view>
    <slot name="footer">
      <view class="footer flex">
        <view class="primary btn" @tap="handleConfirm">{{ confirmText }}</view>
      </view>
    </slot>
  </view>

  <uv-picker closeOnClickOverlay ref="monthPicker" :columns="monthColumns" round="32rpx" confirmColor="#FE58B7"
    @confirm="monthConfirm" :defaultIndex="[defaultMonthIndex]"></uv-picker>
  <uv-picker closeOnClickOverlay ref="yearPicker" :columns="yearColumns" round="32rpx" confirmColor="#FE58B7"
    @confirm="yearConfirm" :defaultIndex="[defaultYearIndex]"></uv-picker>
</template>

<script setup>
import { ref, computed, onMounted, watchEffect } from 'vue'
import dayjs from 'dayjs'
const props = defineProps({
  showTimeSlot: {
    type: Boolean,
    default: false
  },
  timeSlotCannotNull: {
    type: Boolean,
    default: true
  },
  timeSlot: {
    type: Number,
    default: 0
  },
  dateSlot: {
    type: [Array, String],
    required: true
  },
  canEarlyToday: {
    type: Boolean,
    default: false
  },
  confirmText: {
    type: String,
    default: '保存'
  },
  /**
   * 年份范围
   * 第一个值表示最小年份
   * 第二个值表示最大年份
   */
  yearRange: {
    type: Array,
    default: [0, 10]
  },
  mode: {
    validator(value, props) {
      // 如果mode为range，则dateSlot必须是一个数组
      if (['range', 'multiple'].includes(value) && !Array.isArray(props.dateSlot)) {
        return false
      }
      // 如果mode为single，则dateSlot必须是一个字符串
      if (value === 'single' && typeof props.dateSlot !== 'string') {
        return false
      }
      return ['single', 'multiple', 'range'].includes(value)
    },
    default: 'multiple'
  }
})
const emit = defineEmits(['cancel', 'confirm'])
// 添加当前年份/月份的缓存
const currentYear = ref(new Date().getFullYear())
const currentMonth = ref(new Date().getMonth() + 1)
const date = ref(new Date())
const calendar = ref(null)
const dateSelection = ref([])
const defaultDate = ref([])
const monthPicker = ref(null)
const monthColumns = ref([[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]])
const monthConfirm = (e) => {
	console.log('monthConfirm:', e)
  // 确保使用完整日期格式 YYYY-MM-DD
    const year = date.value.getFullYear()
    const safeDateStr = `${year}/${e.value[0].toString().padStart(2, '0')}/01`
    
   // 使用完整日期设置日历
   calendar.value.setDate(safeDateStr)
	console.log('monthConfirm calendar:', calendar.value)
   
   // 使用 dayjs 创建日期对象
   date.value = dayjs(safeDateStr).toDate()
	console.log('monthConfirm date:', date.value)
}

const yearPicker = ref(null)
const yearColumns = computed(() => {
  const currentYearVal = new Date().getFullYear()
  const minYear = currentYearVal + props.yearRange[0]
  const maxYear = currentYearVal + props.yearRange[1]
  const years = []
  
  for (let year = minYear; year <= maxYear; year++) {
    years.push(year)
  }
  return [years]
})

const defaultYearIndex = computed(() => {
  const targetYear = date.value.getFullYear()
  const years = yearColumns.value[0] || []
  console.log('defaultYearIndex:', targetYear)
  // 确保索引不会超出范围
  const index = years.indexOf(targetYear)
  return index >= 0 ? index : 0
})

const defaultMonthIndex = computed(() => {
  const targetMonth = date.value.getMonth() + 1
  console.log('defaultMonthIndex:', targetMonth)
  // 月份始终在1-12范围，直接计算即可
  return Math.max(0, Math.min(11, targetMonth - 1))
})

// 监听并更新当前年份/月份
watchEffect(() => {
  if (yearColumns.value[0]?.length) {
    currentYear.value = yearColumns.value[0][defaultYearIndex.value] || new Date().getFullYear()
  }
  if (monthColumns.value[0]?.length) {
    currentMonth.value = monthColumns.value[0][defaultMonthIndex.value] || new Date().getMonth() + 1
  }
})

  console.log('YearIndex:', defaultYearIndex.value, 
              'Value:', yearColumns.value[0][defaultYearIndex.value])
  console.log('MonthIndex:', defaultMonthIndex.value,
              'Value:', monthColumns.value[0][defaultMonthIndex.value])

const yearConfirm = (e) => {
	console.log('yearConfirm:', e)
	  
	  // 确保使用完整日期格式 YYYY-MM-DD
	  const month = (date.value.getMonth() + 1).toString().padStart(2, '0')
	  const safeDateStr = `${e.value[0]}/${month}/01`
	  
	  // 使用完整日期设置日历
	  calendar.value.setDate(safeDateStr)
	console.log('yearConfirm calendar:', calendar.value)
	  
	  // 使用 dayjs 创建日期对象
	  date.value = dayjs(safeDateStr).toDate()
	console.log('yearConfirm date:', date.value)
}

const timeArr = ref([
  {
    name: '上午',
    time: '8:00-12:00',
    value: 1
  },
  {
    name: '下午',
    time: '12:00-18:00',
    value: 2
  },
  {
    name: '晚上',
    time: '18:00-23:00',
    value: 4
  },
  {
    name: '夜间',
    time: '23:00-8:00',
    value: 8
  }
]);
const activeTimeId = ref([0, 0, 0, 0]);

function checkBinary(n) {
  if (!n) return [0, 0, 0, 0]
  const bits = [1, 2, 4, 8]; // 0001, 0010, 0100, 1000
  const result = new Array(4).fill(0);

  for (let i = 0; i < bits.length; i++) {
    if (n & bits[i]) {
      result[i] = bits[i];
    }
  }

  return result;
}

onMounted(() => {
	console.log('onMounted:', props.mode, props.dateSlot)
  if (props.mode === 'single' && props.dateSlot) {
    defaultDate.value = dayjs(props.dateSlot).format('YYYY/MM/DD')
    date.value = new Date(defaultDate.value)
  }

  if (props.mode === 'multiple' && props.dateSlot) {
    defaultDate.value = props.dateSlot.map(item => dayjs(item).format('YYYY/MM/DD'))
	console.log('defaultDate.value:', defaultDate.value)
    date.value = new Date(defaultDate.value[0])
  }

  dateSelection.value = defaultDate.value
  activeTimeId.value = checkBinary(props?.timeSlot)
})

function addTime(index) {
  if (activeTimeId.value[index] == timeArr.value[index].value) {
    activeTimeId.value.splice(index, 1, 0);
  } else {
    activeTimeId.value.splice(index, 1, timeArr.value[index].value);
  }
}

const handleChange = (e) => {
  if (props.mode === 'multiple') {
    dateSelection.value = e.multiple.data
  } else if (props.mode === 'range') {
    dateSelection.value = e.range
  } else {
    dateSelection.value = e.fulldate
  }
}

function checkEarlyToday(date) {
  const selectedDate = new Date(date);
  const today = new Date();

  if (today.getDate() > selectedDate.getDate()) {
    uni.showToast({
      title: '选择的日期不能早于今天',
      icon: 'none'
    })
    return false
  }
  return true
}

function handleConfirm() {
  const timeSlot = activeTimeId.value.reduce((pre, cur) => pre + cur, 0);
	console.log('handleConfirm dateSelection0:',dateSelection.value)
  if (dateSelection.value.length === 0) {
    const defaultValue = dayjs(date.value).format('YYYY/MM/DD')
	console.log('handleConfirm defaultValue:', props.mode, defaultValue)
    dateSelection.value = props.mode === 'multiple' ? [defaultValue] : defaultValue
  }
	console.log('handleConfirm dateSelection1:',dateSelection.value)
  if (dateSelection.value.length === 0) {
    uni.showToast({
      title: '请至少选择一个日期',
      icon: 'none'
    })
    return
  }
  console.log('handleConfirm showTimeSlot:',props.showTimeSlot)
  console.log('handleConfirm timeSlot:',timeSlot)
  console.log('handleConfirm timeSlotCannotNull:',props.timeSlotCannotNull)
  if (props.showTimeSlot && !timeSlot && props.timeSlotCannotNull) {
    uni.showToast({
      title: '请至少选择一个时间段',
      icon: 'none'
    })
    return
  }

  if (!props.canEarlyToday) {
    for (let i = 0; i < dateSelection.value.length; i++) {
      if (!checkEarlyToday(dateSelection.value[i])) {
        return
      }
    }
  }

  const data = {
    dateSlot: dateSelection.value
  }

  if (props.showTimeSlot) {
    data.timeSlot = timeSlot
  }

  emit('confirm', data)
}

</script>

<script>
export default {
  options: { styleIsolation: "shared" }
}
</script>

<style lang='scss' scoped>
.dust-calendar {
  width: 100%;
  box-sizing: border-box;
  padding: 20rpx 40rpx 28rpx;

  .header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .item {
      width: 166rpx;
      height: 80rpx;
      display: flex;
      gap: 14rpx;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #1F1F1F;
      line-height: 48rpx;

      .icon {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }

  .time-list {
    display: flex;
    align-items: center;
    gap: 24rpx;
    margin-top: 48rpx;

    .activeTiem {
      background-color: #fe58b7;

      text {
        color: #fff;
      }
    }

    view {
      display: flex;
      flex-direction: column;
      flex: 1;
      height: 72rpx;
      background: #f8f7fa;
      border-radius: 16rpx;
      justify-content: center;
      align-items: center;

      text {
        font-size: 24rpx;
        color: #1f1f1f;
      }

      text:nth-child(2) {
        font-size: 20rpx;
      }
    }
  }
}

.footer {
  width: 100%;
  box-sizing: border-box;
  gap: 24rpx;
  margin-top: 40rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #EEEEEE;
  ;

  .btn {
    flex: 1;
    height: 78rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 40rpx;
    font-weight: 700;
    font-size: 32rpx;
    color: #1F1F1F;

    &.primary {
      background: #FE58B7;
      color: #fff;
    }

    &.secondary {
      background-color: #eee;
    }
  }
}

:deep() {
  .uv-calendar__header {
    display: none !important;
  }

  .uv-calendar-item__weeks-box {
    border-radius: 80rpx;
  }

  .uv-calendar-item__weeks-box-item {
    height: 80rpx !important;
    width: 96rpx !important;
  }

  .uv-calendar-item__weeks-lunar-text {
    display: none !important;
  }

  .uv-calendar__weeks-day {
    border: none;
  }
}
</style>