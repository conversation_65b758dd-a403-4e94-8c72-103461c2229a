<template>
	<view class="topic-list">
		<view class="topic-item" v-for="(item, index) in dataList" :key="index">
			<text># {{item.name}}</text>
		</view>
	</view>
</template>

<script setup>
	const props = defineProps({
		dataList: {
			type: Array,
			required: true,
			default: [],
		},
	})
</script>

<style lang="scss">
	.topic-list {
		margin-top: 20rpx;
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}
	
	.topic-item {
		background: #F7F8FA;
		border-radius: 48rpx;
		font-size: 36rpx;
		color: #1F1F1F;
		line-height: 36rpx;
		padding: 24rpx 40rpx;
	}
</style>