<template>
	<view class="app-container">
		<z-paging ref="paging" v-model="datalist" @query="getData" :fixed="true" :hide-empty-view="true">
			<template #top>
				<Navbar leftText="次元" :type="4" />
			</template>

			<view :style="{ height: contentHeight }">
				<slide-video :list="datalist">
					<template #notification>
						<carson-notice theme="primary" :list="noticeList" show-more use-more-icon show-icon
							custom-icon="/static/icon/dimension/<EMAIL>" color="#fff" moreColor="#fff"
							background-color="rgba(0,0,0,0.6)" theKey="title" speed="slow" direction="row" />
					</template>
				</slide-video>
			</view>

			<template #bottom>
				<cc-myTabbar :tabBarShow="1" @change="handleTabChange"></cc-myTabbar>
			</template>
		</z-paging>
		<quick-login-dialog></quick-login-dialog>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue'
import SlideVideo from './components/slide-video/slide-video.vue'
import { getDimensionList } from '@/api/dimension'
import useContentHeight from '@/composables/useContentHeight.ts'
const { contentHeight } = useContentHeight({
	baseSpacing: '106rpx', // 可调整的值
});

const noticeList = computed(() => [{
	title: '相关服务 · cos.Lolita 人像拍摄 | 一对一 | 外拍跟拍',
}])
const datalist = ref([])
const paging = ref(null)

const getData = async (pageNum) => {
	try {
		const res = await getDimensionList({ page: pageNum, size: 10 })
		if (res.code === 20000) {
			paging.value.complete(res.data)
		}
	} catch (error) {
		console.log(error, 'getDimensionList error')
	}

}

const handleTabChange = (index) => {
	if (index === 1) {
		console.log('Tab切换到次元页面，重新加载数据')
		// 清空当前数据，触发数据变化监听
		datalist.value = []
		// 重新加载数据
		paging.value.reload()
	}
}

</script>
