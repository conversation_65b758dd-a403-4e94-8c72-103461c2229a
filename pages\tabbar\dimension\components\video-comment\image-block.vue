<template>
<view class="wrapper">
  <image class="preview-image" :src="url"></image>
  <image class="close-icon" src="/static/icon/dimension/icon_shanc.png" @click="() => emit('onDelete')"></image>
</view>
</template>

<script setup>
const props = defineProps({
  url: {
    type: String,
    default: '',
    required: true
  }
})

const emit = defineEmits(['onDelete'])
</script>

<style scoped lang='scss'>
.wrapper {
  width: 108rpx;
  height: 108rpx;
  border-radius: 16rpx;
  position: relative;

  .preview-image {
    width: 100%;
    height: 100%;
    border-radius: inherit;
  }

  .close-icon {
    position: absolute;
    width: 40rpx;
    height: 40rpx;
    top: -20rpx;
    right: -20rpx;
  }
}
</style>