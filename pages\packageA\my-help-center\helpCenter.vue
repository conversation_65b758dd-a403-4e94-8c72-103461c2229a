<!-- 帮助中心 -->
<template>
	<view class="container">
		<uni-nav-bar class="uni-nav-bar uni-nav-bar-set" :fixed="true" backgroundColor="rgba(255,255,255,1)"
			:border="false" :statusBar="true" leftWidth="268rpx" :rightWidth="rightWidth">
			<template #left>
				<view class="back" @tap="onBack">
					<image class="icon-back" src="/static/icon/<EMAIL>" />
					<text class="back-text">帮助中心</text>
				</view>
			</template>
			<template #right>
				<view class="service">
					<text class="back-text" @tap="gotoService">客服</text>
				</view>
			</template>
		</uni-nav-bar>
		<view class="main-wrapper">
			<view class="main">
				<publish-navigate-row v-for="item in dataList" :key="item.value" :label="item.text"
					@click="selectHandle(item)" />
			</view>
		</view>
	</view>

</template>
<script setup lang="ts">
import { ref, toRaw, onUnmounted, watchEffect, computed } from 'vue';

import { reportService, reportComiket } from "@/api/report";
import { isApp } from '@/common/utils';

const rightReact = isApp() ? { width: 0 } : uni.getMenuButtonBoundingClientRect();
const rightWidth = ref(rightReact.width + 50);
interface DataItem {
	text: string;
	value: string;
}
const dataList = ref<DataItem[]>([{ text: "问题1", value: "1" }, { text: "问题2", value: "2" }]);
const selectHandle = (item: DataItem) => {
	uni.navigateTo({
		url: `/pages/packageA/my-help-center/detail?text=${item.text}&value=${item.value}`
	});
}

function gotoService() {
	uni.navigateTo({
		url: `/pages/packageA/my-help-center/serviceCode`
	});
}

function onBack() {
	uni.navigateBack();
}
</script>

<style lang="scss" scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100%;
	box-sizing: border-box;

	padding-bottom: constant(safe-area-inset-bottom);
	;
	padding-bottom: env(safe-area-inset-bottom);
}

.main-wrapper {
	flex: 1 1 auto;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	box-sizing: border-box;
	padding: 32rpx 32rpx 24rpx;

	overflow-y: hidden;

	.main {
		display: flex;
		flex-direction: column;
		gap: 24rpx;
		padding: 32rpx 0;
	}
}

.uni-nav-bar {
	flex: 1;
}

.back {
	display: flex;
	align-items: center;
}

.service {
	width: 100%;
}

.icon-back {
	width: 44rpx;
	height: 46rpx;
}

.back-text {
	flex: 1;
	color: #1F1F1F;
	font-size: 36rpx;
	font-weight: 700;
	margin-left: 12rpx;
}
</style>
