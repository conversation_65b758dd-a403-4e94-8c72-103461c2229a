<template>
  <view class="container">
    <view class="title">{{ computedItem.name }}</view>
    <view class="time-info">
      <image class="icon" src="/static/img/exhibition-details/icon_sj.png"></image>
      <text>{{ computedItem.time }}</text>
    </view>
    <view class="time-picker-list">
      <view class="item" v-for="date in computedItem.dates" :key="date.label"
        :class="{ active: isSameDay(date.value) !== -1 }">{{ date.label }}</view>
    </view>
    <view class="divider" @click.stop="openCollapse">
      <image class="icon" :class="{ active: show }" src="../../static/service/<EMAIL>"></image>
    </view>
    <view class="collapse-item" :class="{ active: show }">
      <view class="collapse-item-container" v-for="comboItem in list" :key="comboItem.id">
        <view class="title">{{ comboItem.name }}</view>
        <view class="combo-description">商家保证金{{ comboItem.bond }}%</view>
        <view class="combo-description">
          <text>参考价：</text>
          <view class="price-wrapper">
            <text class="prefix">￥</text>
            <text class="price">{{ comboItem.price / 100 }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { getDaysBetweenDates } from '../../service-expo/hooks/useExpo';
import { getMineServiceDetailItems } from '@/api/mineService'

const props = defineProps(['item', 'serviceId', 'dateSlot'])
const show = ref(false)
const list = ref([])
const computedItem = computed(() => {
  return {
    ...props.item,
    dates: getDaysBetweenDates(props.item.start_datetime, props.item.end_datetime),
    time: formatTimeRange(props.item.start_datetime, props.item.end_datetime)
  }
})
console.log(computedItem.value, 'computedItem', props.dateSlot)

const isSameDay = (d) => {
  const d2 = new Date(d)
  return (props.dateSlot || []).findIndex(item => {
    const d1 = new Date(item)
    return (
      d1.getFullYear() === d2.getFullYear() &&
      d1.getMonth() === d2.getMonth() &&
      d1.getDate() === d2.getDate()
    )
  })
}

const openCollapse = () => {
  if (list.value.length === 0) {
    queryCombos()
  }
  show.value = !show.value
}

async function queryCombos() {
  try {
    const res = await getMineServiceDetailItems(props.serviceId)

    if (res.code === 20000) {
      list.value = res.data
    }
  } catch (error) {
    console.log(error, 'getMineServiceDetailItems service')
  }
}


function formatTimeRange(start, end) {
  function padZero(num) {
    return (num < 10 ? '0' : '') + num;
  }
  const startDate = new Date(start);
  const endDate = new Date(end);

  const year = endDate.getFullYear()
  const startMonth = startDate.getMonth() + 1;
  const startDay = startDate.getDate();
  const startHour = padZero(startDate.getHours());
  const startMinute = padZero(startDate.getMinutes());
  const endMonth = endDate.getMonth() + 1;
  const endDay = endDate.getDate();
  const endHour = padZero(endDate.getHours());
  const endMinute = padZero(endDate.getMinutes());

  return `${year}年${startMonth}月${startDay}日至${endMonth}月${endDay}日 ${startHour}:${startMinute}~${endHour}:${endMinute}`;
}
</script>

<style scoped lang='scss'>
.container {
  box-sizing: border-box;
  padding: 32rpx 32rpx 0;
  width: 100%;
  background: #F7F8FA;
  border-radius: 32rpx;
  overflow: hidden;


  .title {
    font-weight: 700;
    font-size: 36rpx;
  }

  .time-info {
    margin-top: 16rpx;
    display: flex;
    align-items: center;
    gap: 8rpx;

    .icon {
      width: 28rpx;
      height: 28rpx;
    }
  }

  .time-picker-list {
    display: flex;
    flex-wrap: wrap;
    gap: 24rpx;
    margin-top: 24rpx;

    .item {
      width: 100rpx;
      height: 40rpx;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      border: 2rpx solid #AAAAAA;
      font-size: 24rpx;
      color: #85878D;
      text-align: center;
      line-height: 40rpx;

      &.active {
        background: #FE58B7;
        border-color: #FE58B7;
        color: #fff;
      }
    }
  }

  .divider {
    width: 100%;
    border-top: 2rpx solid #eee;
    margin-top: 24rpx;
    display: flex;
    justify-content: center;
    padding-top: 4rpx;
    padding-bottom: 8rpx;

    .icon {
      width: 40rpx;
      height: 40rpx;
      transition: all 0.5s;

      &.active {
        transform: rotate(180deg);
      }
    }
  }

  .collapse-item {
    height: 0;
    padding-bottom: 0;
    transition: all 0.5s;

    &.active {
      height: fit-content;
    }

    .collapse-item-container {
      &:not(:first-child) {
        margin-top: 24rpx;
      }

      &:last-child {
        padding-bottom: 32rpx;
      }
    }

    .combo-description {
      font-size: 28rpx;
      color: #85878D;
      line-height: 40rpx;
      display: flex;
      align-items: center;
      margin-top: 12rpx;

      .price-wrapper {
        display: flex;
        align-items: flex-end;
        font-weight: 700;
        line-height: 34rpx;
        color: #FE58B7;

        .prefix {
          font-size: 24rpx;
        }

        .price {
          font-size: 36rpx;
        }
      }
    }
  }

}
</style>