<template>
  <view class="container">
    <uni-nav-bar title="城市选择测试" :border="true"></uni-nav-bar>
    
    <view class="content">
      <view class="result-section" v-if="selectedResult.province">
        <view class="result-title">已选择的地区：</view>
        <view class="result-text">
          {{ selectedResult.province?.name }} - 
          {{ selectedResult.city?.name }} - 
          {{ selectedResult.district?.name }}
        </view>
      </view>
      
      <city-select 
        :show-hot-city="true"
        :custom-nav-height="88"
        @confirm="onCityConfirm"
        @change="onCityChange">
      </city-select>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import CitySelect from '@/components/city-select/city-select.vue'

const selectedResult = ref({})

const onCityConfirm = (result) => {
  selectedResult.value = result
  console.log('确认选择:', result)
  
  uni.showToast({
    title: '选择成功',
    icon: 'success'
  })
}

const onCityChange = (result) => {
  console.log('选择变化:', result)
}
</script>

<style scoped lang="scss">
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.result-section {
  padding: 24rpx;
  background: #fff;
  margin-bottom: 16rpx;
  
  .result-title {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 16rpx;
  }
  
  .result-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
  }
}
</style>
