<template>
  <view class="comment-container">
    <view class="header">
      <view class="title">
        <text>评论</text>
        <text>({{ detailComments?.count || 0 }})</text>
      </view>
      <view class="action" @click="handleTo">
        <text>查看全部</text>
      </view>
    </view>
    <uni-list class="comment-list" :border="false">
      <uni-list-item class="dust-uni-list-item" v-for="comment in formatComments" :key="comment.id" :border="false">
        <template v-slot:body>
          <comment-block :item="comment" :show-footer="false" />

          <view v-if="comment.children && comment.children.results && comment.children.results.length > 0"
            class="children-comments">
            <view v-for="child in comment.children.results" :key="child.id" class="child-comment">
              <comment-block :item="child" :show-footer="false" />
            </view>
          </view>
        </template>
      </uni-list-item>
    </uni-list>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'

const props = defineProps({
  isAll: {
    type: Boolean,
    default: true
  }
})

const store = useStore()
const expoDetailState = computed(() => store.state.expoDetail)
const detailComments = computed(() => expoDetailState.value.expoComments)

const formatComments = computed(() => {
  const formatCommentItem = (item) => {
    const isJson = item.editor === 'json'
    const content = isJson && item.content ? JSON.parse(item.content) : item.content
    const comment = isJson ? content.text : content
    const image = isJson ? content.image : ''
    return {
      ...item,
      avatar: item.user_avatar,
      name: item.user_nickname,
      comment,
      time: formatTime(item.created_at * 1000),
      image
    }
  }

  const list = detailComments.value?.results?.map(item => {
    const formattedItem = formatCommentItem(item)

    // 格式化子评论
    if (item.children && item.children.results && item.children.results.length > 0) {
      formattedItem.children = {
        ...item.children,
        results: item.children.results.map(child => formatCommentItem(child))
      }
    }

    return formattedItem
  })

  return list.slice(0, 2)
})

function handleTo() {
  uni.navigateTo({
    url: '/pages/packageA/exhibition-comment/exhibition-comment'
  })
}

/**
 * 格式化时间戳
 * @param {number} timestamp - 时间戳
 * @returns {string} 格式化后的时间字符串
 * 今天：hh:mm
 * 今年非今天：MM月DD日 hh:mm
 * 非今年：YYYY年MM月DD日 hh:mm
 */
function formatTime(timestamp) {
  if (!timestamp) return '';

  const date = new Date(timestamp);
  const now = new Date();

  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  const nowYear = now.getFullYear();
  const nowMonth = now.getMonth() + 1;
  const nowDay = now.getDate();

  // 判断是否是今天
  if (year === nowYear && month === nowMonth && day === nowDay) {
    return `${hours}:${minutes}`;
  }
  // 判断是否是今年
  else if (year === nowYear) {
    return `${month}月${day}日 ${hours}:${minutes}`;
  }
  // 非今年
  else {
    return `${year}年${month}月${day}日 ${hours}:${minutes}`;
  }
}

</script>

<style lang="scss" scoped>
.comment-container {
  margin-top: 32rpx;
  margin-bottom: 200rpx;
  box-sizing: border-box;
  padding: 0 24rpx;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      display: flex;
      align-items: center;
      gap: 12rpx;
      font-size: 28rpx;
      color: #3D3D3D;
    }

    .action {
      font-size: 28rpx;
      color: #48DAEA;
    }

  }

  .comment-list {
    margin-top: 24rpx;

    .dust-uni-list-item {
      margin: 24rpx 0;
      padding-bottom: 24rpx;
      border-bottom: 1px solid #F4F4F4;
    }

    .children-comments {
      box-sizing: border-box;
      padding-left: 88rpx;
      margin-top: 24rpx;
      display: flex;
      flex-direction: column;
      gap: 24rpx;

      .load-more {
        font-size: 28rpx;
        color: #48DAEA;
      }
    }
  }
}

.popup-wrapper {
  width: 100%;
  min-height: 220rpx;
  overflow-y: auto;
  box-sizing: border-box;
  padding: 24rpx;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 24rpx;

  .status-item {
    width: 100%;
    text-align: center;
    padding-bottom: 24rpx;
    border-bottom: 2rpx solid #EEEEEE;
    font-size: 32rpx;
    color: #1F1F1F;

    &.red {
      color: $uni-color-error;
    }

    &:last-child {
      border: none;
    }
  }
}
</style>
