<template>
	<z-paging ref="paging" v-model="dataList" @query="queryList" :safe-area-inset-bottom="true"
		:use-safe-area-placeholder="true">
		<template #top>
			<Navbar :type="1" leftText="被邀请者"></Navbar>
		</template>
		<view v-for="(item, index) in dataList" class="itemBox" :key="index" @tap="toUser(item)">
			<view class="messageItem">
				<uv-avatar :src="prefixFilePath(item?.avatar)" size="96rpx"
					mode="aspectFill" />
				<view class="messageRight">
					<view class="messageItemContent">
						<view class="itemName">{{ item.nickname }}</view>
						<!-- <view class="itemTips">{{ item.bio }}</view> -->
					</view>
					<view v-if="!item.is_friend" class="messageBtn"
						@click.stop="onAddFriend(item.id)">申请</view>
				</view>
			</view>
		</view>
	</z-paging>
</template>

<script setup>
	import {
		ref
	} from 'vue';
	import {
		onLoad
	} from '@dcloudio/uni-app';
	import {
		useStore
	} from 'vuex';

	import {
		getInviteListApi
	} from '@/api/user.ts'
	import { addFriend } from '@/api/message'

	const store = useStore();
	const dataList = ref([]);
	const paging = ref(null);

	const queryList = async (pageNum, pageSize) => {
		const params = {
			page: pageNum,
			size: 10
		}
		const {
			data: {
				results
			},
			code
		} = await getInviteListApi(params)
		if (code === 20000) {
			paging.value.complete(results);
		} else {
			paging.value.complete([]);
		}
	};
	// 跳转用户主页
	function toUser(item) {
		uni.navigateTo({
			url: `/pages/home-pages/user-mine?id=${item.id}`
		});
	}
	async function onAddFriend(id) {
		await addFriend(id)
		uni.showToast({
			title: '已发送好友申请',
			icon: 'none'
		})
		paging.value?.reload();
	}
	// 当前环境域名和资源地址拼接
	const prefixFilePath = (url) => {
		if (!url) return '';
		return store.state.common.cloudFileUrl + url;
	}
</script>

<style lang="scss" scoped>
	.itemBox {
		padding: 0 32rpx;
	}

	.messageItem {
		display: flex;
		align-items: center;
	}

	.messageRight {
		display: flex;
		align-items: center;
		flex: 1;
		min-height: 96rpx;
		padding: 32rpx 0;
		border-bottom: 2rpx solid #eeeeee;
	}

	.messageItemContent {
		margin: 0 24rpx;
		flex: 1;
	}

	.itemName {
		font-size: 32rpx;
		color: #1F1F1F;
		line-height: 32rpx;
	}

	.itemTips {
		margin-top: 16rpx;
		font-size: 28rpx;
		color: #85878D;
		line-height: 28rpx;
	}

	.messageBtn {
		width: 144rpx;
		height: 72rpx;
		text-align: center;
		font-size: 32rpx;
		color: #FFFFFF;
		line-height: 72rpx;
		background: #FE58B7;
		border-radius: 48rpx;
	}
</style>