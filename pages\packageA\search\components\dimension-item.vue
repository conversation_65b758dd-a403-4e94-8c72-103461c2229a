<template>
	<uni-list class="dust-uni-list" :border="false">
		<uni-list-item class="dust-uni-list-item" v-for="(item, index) in dataList" :key="item.id" :border="false">
			<template v-slot:body>
				<view class="article-container" @tap="handleTo(item)">
					<view class="art-image-container">
						<dust-image-base :src="item.cover" :local="false" aspect-ratio="16:9"></dust-image-base>
					</view>
					<view class="create-container">
						<view class="creator-container">
							<view class="avatar-bar">
								<uv-avatar :src="prefixFilePath(item.user.avatar)" size="56rpx" mode="aspectFill" />
								<!-- <image v-if="item.user_gender === 1" class="gender-icon"
									src="/static/my/<EMAIL>" />
								<image v-if="item.user_gender === 2" class="gender-icon"
									src="/static/my/<EMAIL>" /> -->
							</view>
							<view class="creator-cnt">
								<text class="name">{{ item.name }}</text>
								<text class="date">{{ toDate(item.publish_at) }}</text>
							</view>
							<text :class="['user-interest', item.user.is_follow && 'user-interest-no']" @tap="onFollow(item.user)">
								{{ item.user.is_follow ? '已关注' : '关注' }}
							</text>
						</view>
					</view>
				</view>
			</template>
		</uni-list-item>
	</uni-list>
</template>

<script setup>
	import {
		ref
	} from 'vue'
	import {
		useStore
	} from 'vuex';
	import dayjs from 'dayjs'
	import {
		addFollow,
		delFollow
	} from '@/api/user'

	const store = useStore();
	const props = defineProps(['dataList'])

	// 当前环境域名和资源地址拼接
	const prefixFilePath = (url) => {
		if (!url) return '';
		return store.state.common.cloudFileUrl + url;
	}

	// 关注
	const onFollow = async (user) => {
		let is_follow = user.is_follow
		is_follow ? (await delFollow(user.id)) : (await addFollow(user.id))
		user.is_follow = !is_follow
	}
	
	function toDate(date) {
		return dayjs(date).format('YYYY.MM.DD')
	}
	
	function handleTo(item) {}
</script>

<style lang="scss">
	.anime-container {
		width: 100%;
		height: fit-content;
		background: #F2F4F7;
		border-radius: 32rpx;
		overflow: hidden;
		padding-bottom: 32rpx;
	}

	.anime-item-wrap {
		padding: 0 24rpx;
	}

	.user-interest {
		width: 116rpx;
		height: 56rpx;
		margin-left: 32rpx;
		text-align: center;
		font-size: 28rpx;
		line-height: 56rpx;
		color: #ffffff;
		border-radius: 28rpx;
		background: #FE58B7;
	}

	.user-interest-no {
		color: #3D3D3D;
		background-color: #fff;
	}

	.art-image-container {
		width: 100%;
		height: fit-content;
		position: relative;
	}

	.creator-container {
		display: flex;
		align-items: center;
		gap: 8rpx;

		.name {
			font-weight: 700;
			font-size: 32rpx;
			color: #3D3D3D;
		}
	}

	.creator-cnt {
		display: flex;
		flex-direction: column;

		.date {
			color: #85878D;
			font-size: 20rpx;
		}
	}

	.create-container {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 22rpx;
		color: #AAAAAA;
		font-size: 24rpx;
		padding: 0 28rpx;
	}

	.suit-list {
		margin-top: 20rpx;
		display: flex;
		flex-direction: column;
		gap: 10rpx;

		.suit-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 24rpx;
		}

		.price-text {
			color: #FE58B7;
			font-weight: 700;
			font-size: 24rpx;
		}

		.name-text {
			font-size: 28rpx;
			color: #3D3D3D;
			margin-left: 24rpx;
		}

		.sale-text {
			font-size: 24rpx;
			color: #FE58B7;
		}
	}
</style>