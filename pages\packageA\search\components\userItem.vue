<template>
    <view class="userItem" @tap="handleToUserMine">
        <uv-avatar :src="prefixFilePath(data?.friend_avatar || data?.user_avatar || data?.avatar)" size="96rpx" mode="aspectFill"/>
        <view class="userRight">
            <view class="userItemContent">
                <view class="itemName">{{ data.friend_nickname || data.user_nickname || data.nickname }}</view>
                <view class="itemTips">{{ data.bio }}</view>
            </view>
            <text :class="['user-interest', data?.is_follow && 'user-interest-no']" @tap.stop="onFollow">
            	{{ data?.is_follow ? '已关注' : '关注' }}
            </text>
        </view>
    </view>
</template>

<script setup>
import { ref } from 'vue'
import { useStore } from 'vuex';
import { addFollow, delFollow } from '@/api/user'

const store = useStore();
const props = defineProps(['data'])

// 当前环境域名和资源地址拼接
const prefixFilePath = (url) => {
	if (!url) return '';
	return store.state.common.cloudFileUrl + url;
}

// 关注
const onFollow = async () => {
	let is_follow = props.data?.is_follow
	is_follow ? (await delFollow(props.data.id)) : (await addFollow(props.data.id))
	props.data.is_follow = !is_follow
}
function handleToUserMine() {
	uni.navigateTo({
		url: '/pages/home-pages/user-mine?id=' + props.data.id,
	})
}
</script>

<style lang="scss" scoped>
.userItem {
    display: flex;
    align-items: center;
    .userRight {
        display: flex;
        align-items: center;
        flex: 1;
        min-height: 96rpx;
        padding: 32rpx 0;
        border-bottom: 2rpx solid #eeeeee;
    }
    .userItemContent {
        margin: 0 24rpx;
        flex: 1;
    }
    .itemName {
        font-size: 32rpx;
        color: #1F1F1F;
        line-height: 32rpx;
    }
    .itemTips {
        margin-top: 16rpx;
        font-size: 28rpx;
        color: #85878D;
        line-height: 28rpx;
    }
    .userBtn {
        width: 144rpx;
        height: 72rpx;
        text-align: center;
        font-size: 32rpx;
        color: #FFFFFF;
        line-height: 72rpx;
        background: #FE58B7;
        border-radius: 48rpx;
    }
}
.user-interest {
	width: 116rpx;
	height: 56rpx;
	margin-left: 32rpx;
	text-align: center;
	font-size: 28rpx;
	line-height: 56rpx;
	color: #ffffff;
	border-radius: 28rpx;
	background: #FE58B7;
}

.user-interest-no {
	color: #3D3D3D;
	background-color: #fff;
}
</style>