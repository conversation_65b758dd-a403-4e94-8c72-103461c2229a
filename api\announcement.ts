import { requestApi } from './request';

interface ArrayResponse<T = any> {
	count: number;
	results: Array<T>
}

export interface AnnouncementItem {
	id: number;
	title: string;
	cover: string;
	content: string;
	author_id: number;
	is_pinned: boolean;
	publish_at: number;
	start_at: number;
	end_at: number;
	status: number;
	editor: "json" | "other";
	created_at: number;
	updated_at: number;
	author: {
		id: string;
		username: string;
		nickname: string;
		avatar: string;
	}
}

/** 首页获取公告列表 */
export const getAnnouncementList = (params = {size: 4}) => {
	return requestApi<AnnouncementItem>({
		url: "/api/v1/foreground/system/announcement",
		data: params,
		method: "GET",
		loading: false,
		isToken: false,
	});
}

/** 首页-点击公告跳转：获取公告详情 */
export const getAnnouncementDetail = (id: number) => {
	return requestApi<AnnouncementItem>({
		url: `/api/v1/foreground/system/announcement/${id}`,
		method: "GET",
		loading: false,
		isToken: false,
	});
}
// 评论
export interface CommentsItem {
	count: number
	id: string;
	user_id: number;
	user_avatar: string;
	user_nickname: string;
	editor: "json" | "other"
	content: string;
	is_violation: boolean;
	is_deleted: boolean;
	parent_id: number;
	is_current_user: boolean;
	created_at: number;
	children: {results: Array<CommentsItem>} 
}
/** 首页-点击公告跳转：获取公告评论 */
interface CommentsOption {
	page: number;
	size: number;
	parent?: number;
}
export const getAnnouncementComments = (id: number, params: CommentsOption = {page: 1, size: 4}) => {
	return requestApi<ArrayResponse<CommentsItem>>({
		url: `/api/v1/foreground/system/announcement/${id}/comment`,
		method: "GET",
		data: params,
		loading: false,
	});
}

interface PublishComment {
	"content": string, 
	"parent": number,
	"editor"?: string;
}
export const publishAnnouncementComment = (id: number, params: PublishComment) => {
	return requestApi<Response>({
		url: `/api/v1/foreground/system/announcement/${id}/comment`,
		data: {"editor": "json", ...params},
		loading: false,
	});
}