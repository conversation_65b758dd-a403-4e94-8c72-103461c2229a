# 城市选择组件 (CitySelect)

基于 uView IndexList 组件封装的三级联动城市选择器，支持省份、城市、区县的选择。

## 功能特性

- ✅ 支持三种选择模式：仅省份、省市、省市区
- ✅ 基于 uView IndexList 组件，支持字母索引快速定位
- ✅ 数据从 Vuex store 获取省份，通过 API 动态获取城市和区县
- ✅ 支持面包屑导航，可快速切换选择层级
- ✅ 支持默认值设置
- ✅ 响应式设计，适配不同屏幕尺寸
- ✅ 加载状态提示

## 安装依赖

确保项目中已安装以下依赖：

```bash
npm install js-pinyin
```

## 使用方法

### 基础用法

```vue
<template>
  <city-select 
    mode="province-city-district"
    @change="handleLocationChange"
  />
</template>

<script setup>
import CitySelect from '@/components/city-select/city-select.vue'

function handleLocationChange(location) {
  console.log('选择的位置:', location)
  // location 结构：
  // {
  //   province: { id, name, code, ... },
  //   city: { id, name, code, ... },
  //   district: { id, name, code, ... }
  // }
}
</script>
```

### 设置默认值

```vue
<template>
  <city-select 
    mode="province-city"
    :value="defaultLocation"
    @change="handleLocationChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import CitySelect from '@/components/city-select/city-select.vue'

const defaultLocation = ref({
  province: { id: '1', name: '北京市' },
  city: { id: '1', name: '北京市' },
  district: {}
})

function handleLocationChange(location) {
  console.log('选择的位置:', location)
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 | 可选值 |
|------|------|------|--------|--------|
| mode | 选择模式 | String | 'province-city-district' | 'province' / 'province-city' / 'province-city-district' |
| customNavHeight | 自定义导航栏高度 | String/Number | 0 | - |
| value | 默认选中的值 | Object | {} | - |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 选择变化时触发 | location: { province, city, district } |
| confirm | 选择确认时触发 | location: { province, city, district } |

## 选择模式说明

### province
仅选择省份，选择省份后立即触发 change 事件。

### province-city
选择省份和城市，选择城市后触发 change 事件。

### province-city-district
选择省份、城市和区县，选择区县后触发 change 事件。

## 数据结构

### 省份数据结构
```javascript
{
  id: "1",
  code: "11",
  full_code: "110000",
  name: "北京市",
  full_name: "北京市",
  abbreviation: "京",
  abbr_pinyin: "BJ",
  abbr: "京"
}
```

### 城市数据结构
```javascript
{
  id: "1",
  code: "01",
  full_code: "110100",
  name: "北京市",
  full_name: "北京市"
}
```

### 区县数据结构
```javascript
{
  id: "1",
  code: "01",
  full_code: "110101",
  name: "东城区",
  full_name: "东城区"
}
```

## 注意事项

1. 确保 Vuex store 中的 location 模块已正确配置
2. 确保 API 接口 `getSystemCity` 和 `getSystemDistrict` 可正常调用
3. 组件依赖 `js-pinyin` 库进行拼音转换，用于字母索引
4. 如果使用自定义导航栏，需要设置 `customNavHeight` 属性

## 样式自定义

组件使用 SCSS 编写样式，可以通过修改 CSS 变量或覆盖样式类来自定义外观：

```scss
// 自定义面包屑样式
.breadcrumb {
  background-color: #your-color;
}

// 自定义列表项样式
.list-cell {
  font-size: 30rpx;
  padding: 30rpx 40rpx;
}
```

## 示例

查看 `demo.vue` 文件获取完整的使用示例。
