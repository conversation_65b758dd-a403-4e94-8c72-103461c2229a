{
	"easycom": {
		"autoscan": true
	},
	"pages": [
		{
			"path": "pages/tabbar/home/<USER>",
			"style": {
				"navigationBarTitleText": "首页"
			}
		},
		{
			"path": "pages/tabbar/dimension/dimension",
			"style": {
				"navigationBarTitleText": "次元"
			}
		},
		{
			"path": "pages/tabbar/publish/publish",
			"style": {
				"navigationBarTitleText": "发布"
			}
		},
		{
			"path": "pages/tabbar/notification/notification",
			"style": {
				"navigationBarTitleText": "消息"
			}
		},
		{
			"path": "pages/tabbar/mine/mine",
			"style": {
				"navigationBarTitleText": "我的"
			}
		},
		{
			"path": "pages/tabbar/notification/contacts",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/tabbar/notification/systemNotification",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/tabbar/notification/ordersNotification",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/tabbar/notification/newFriend",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/tabbar/notification/ordersNotificationDetails",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/tabbar/notification/search",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/tabbar/notification/createOrders",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/tabbar/notification/addFriend",
			"style": {
				"navigationBarTitleText": ""
			}
		}
	],
	"subPackages": [
		{
			"root": "pages/packageA",
			"pages": [
				{
					"path": "exhibition-details/exhibition-details",
					"style": {
						"navigationBarTitleText": "展会详情"
					}
				},
				{
					"path": "exhibition-comment/exhibition-comment",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "exhibition-joiner/exhibition-joiner",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "exhibition-join-date/exhibition-join-date",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "photography/photography",
					"style": {
						"navigationBarTitleText": "摄影"
					}
				},
				{
					"path": "photographyDetails/photographyDetails",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "orderDetails/orderDetails",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "order/order",
					"style": {
						"navigationBarTitleText": "我的订单"
					}
				},
				{
					"path": "wallet/wallet",
					"style": {
						"navigationBarTitleText": "我的钱包"
					}
				},
				{
					"path": "wallet-top-up/wallet-top-up",
					"style": {
						"navigationBarTitleText": "充值"
					}
				},
				{
					"path": "wallet-withdrawal/wallet-withdrawal",
					"style": {
						"navigationBarTitleText": "提现"
					}
				},
				{
					"path": "wallet-bankcard/wallet-bankcard",
					"style": {
						"navigationBarTitleText": "银行卡"
					}
				},
				{
					"path": "wallet-bankcard/bind-card",
					"style": {
						"navigationBarTitleText": "绑定银行卡"
					}
				},
				{
					"path": "service/service",
					"style": {
						"navigationBarTitleText": "我的服务"
					}
				},
				{
					"path": "service-comment/service-comment",
					"style": {
						"navigationBarTitleText": "服务评价"
					}
				},
				{
					"path": "service-create/basic",
					"style": {
						"navigationBarTitleText": "服务项"
					}
				},
				{
					"path": "service-create/type",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "service-create/detail-expo",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "service-create/detail-daliy",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "service-create/combo-list",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "service-create-description/basic",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "service-portfolio/service-portfolio",
					"style": {
						"navigationBarTitleText": "作品集"
					}
				},
				{
					"path": "service-expo/service-expo",
					"style": {
						"navigationBarTitleText": "漫展"
					}
				},
				{
					"path": "service-expo/add-combo",
					"style": {
						"navigationBarTitleText": "添加套餐"
					}
				},
				{
					"path": "service-create-edit/service-create-edit",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "orderComment/orderComment",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "login-pwd/login-pwd",
					"style": {
						"navigationBarTitleText": "密码登录"
					}
				},
				{
					"path": "verify-idcard/verify-idcard",
					"style": {
						"navigationBarTitleText": "实名认证"
					}
				},
				{
					"path": "my-settings/my-settings",
					"style": {
						"navigationBarTitleText": "设置"
					}
				},
				{
					"path": "agreement/agreement",
					"style": {
						"navigationBarTitleText": "儿童/青少年个人信息保护规则"
					}
				},
				{
					"path": "agreement/userAgreement",
					"style": {
						"navigationBarTitleText": "用户协议"
					}
				},
				{
					"path": "agreement/privacyAgreement",
					"style": {
						"navigationBarTitleText": "隐私协议"
					}
				},
				{
					"path": "agreement/commissionAgreement",
					"style": {
						"navigationBarTitleText": "返佣规则"
					}
				},
				{
					"path": "my-help-center/helpCenter",
					"style": {
						"navigationBarTitleText": "帮助与客服"
					}
				},
				{
					"path": "my-help-center/detail",
					"style": {
						"navigationBarTitleText": "帮助详情"
					}
				},
				{
					"path": "my-help-center/serviceCode",
					"style": {
						"navigationBarTitleText": "帮助与客服"
					}
				},
				{
					"path": "change-phone/change-phone",
					"style": {
						"navigationBarTitleText": "修改手机号"
					}
				},
				{
					"path": "my-info/my-info",
					"style": {
						"navigationBarTitleText": "个人信息"
					}
				},
				{
					"path": "my-info/edit-bio",
					"style": {
						"navigationBarTitleText": "简介信息"
					}
				},
				{
					"path": "blacklist/blacklist",
					"style": {
						"navigationBarTitleText": "黑名单"
					}
				},
				{
					"path": "my-questions/list",
					"style": {
						"navigationBarTitleText": "常见问题"
					}
				},
				{
					"path": "my-questions/detail",
					"style": {
						"navigationBarTitleText": "常见问题"
					}
				},
				{
					"path": "report/report",
					"style": {
						"navigationBarTitleText": "举报"
					}
				},
				{
					"path": "report/first-type",
					"style": {
						"navigationBarTitleText": "举报"
					}
				},
				{
					"path": "report/second-type",
					"style": {
						"navigationBarTitleText": "举报"
					}
				},
				{
					"path": "login-pwd/invite-user-info",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "search/search",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "exhibition-details/exhibition-map",
					"style": {
						"navigationBarTitleText": "漫展地址"
					}
				},
				{
					"path": "other-home-service/other-home-service",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "other-home-service/expo-service-detail",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "other-home-service/daily-service-detail",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path" : "group-booking/group-booking",
					"style" : 
					{
						"navigationBarTitleText" : "约团"
					}
				},
				{
					"path" : "group-booking/group-booking-detail",
					"style" : 
					{
						"navigationBarTitleText" : "约团详情"
					}
				},
				{
					"path" : "invite-list/invite-list",
					"style" : 
					{
						"navigationBarTitleText" : "被邀请者"
					}
				},
				{
					"path": "friend-chat/friend-chat",
					"style": {
						"navigationBarTitleText": ""
					}
				}
			]
		},
		{
			"root": "pages/packages-publish", // 关于发布的子包，如活动、次元、需求、服务
			"pages": [
				{
					"path": "activity/publish" // 发布活动
				},
				{
					"path": "activity/service" // 选择服务 
				},
				{
					"path": "activity/types"   // 活动类型
				},
				{
					"path": "activity/chooseAddress" // 地址选择
				},
				{
					"path": "dimension/publish" // 发布次元
				}
				
			]
		},
		{
			"root": "pages/home-pages",
			"pages": [
				{
					"path": "user-mine"
				}
			]
		},
		{
			"root": "pages/announcement", // 公告-分包
			"pages": [
				{
					"path": "detail" // 公告-详情
				},
				{
					"path": "comment-list" // 公告-评论列表
				}
			]
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8",
		"navigationStyle": "custom"
	},
	"tabBar": {
		"borderStyle": "black",
		"backgroundColor": "#fff",
		"color": "#000",
		"selectedColor": "#000",
		"list": [
			{
				"pagePath": "pages/tabbar/home/<USER>",
				"iconPath": "static/tabbar/icon_sy.png",
				"selectedIconPath": "static/tabbar/icon_sy2.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/tabbar/dimension/dimension",
				"iconPath": "static/tabbar/icon_cy.png",
				"selectedIconPath": "static/tabbar/icon_cy2.png",
				"text": "次元"
			},
			{
				"pagePath": "pages/tabbar/notification/notification",
				"iconPath": "static/tabbar/icon_xx.png",
				"selectedIconPath": "static/tabbar/icon_xx2.png",
				"text": "消息"
			},
			{
				"pagePath": "pages/tabbar/mine/mine",
				"iconPath": "static/tabbar/icon_wd.png",
				"selectedIconPath": "static/tabbar/icon_wd2.png",
				"text": "我的"
			}
		]
	},
	"condition": {
		"current": 0,
		"list": [
			{
				"name": "",
				"path": "",
				"query": ""
			}
		]
	}
}