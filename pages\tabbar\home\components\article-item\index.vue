<template>
  <view class="article-container">
    <view class="art-image-container">
      <dust-image-base :src="article.cover" :local="false" aspect-ratio="16:9"></dust-image-base>
      <view class="art-viewer" v-if="showViewer">
        <image :class="{ 'viewer-icon': true, 'viewer-icon-small': type !== 0 }"
          :src="type === 0 ? '/static/icon/dust_icon_view.svg' : '/static/icon/<EMAIL>'"></image>
        <text>{{ showIndex ? article.index : formatCountToString(article.views_count) }}</text>
      </view>
    </view>
    <slot>
      <view class="art-title">{{ article.name }}</view>
      <view class="create-container">
        <view class="creator-container" @tap.stop="handleToUserMine(article)">
          <image class="avatar" :src="prefixFilePath(article.user_avatar)"></image>
          <text class="name">{{ article.user_nickname }}</text>
        </view>
        <text class="create-time">{{ formatTimeText }}</text>
      </view>
    </slot>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import {
  useStore
} from 'vuex';
import {
  formatCountToString
} from '@/common/utils'

const store = useStore();

const props = defineProps({
  article: {
    type: Object,
    required: true
  },
  type: {
    type: Number,
    default: 0
  },
  showViewer: {
    type: Boolean,
    default: true
  },
  showIndex: {
    type: Boolean,
    default: false
  }
})

const cloudFileUrl = computed(() => store.state.common.cloudFileUrl)

function prefixFilePath(url) {
  if (!url) return ''
  return cloudFileUrl.value + url
}

const formatTimeText = computed(() => {
  return timeAgo(props.article.created_at)
})

function timeAgo(givenTime) {
  const now = new Date();
  const timeDiff = Math.floor((now - new Date(givenTime)) / 1000); // 计算时间差（秒）

  if (timeDiff < 60) {
    return `${timeDiff}秒前`;
  } else if (timeDiff < 3600) {
    const minutes = Math.floor(timeDiff / 60);
    return `${minutes}分钟前`;
  } else if (timeDiff < 86400) {
    const hours = Math.floor(timeDiff / 3600);
    return `${hours}小时前`;
  } else if (timeDiff < 2592000) { // 30天
    const days = Math.floor(timeDiff / 86400);
    return `${days}天前`;
  } else if (timeDiff < 31536000) { // 12个月
    const months = Math.floor(timeDiff / 2592000);
    return `${months}个月前`;
  } else {
    const years = Math.floor(timeDiff / 31536000);
    return `${years}年前`;
  }
}
function handleToUserMine(item) {
	uni.navigateTo({
		url: '/pages/home-pages/user-mine?id=' + item.user_id,
	})
}
</script>

<style scoped lang='scss'>
.article-container {
  width: 100%;
  height: fit-content;
  background: #F2F4F7;
  border-radius: 32rpx;
  overflow: hidden;
  padding-bottom: 32rpx;

  .art-image-container {
    width: 100%;
    height: fit-content;
    position: relative;

    .art-viewer {
      width: fit-content;
      height: 32rpx;
      padding: 0 8rpx 0 10rpx;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 20rpx 0rpx 0rpx 0rpx;
      display: flex;
      align-items: center;
      gap: 14rpx;
      position: absolute;
      bottom: 0;
      right: 0;

      text {
        color: #fff;
        font-size: 20rpx;
      }

      .viewer-icon {
        width: 24rpx;
        height: 24rpx;

        &.viewer-icon-small {
          width: 20rpx;
          height: 20rpx;
        }
      }
    }
  }

  .art-title {
    padding: 8rpx 28rpx 0;
    font-size: 28rpx;
    color: #1F1F1F;
    line-height: 40rpx;
    font-weight: 500;
  }

  .create-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 22rpx;
    color: #AAAAAA;
    font-size: 24rpx;
    padding: 0 28rpx;

    .creator-container {
      display: flex;
      align-items: center;
      gap: 8rpx;

      .avatar {
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
      }
    }
  }
}
</style>
