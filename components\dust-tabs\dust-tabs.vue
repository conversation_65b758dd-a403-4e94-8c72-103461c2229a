<template>
  <view class="dust-tabs-container" :style="customStyle">
    <view class="dust-tabs-item"
      :class="{ active: modelValue === index, 'line-gradient-border': lineGradientBorder || lineGradientBorder === 'true', 'font-weight-nomal': tabType === 'secondary' }"
      v-for="(tab, index) in list" :key="tab.name + index" @click="selectTab(tab.value)">
      {{ tab.name }}
      <image v-if="showActiveStar || showActiveStar === 'true'" class="active-star"
        src="/static/icon/dust_icon_tabs_active_star.svg"></image>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  list: {
    type: Array,
    required: true,
  },
  modelValue: {
    type: Number,
    required: true,
  },
  customStyle: {
    type: Object,
    required: false,
    default: {}
  },
  tabType: {
    type: String,
    required: true,
    validator: (value) => {
      return ['primary', 'secondary'].includes(value);
    }
  },
  lineGradientBorder: {
    type: [Boolean, String],
    default: false
  },
  showActiveStar: {
    type: [Boolean, String],
    default: false
  }
})
const emit = defineEmits(['update:modelValue']);

const selectTab = (value) => {
  emit('update:modelValue', value);
};
</script>

<style scoped lang='scss'>
.dust-tabs-container {
  width: 100%;
  height: 32rpx;
  display: flex;
  align-items: center;
  gap: 42px;

  .dust-tabs-item {
    /* flex: 1; */
    position: relative;
    font-weight: bold;
	font-size: 32rpx;

    &.font-weight-nomal {
      font-weight: normal;
    }

    .active-star {
      position: absolute;
      top: 0;
      right: -20rpx;
      width: 20rpx;
      height: 20rpx;
      display: none;
    }

    &.active {
      position: relative;
      font-weight: bold;

      .active-star {
        display: block;
      }

      &::after {
        content: "";
        position: absolute;
        bottom: -2px;
        z-index: -1;
        display: block;
        width: 100%;
        height: 8px;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        background-color: #48DAEA;
      }
    }

    &.line-gradient-border {
      &::after {
		width: 80%;
        background-image: linear-gradient(to right, #48daea, #fff);
      }
    }
  }
}
</style>
