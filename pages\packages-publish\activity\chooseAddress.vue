<template>
	<view class="container">
		<Navbar :type="1" leftText="所在位置"></Navbar>
		<view class="main">
			<view class="search">
				<publish-search-input v-model="search" />
			</view>
			<view class="body">
				<view class="map-container" @longpress="onlongpress" @touchend="ontouchend">
					<map class="map" id="map" ref="map" :longitude="location.longitude" :latitude="location.latitude"
						:show-location="true" enable-building @regionchange="onregionchange"
					/>
					<img class="location" :class="{'dragging': isDragging}"
						src="@/pages/packages-publish/static/<EMAIL>" />
					<uni-icons class="reset" size="32" type="navigate" @tap.stop="resetHandle"></uni-icons>

				</view>
				<view class="scrollview" scroll-y="true">
					<view class="item" v-for="(item, index) in regionDataList" @click="addressItemClick(item)" :key="index">
						<view class="left">
							<image src="@/pages/packages-publish/static/<EMAIL>" mode="aspectFit" />
						</view>
						<view class="middle">
							<text class="title">{{item.title}}</text>
							<text class="detail">{{item.address}}</text>
						</view>
						<view class="right">
							<image v-if="item.selected" src="@/pages/packages-publish/static/<EMAIL>"
								mode="aspectFit" />
						</view>
					</view>
				</view>
				<scroll-view class="search-srcoll" scroll-y="true" v-if="search">
					<view class="item" v-for="(item, index) in searchDataList" @click="searchItemClick(item)" :key="index">
						<!-- 	<view class="left">
							<image src="@/pages/packages-publish/static/<EMAIL>" mode="aspectFit" />
						</view> -->
						<view class="middle">
							<text class="title">{{item.title}}</text>
							<text class="detail">{{item.address}}</text>
						</view>
						<view class="right">
							<image v-if="item.selected" src="@/pages/packages-publish/static/<EMAIL>"
								mode="aspectFit" />
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, onMounted, computed, watchEffect, getCurrentInstance, toRaw } from "vue";
	import { useStore } from "vuex";
	import { onReady, onLoad } from "@dcloudio/uni-app"
	import Config from '@/common/config';
	import {
		getSystemPcd
	} from "@/api/common";
	
	import { PUBLISH_ACTIVITY_ADDRESS_SELECT } from "./const";
	import type { AddressItem, SearchResponse } from "./const";

	interface Location {
		longitude : number,
		latitude : number,
	}
	const store = useStore();
	const mapContext = ref<UniApp.MapContext>(null);
	// 用户保存的位置， 没有的话需要重新获取
	const location = ref<Location>(null);
	// 监听本地经纬度变化 必须监听，因为刚进入是时可能正在获取地址
	watchEffect(() => {
		let newLocation = store.state.location.geoLocation;
		location.value = newLocation || {};
		
		if (!mapContext.value) return;
		const { latitude, longitude } = location.value;
		if (!latitude && !longitude) return
		// 真机测试进入时有经纬度 但是地图不显示在中间区域处理
		mapContext.value.moveToLocation(location.value);
	});

	onLoad((query) => {
		console.log("传递过来的经纬度：", query);
		const {longitude, latitude} = query;
		if (!longitude || !latitude) return;
		location.value = {longitude, latitude};
	});
	
	onReady(() => {
		mapContext.value = uni.createMapContext("map", getCurrentInstance()!.proxy!);
		const { latitude, longitude } = location.value;
		if (!latitude && !longitude) return store.dispatch("location/getGeoLocation");
		console.log("根据经纬度搜索:", longitude, latitude);
		searchLocationPoi(location.value);
		mapContext.value.moveToLocation(location.value);
	});

	// 当经纬度变化后，地图会自动变更区域 不需要手动请求周边地址
	let isDragging = ref(false);
	const onlongpress = () => {
		isDragging.value = true;
	};
	const ontouchend = () => {
		isDragging.value = false;
	}

	// 点击rest时会多次触发regionchange
	let isReseting = false;
	let regionChangeTimer : any = 0;
	const onregionchange = (e : { detail : { type : "bgein" | "end", centerLocation : Location } }) => {
		console.log('视野发生变化时触发', e.detail)
		if (isDragging.value) return;
		const { type, centerLocation } = e.detail;
		if (type !== "end") return;
		// 点击复位后，regionchange会触发两次，且时延迟触发 所以需要debouce;
		console.log("搜索经纬度周边：", centerLocation);
		if (!mapContext.value) return;

		clearTimeout(regionChangeTimer);
		regionChangeTimer = setTimeout(() => {
			if (isReseting) return isReseting = false;
			searchLocationPoi(centerLocation);
		}, 100);
	};

	const regionDataList = ref<AddressItem[]>([]);
	const searchLocationPoi = function (location : Location) {
		uni.request({
			url: Config.mapSearchUrl + '/search',
			data: {
				key: Config.tencentKey,
				// keyword: "会场",
				boundary: `nearby(${location.latitude},${location.longitude},1000,1)`,
			},
			success: function (res) {
				const data : SearchResponse[] = (res.data as any).data || [];
				const newData : AddressItem[] = data.map(item => {
					const distance = item._distance / 1000;
					const displayDistance = distance / 1000 > 0 ? `${distance.toFixed(2)}km` : `${item._distance}m`;
					return {
						longitude: item.location.lng,
						latitude: item.location.lat,
						title: item.title,
						address: item.address,
						selected: false,
						adcode: item.ad_info.adcode,
						distance: displayDistance
					}
				});
				regionDataList.value = newData;
			}
		});
	};

	// 搜索显示列表
	type SearchDataItem = Omit<AddressItem, "adcode">
	const searchDataList = ref<SearchDataItem[]>([]);
	const getSearchSuggestion = (keyword : string) => {
		uni.request({
			url: Config.mapSearchUrl + '/suggestion',
			data: {
				key: Config.tencentKey,
				keyword: keyword,
				policy: 1,
			},
			success: function (res) {
				const data : SearchResponse[] = (res.data as any).data || [];
				const newData : SearchDataItem[] = data.map(item => {
					const distance = item._distance / 1000;
					const displayDistance = distance / 1000 > 0 ? `${distance.toFixed(2)}km` : `${item._distance}m`;
					return {
						longitude: item.location.lng,
						latitude: item.location.lat,
						title: item.title,
						address: item.address,
						selected: false,
						distance: displayDistance
					}
				});
				searchDataList.value = newData;
			}
		});
	}

	const resetHandle = function () {
		if (!mapContext.value) return;
		if (isReseting) return;
		isReseting = true;
		mapContext.value.moveToLocation({});
	}

	const addressItemClick = function (item : AddressItem) {
		item.selected = !item.selected;
		if (!item.selected) return;
		console.log("选择的地址:", toRaw(item));
		uni.showLoading({
			title: "地址转换中..."
		})
		const adCode = item.adcode + "";
		getSystemPcd({province : adCode.slice(0, 2), city: adCode.slice(2, 4), district: adCode.slice(4, 6)}).then(res => {
			const data: any = res.data || {};
			const adderssInfo = {
				province: data.province ? data.province.id : 0,
				city: data.city ? data.city.id : 0,
				district: data.district ? data.district.id : 0,
			}
			uni.hideLoading();
			uni.$emit(PUBLISH_ACTIVITY_ADDRESS_SELECT, Object.assign(toRaw(item), adderssInfo));
			// 为了显示选择状态延时
			uni.navigateBack();
		}).catch(err => {
			// 选中复原
			item.selected = !item.selected;
			uni.hideLoading();
			uni.showToast({
				icon: "error",
				title: "地址转换失败"
			});
		})
		
	};

	// 搜索相关
	const search = ref("");
	let searchDeboudce : any = 0;
	watchEffect(() => {
		const keyword = search.value;
		if (!keyword) return;
		clearTimeout(searchDeboudce);
		setTimeout(() => getSearchSuggestion(keyword), 200);
	});

	const searchItemClick = (item : SearchDataItem) => {
		console.log("------2222", item);
		item.selected = !item.selected;
		if (!item.selected) return;
		search.value = "";
		const loactaion: Location = { latitude: item.latitude, longitude: item.longitude };
		console.log("定位地址：", loactaion)
		mapContext.value.moveToLocation({
			...loactaion,
			success: () => {
				console.log("移动成功");
			},
			complete: () => {
				console.log("移动完成");
			}
		});
		// searchLocationPoi(loactaion);
	};
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		height: 100%;
		box-sizing: border-box;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		overflow: hidden;
	}

	.main {
		flex: 1 1 auto;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		padding: 0 32rpx;
		box-sizing: border-box;
		overflow: hidden;
	}

	.search {
		padding: 32rpx 0;
	}

	.body {
		position: relative;
		display: flex;
		flex-direction: column;
		flex: 1;
		gap: 16rpx;
		box-sizing: border-box;
		overflow: hidden;
	}

	.map-container {
		position: relative;

		.location {
			position: absolute;
			width: 32px;
			height: 32px;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -100%);
			transition: all 0.3s ease-out,
		}

		.dragging {
			transform: translate(-50%, -115%);
		}

		.reset {
			position: absolute;
			background-color: white;
			border-radius: 50%;
			right: 10px;
			bottom: 10px;
		}
	}

	.map {
		width: 100%;
		height: 352rpx;
		border-radius: 16rpx;
		overflow: hidden;
	}

	.scrollview {
		flex: 1 1 auto;
		box-sizing: border-box;
		overflow: auto;
	}

	.search-srcoll {
		position: absolute;
		top: 0;
		left: 0;
		height: 100%;
		width: 100%;
		background-color: white;
	}

	.item {
		display: flex;
		justify-content: space-between;
		flex-wrap: nowrap;
		gap: 12rpx;
		width: 100%;
		padding: 24rpx 0;
		border-bottom: 1px solid rgba(238, 238, 238);
		overflow: hidden;
		.middle {
			display: flex;
			flex: 1 1 auto;
			flex-direction: column;
			gap: 8rpx;
			overflow: hidden;

			.title {
				height: 46rpx;
				color: rgba(31, 31, 31, 1);
				font-size: 32rpx;
				font-weight: 600;
			}

			.detail {
				font-size: 24rpx;
				color: rgba(31, 31, 31, 1);
	
				white-space: nowrap;    /* 不换行 */
				overflow: hidden;       /* 超出容器的部分隐藏 */
				text-overflow: ellipsis;
			}
		}

		.left,
		.right {
			width: 40rpx;
		}

		image {
			height: 40rpx;
			width: 40rpx;
		}
	}
</style>