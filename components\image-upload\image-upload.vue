<template>
	<view class="container-upload" :style="props.customStyle">
		<view class="ratio-wrapper" v-for="(item, index) in resourceData" :key="item.url + index">
			<view class="ratio-inner">
				<view class="preview-wrapper" @tap="previewHandle(item)">
					<template v-if="item.type == 'video'">
						<image class="image" :src="item.thumb" :mode="props.mode"></image>
						<view class="icon-container">
							<view class="icon-play" />
						</view>
					</template>
					<template v-else>
						<image class="image" :src="item.url" :mode="props.mode"></image>
					</template>
					<!-- 删除按钮 -->
					<image class="delete" src="@/pages/packages-publish/static/<EMAIL>"
						@tap.stop="deleteItem(index, item)" />
				</view>
			</view>
		</view>

		<view class="ratio-wrapper" v-if="!isOverLimit">
			<view class="ratio-inner">
				<view class="preview-wrapper upload-btn" @tap="uploadHandle">
					<view class="icon-uplaod-container">
						<view class="icon-uplaod" />
						<view class="icon-uplaod" />
					</view>
					<text>上传图片</text>
				</view>
			</view>
		</view>
		<video-preview ref="previewVideo" />
	</view>
</template>

<script lang="ts" setup>
import { ref, PropType, computed, watch } from "vue";
import { uploadFile } from '@/api/request';
import { chooseMedia, Resource } from "./utils";

// 单个文件类型
interface FileOption {
	url: string;
	type: "image" | "video"// 默认为image
	thumb?: string;
};

interface Props {
	fileList: FileOption[];
	customStyle?: string;  // 自定义style
	accept?: "all" | "image" | "video"; // 支持上传类型 默认为iamge
	maxCount?: number; // 上传最大值，默认为9
	mode?: "aspectFill" | "aspectFit"; // 预览显示样式
	multiple?: boolean; // 是否多选 默认为false
};
const props = withDefaults(defineProps<Props>(), {
	fileList: () => [],
	accept: "all",
	maxCount: 9,
	mode: "aspectFill",
	multiple: false
});

interface Emits {
	(e: "uploadSucess", value: FileOption[]): void;
	(e: "delete", value: FileOption & { index: number }): void;
}
const emits = defineEmits<Emits>();
// 资源列表
const resourceData = ref<Array<FileOption & { thumb?: string }>>([]);
const isOverLimit = computed(() => resourceData.value.length >= props.maxCount);

// 用来保存video url对应的thumb地址，
const videThumbInfo = {};

watch(() => props.fileList, (value: FileOption[]) => {
	const newData: Array<FileOption> = [];
	const videoTyps = ["mp4", "avi", "kov"]
	value && value.forEach((item, index) => {
		if (index >= props.maxCount) return;
		const formatStr = item.url.split(".").pop();
		const type = item.type ? item.type : videoTyps.includes(formatStr) ? "video" : "image";
		newData.push({ url: item.url, type: type, thumb: videThumbInfo[item.url] });
	});
	resourceData.value = newData;
}, {
	deep: true,
	immediate: true
});

// 删除图片
const deleteItem = function (index: number, item: FileOption) {
	resourceData.value.splice(index, 1);
	delete videThumbInfo[item.url];
	emits("delete", { ...item, index: index });
}

let timer: any = 0
const uploadHandle = function () {
	timer && clearTimeout(timer);
	timer = setTimeout(() => {
		chooseMedia(props.multiple, props.maxCount, props.accept).then(uploadRequest).catch(console.log);
	}, 100)
};

const uploadRequest = function (data: Resource[]) {
	if (!data || !data.length) return;
	// 超过个数不能上传
	const canUploadCount = props.maxCount - resourceData.value.length;
	if (canUploadCount <= 0) return;
	data = data.slice(0, canUploadCount);

	const limitSize = 10 * 1024 * 1024; // 服务器单张最大上传为10M
	const limitSource = data.filter(item => item.size > limitSize);
	if (limitSource.length) return uni.showToast({
		title: "大小超过了10M",
		icon: "error"
	});

	// 多资源上传处理
	// 根据uploadReponse中上传的url是否为空判断是否上传成功
	const uploadReponses: Array<FileOption & { errMsg?: string, thumb?: string }> = [];
	const multipleRequest: Promise<any>[] = [];

	data.forEach((item, index) => {
		uploadReponses.push({ type: item.type, url: "", thumb: item.thumb });
		const request = uploadFile<{ completeUrl: string }>(item.url).then(res => {
			const isLoadSucessed = res.code === 20000;
			const response = uploadReponses[index];
			if (!isLoadSucessed) return (response.errMsg = res.msg);
			response.url = res.data.completeUrl;
		});
		multipleRequest.push(request);
	});

	// 上传成功后续处理
	Promise.all(multipleRequest).finally(() => {
		// 是否全部上传完成
		console.log("图片上传结果：", uploadReponses);
		const sucessResource = uploadReponses.filter(item => {
			item.type === "video" && (videThumbInfo[item.url] = item.thumb);
			return !!item.url;
		});
		// 显示图片
		resourceData.value = resourceData.value.concat(sucessResource);
		sucessResource.length && emits("uploadSucess", sucessResource);

		if (sucessResource.length == uploadReponses.length) return;
		const isVideo = uploadReponses[0].type === "video";  // 因为有视频时不会包含图片。
		uni.showToast({
			icon: "error",
			title: isVideo ? "视频上传失败" : `${uploadReponses.length}张图片上传失败`
		});
	});
};

const previewHandle = function (item: FileOption) {
	if (item.type === "video") {
		previewVideo.value.open(item.url);
	} else {
		const imageUrls: string[] = [];
		resourceData.value.forEach(temp => {
			if (temp.type === "video") return;
			imageUrls.push(temp.url);
		});
		let current = imageUrls.findIndex(url => url === item.url);
		uni.previewImage({
			// 先filter找出为图片的item，再返回filter结果中的图片url
			urls: imageUrls,
			current: current,
			fail() {
				uni.showToast({
					icon: "error",
					title: "预览图片失败"
				})
			},
		});
	}
}
const previewVideo = ref(null);

</script>

<style lang="scss" scoped>
.container-upload {
	display: flex;
	box-sizing: border-box;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: flex-start;

	.ratio-wrapper {
		width: 30%;
		box-sizing: border-box;
		margin-right: 28rpx; // 调整项
		margin-top: 28rpx;
		box-sizing: border-box;

		.ratio-inner {
			position: relative;
			height: 0;
			padding-bottom: 100%;
		}
	}

	.ratio-wrapper:first-child,
	.ratio-wrapper:nth-child(2),
	.ratio-wrapper:nth-child(3) {
		margin-top: 0;
	}

	// 调整项
	.ratio-wrapper:nth-child(3n) {
		margin-right: -14rpx;
	}

	.preview-wrapper {
		position: absolute;
		top: 0;
		left: 0;
		height: 100%;
		width: 100%;
		border-radius: 16rpx;
		border: 1px dashed #eee;
		background-color: rgba(247, 248, 250, 1);
		overflow: hidden;
		box-sizing: border-box;

		.delete {
			position: absolute;
			top: 0;
			right: 0;
			width: 44rpx;
			height: 44rpx;
		}

		.image {
			display: block;
			width: 100%;
			height: 100%;
		}
	}

	.upload-btn {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		gap: 16rpx;

		font-size: 28rpx;
		color: rgba(61, 61, 61);
	}

	.icon-uplaod-container {
		position: relative;
		height: 48rpx;
		width: 48rpx;

		.icon-uplaod:first-child {
			position: absolute;
			top: 50%;
			left: 0;
			width: 100%;
			height: 3px;
			background-color: rgba(133, 135, 141);
			border-radius: 3px;
			transform: translate(0, -50%);
		}

		.icon-uplaod:last-child {
			position: absolute;
			top: 50%;
			left: 0%;
			width: 100%;
			height: 3px;
			background-color: rgba(133, 135, 141);
			border-radius: 3px;
			transform: translate(0, -50%) rotate(90deg);
		}

	}

	.icon-container {
		position: absolute;
		top: 50%;
		left: 50%;
		height: 56rpx;
		width: 56rpx;
		border-radius: 50%;
		background-color: rgba(26, 26, 26, 0.80);
		transform: translate(-50%, -50%);

		.icon-play {
			position: absolute;
			left: 50%;
			top: 50%;
			height: 0;
			width: 0;
			border-left: 26rpx solid white;
			border-right: 26rpx solid transparent;
			border-top: 15rpx solid transparent;
			border-bottom: 15rpx solid transparent;
			transform: translate(-20%, -50%);
		}
	}
}
</style>