<template>
  <z-paging class="app-container">
    <template #top>
      <Navbar :type="1" leftText="我要参加"></Navbar>
      <view class="divider"></view>
    </template>

    <view class="container">
      <view class="selection-date">
        <view class="label">参加时间</view>
        <view class="date-list-wrapper">
          <view class="date-item" :class="{ 'active': selectionDate.has(item.value) }" v-for="item in timeRange.list"
            :key="item.id" @click="() => handleSelectionDate(item.value)">{{ item.label }}</view>
        </view>
      </view>

      <view class="input-item" v-if="type === 'coser'">
        <view class="label">作品</view>
        <input type="text" v-model="inputForm.artwork" placeholder="请输入">
      </view>
      <view class="input-item" v-if="type === 'coser'">
        <view class="label">COS角色</view>
        <input type="text" v-model="inputForm.cosname" placeholder="请输入">
      </view>
    </view>
    <template #bottom>
      <view class="bottom-container">
        <view class="primary-btn" @click="handleCommit">确认</view>
      </view>
    </template>

  </z-paging>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad } from "@dcloudio/uni-app";
import { joinExhibition } from '@/api/exhibitionExpo'
import { getDaysBetweenDates } from '../service-expo/hooks/useExpo'

const type = ref('')
const expo_id = ref('')
const timeRange = ref({
  start: '',
  end: '',
  list: []
})
const selectionDate = ref(new Set())
const inputForm = ref({
  artwork: '',
  cosname: ''
})
onLoad(option => {
  type.value = option.type
  timeRange.value = {
    start: option.start,
    end: option.end,
    list: getDaysBetweenDates(+option.start, +option.end)
  }
  expo_id.value = option.expo_id
})

const handleSelectionDate = (date) => {
  if (selectionDate.value.has(date)) {
    selectionDate.value.delete(date)
  } else {
    selectionDate.value.add(date)
  }
}

const handleCommit = async () => {
  try {
    const res = await joinExhibition(expo_id.value, {
      role: type.value === 'coser' ? 2 : 1,
      join_datetime: [...selectionDate.value],
      source: inputForm.value.artwork,
      character: inputForm.value.cosname
    })
    if (res.code === 20000) {
      uni.showToast({
        title: '报名成功'
      })
      uni.$emit('exhibition-detail-refresh')
      uni.navigateBack()
    }
  } catch (error) {
    console.log(error, 'joinExhibition error')
  }
}

</script>

<style scoped lang='scss'>
.divider {
  width: 100%;
  height: 2rpx;
  background-color: #eee;
  margin-bottom: 64rpx;
}


.container {
  flex: 1;
  box-sizing: border-box;
  padding: 0 32rpx;

  .selection-date {
    .label {
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2129;
      line-height: 32rpx;
    }

    .date-list-wrapper {
      margin-top: 46rpx;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 36rpx;
      align-items: start;

      .date-item {
        margin-bottom: 4rpx;
        width: 144rpx;
        height: 72rpx;
        background: #F8F7FA;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        text-align: center;
        line-height: 72rpx;
        font-size: 32rpx;
        color: #1F1F1F;

        &.active {
          background: #FE58B7;
          color: #fff;
        }
      }
    }
  }

  .input-item {
    margin-top: 48rpx;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 32rpx;

    .label {
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2129;
      line-height: 32rpx;
    }

    input {
      width: 100%;
      height: 80rpx;
      background: #F7F8FA;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
    }
  }
}

.bottom-container {
  width: 100%;
  height: 152rpx;
  box-sizing: border-box;
  padding: 0 32rpx;

  .primary-btn {
    width: 100%;
    height: 96rpx;
    background: #fe58b7;
    border-radius: 96rpx;
    color: #fff;
    text-align: center;
    line-height: 96rpx;
    font-weight: 500;
    font-size: 36rpx;
  }

}
</style>