import { requestApi } from './request'

/** 查询关注我的用户 Result */
interface ImFollowerResult {
	/** 用户 ID */
	id : string,
	/** 用户昵称 */
	nickname : string,
}
/** 查询关注我的用户 */
export const imFollower = () => {
	return requestApi<ImFollowerResult>({
		url: `/api/v1/im/follower`,
		method: 'GET',
	})
}
/** 查询关注我的用户 end */

/** 查询我关注的用户 Result */
interface ImFollowingResult {
	/** 用户 ID */
	id : string,
	/** 用户昵称 */
	nickname : string,
}
/** 查询我关注的用户 */
export const imFollowing = () => {
	return requestApi<ImFollowingResult>({
		url: `/api/v1/im/following`,
		method: 'GET',
	})
}
/** 查询我关注的用户 end */

/** 关注某个用户 Params */
interface ImToFollowingParams {
	/** 要关注用户的 ID */
	user_id : string,
}
/** 关注某个用户 */
export const imToFollowing = (params: ImToFollowingParams) => {
	return requestApi({
		url: `/api/v1/im/following`,
		data: params,
	})
}
/** 关注某个用户 end */

/** 取消关注某个用户 Params */
interface ImCancelFollowingParams {
	/** 要关注用户的 ID */
	user_id : string,
}
/** 取消关注某个用户 */
export const imCancelFollowing = (params: ImCancelFollowingParams) => {
	return requestApi({
		url: `/api/v1/im/following`,
		data: params,
		method: 'DELETE',
	})
}
/** 取消关注某个用户 end */