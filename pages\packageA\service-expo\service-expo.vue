<template>
	<z-paging class="app-container" ref="paging" :fixed="false" :safe-area-inset-bottom="true"
		:use-safe-area-placeholder="true">
		<template #top>
			<Navbar :type="1" :goBackTier="0" :leftText="`新增${categoryTitle}`" @onBack="handleBack"></Navbar>
		</template>

		<view class="wrapper">
			<view class="article-wrapper">
				<article-item :article="expoDetail" :showViewer="false">
					<view class="anime-item-wrap">
						<view class="flex title-wrap">
							<text class="title">{{ expoDetail?.name || '' }}</text>
						</view>
						<view class="time-range-container">
							<image class="icon" src="/static/icon/icon_wz.png"></image>
							<text>{{ expoDetail?.location || '' }}</text>
						</view>
						<view class="description">{{ expoDetail?.location || '' }}</view>
					</view>
				</article-item>
			</view>

			<view class="flex flex-col date-wrapper">
				<view class="flex justify-between">
					<view class="label">漫展服务时间</view>
					<view class="delete-btn" v-if="!!serviceId" @click="deleteService">删除漫展服务</view>
				</view>
				<view class="date-list">
					<view class="date-item" :class="{ active: isSameDay(item.value) !== -1 }"
						v-for="item in betweenDays" :key="item.id" @click="() => handleSelectDays(item.value)">
						{{ item.label }}
					</view>
				</view>
			</view>

			<view class="combolist-container">
				<view class="flex items-center justify-between">
					<text class="label">服务套餐</text>
					<view class="flex items-center add-combo-btn" @click="() => handleTo('add-combo')">
						<image class="icon" src="../static/service/add-circle.svg"></image>
						<text>添加套餐</text>
					</view>
				</view>
				<empty-list v-if="dataList.length === 0">
					<text>暂无服务套餐</text>
				</empty-list>
				<uni-list v-else class="dust-uni-list" :border="false">
					<uni-list-item :border="false" class="dust-uni-list-item" v-for="(item, index) in dataList"
						:key="item.id">
						<template v-slot:body>
							<view class="combo-item-wrapper" @click="() => handleTo(item.id)">
								<daliy-combo-item :item="item" />
							</view>
						</template>
					</uni-list-item>
				</uni-list>
			</view>
		</view>

		<template #bottom>
			<btn-group>
				<button type="primary" v-if="!isOnSale" class="btn-submit"
					@tap="onPublish">确认发布</button>
				<button type="primary" v-else class="btn-submit" @tap="onDesale">下架服务</button>
			</btn-group>
		</template>
	</z-paging>
</template>

<script setup>
	import {
		ref
	} from 'vue'
	import {
		onLoad
	} from '@dcloudio/uni-app'
	import {
		useTitle
	} from '../service-create/hooks/useTitle';
	import {
		useExpo,
		getDaysBetweenDates
	} from './hooks/useExpo';
	import {
		getExhibitionDetail
	} from '@/api/exhibitionExpo'
	import {
		getMineServiceDetailItems,
		deleteDimensionService,
		publishService,
		desaleService,
	} from '@/api/mineService'
	import DaliyComboItem from '../service-create/components/daliy-combo-item.vue'

	const paging = ref(null)
	const dataList = ref([])
	const serviceId = ref(null)
	const expoId = ref(null)
	const expoDetail = ref(null)
	const from = ref(null)
	const betweenDays = ref([])
	const isOnSale = ref(false)

	const {
		categoryId,
		categoryTitle,
		typeTitle
	} = useTitle()
	const {
		handleSelectDays,
		isSameDay
	} = useExpo({
		expoId,
		serviceId,
		categoryId,
		from,
		expoDetail
	})

	onLoad((option) => {
		expoId.value = option.id
		serviceId.value = option.serviceId
		from.value = option.from
		if (option.isOnSale === '1') {
			isOnSale.value = true
		} else {
			isOnSale.value = false
		}
		
		queryExpoDetail()
		if (!option.from) {
			queryCombos()
		}
	})

	uni.$on('service-expo-combo-list:reload-data', () => {
		queryCombos()
	})

	async function queryExpoDetail() {
		try {
			let res = await getExhibitionDetail(expoId.value)
			if (res.code === 20000) {
				expoDetail.value = res.data
				betweenDays.value = getDaysBetweenDates(res.data.start_datetime, res.data.end_datetime)
			}
		} catch (error) {
			console.log(error, 'getExhibitionDetail')
		}
	}

	async function queryCombos() {
		try {
			const res = await getMineServiceDetailItems(serviceId.value)

			if (res.code === 20000) {
				console.log('&*********:', res.data)
				dataList.value = res.data
			}
		} catch (error) {
			console.log(error, 'getMineServiceDetailItems service')
		}
	}

	const deleteService = async () => {
		try {
			const res = await deleteDimensionService(serviceId.value)
			if (res.code === 20000) {
				uni.$emit('service-create-detail-expo:reload-data')
				handleBack()
			}
		} catch (error) {
			console.log(error, 'deleteDimensionService service')
		}
	}

	const handleTo = (id) => {
		let url = ''
		if (id === 'add-combo') {
			if (from.value === 'add-combo' && !serviceId.value) {
				uni.showToast({
					title: '请先选择漫展服务时间',
					icon: 'none'
				})
				return
			}
			url =
				`/pages/packageA/service-create-edit/service-create-edit?serviceId=${serviceId.value}&categoryTitle=${categoryTitle.value}&typeTitle=${typeTitle.value}`
		} else {
			if (!!id) {
				url =
					`/pages/packageA/service-create-edit/service-create-edit?edit=true&itemId=${id}&serviceId=${serviceId.value}&categoryTitle=${categoryTitle.value}&typeTitle=${typeTitle.value}`
			}
		}
		url && uni.navigateTo({
			url
		})
	}

	const handleBack = () => {
		if (from.value === 'add-combo') {
			uni.$emit('service-create-detail-expo:reload-data')
		}
		uni.navigateBack({
			delta: from.value === 'add-combo' ? 2 : 1
		})
	}

	// 发布服务
	async function onPublish() {
		console.log('onPublish')
		if (!serviceId.value) {
			uni.showToast({
				title: '请先选择漫展服务时间',
				icon: 'none'
			})
			return
		}
		const res = await publishService(serviceId.value)
		if (res.code === 20000) {
			uni.showToast({
				title: '发布成功'
			})
			isOnSale.value = true
		} else {}
	}
	// 下架服务
	async function onDesale() {
		if (!serviceId.value) {
			uni.showToast({
				title: '请先选择漫展服务时间',
				icon: 'none'
			})
			return
		}
		const res = await desaleService(serviceId.value)
		if (res.code === 20000) {
			uni.showToast({
				title: '服务下架成功'
			})
			isOnSale.value = false
		} else {}
	}
</script>

<style scoped lang='scss'>
	.wrapper {
		flex: 1;
		box-sizing: border-box;
		padding: 0 24rpx;
	}

	.article-wrapper {
		margin-top: 32rpx;

		.anime-item-wrap {
			padding: 0 24rpx;

			.title-wrap {
				width: 100%;
				align-items: center;
				justify-content: space-between;
				margin-top: 20rpx;

				.title {
					font-size: 36rpx;
					font-weight: 700;
					color: #3D3D3D;
				}

				.collect-wrap {
					gap: 8rpx;
					align-items: center;
					color: #1f1f1f;
					font-size: 28rpx;

					image {
						width: 40rpx;
						height: 40rpx;
					}
				}
			}

			.time-range-container {
				margin-top: 16rpx;
				display: flex;
				align-items: center;
				gap: 8rpx;
				font-weight: 400;
				font-size: 28rpx;
				line-height: 40rpx;
				color: #48DAEA;

				.icon {
					width: 28rpx;
					height: 28rpx;
					flex-shrink: 0;
				}
			}

			.description {
				color: #aaa;
				padding-left: 36rpx;
				font-size: 28rpx;
			}
		}
	}

	.date-wrapper {
		gap: 20rpx;
		margin-top: 32rpx;

		.label {
			font-weight: 500;
			font-size: 32rpx;
			color: #3D3D3D;
			line-height: 44rpx;
		}

		.date-list {
			display: flex;
			align-items: center;
			gap: 24rpx;
			flex-wrap: wrap;

			.date-item {
				width: 100rpx;
				height: 40rpx;
				border-radius: 8rpx 8rpx 8rpx 8rpx;
				border: 2rpx solid #AAAAAA;
				font-size: 24rpx;
				color: #aaa;
				display: flex;
				align-items: center;
				justify-content: center;

				&.active {
					background: #FE58B7;
					border-color: #FE58B7;
					color: #fff;
				}
			}
		}

		.delete-btn {
			font-size: 28rpx;
			color: #FE58B7;
		}
	}

	.combolist-container {
		margin-top: 40rpx;

		.combo-item-wrapper {
			width: 100%;
		}

		.label {
			font-weight: 500;
			font-size: 32rpx;
			color: #3D3D3D;
			line-height: 44rpx;
		}

		.empty-list {
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			color: #85878D;

			.icon {
				width: 380rpx;
				height: 380rpx;
			}
		}

		.add-combo-btn {
			font-size: 28rpx;
			color: #FE58B7;
			gap: 4rpx;

			.icon {
				width: 28rpx;
				height: 28rpx;
			}
		}
	}
</style>