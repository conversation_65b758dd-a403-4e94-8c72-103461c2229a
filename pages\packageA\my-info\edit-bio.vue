<template>
  <view class="app-container">
    <Navbar :type="1" leftText="编辑简介" leftWidth="400rpx"></Navbar>
    <view class="container">
      <textarea class="textarea-ele" v-model="bio" placeholder="点击填写兴趣爱好、生活方式等个人简介"></textarea>
      <view class="textarea-tip">{{ bio.length }}/100</view>
      <view class="btn-submit edit-btn" @click="saveBio">保存</view>
    </view>
  </view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { useStore } from 'vuex';
import {
  updateUserInfo
} from '@/api/user'

const store = useStore();
const bio = ref('')

onLoad((option) => {
  console.log('onLoad', option)
  bio.value = option.bio || ''
})

function saveBio() {
  if (bio.value.length > 100) {
    uni.showToast({
      title: '简介不能超过100个字符',
      icon: 'none'
    })
    return
  }

  updateUserInfo({
    // ...userInfo.value,
    bio: bio.value
  }).then(res => {
    if (res.code === 20000) {
      store.commit('userInfo/UPDATE_USER_INFO', {
        bio: bio.value
      })
      uni.$emit('myinfo-refresh-formData-bio', bio.value)
      uni.navigateBack({
        delta: 1
      })
    }
  })
}

</script>

<style scoped lang='scss'>
.container {
  width: 100%;
  padding: 0 32rpx;
  box-sizing: border-box;
  margin-top: 40rpx;

  .textarea-ele {
    width: 100%;
    height: 352rpx;
    background: #F7F8FA;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    box-sizing: border-box;
    padding: 28rpx;
  }

  .textarea-tip {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    font-size: 36rpx;
    color: #85878D;
    margin-top: 20rpx;
  }

  .edit-btn {
    margin-top: 56rpx;
    text-align: center;
  }
}
</style>