<template>
	<z-paging class="app-container" ref="paging" v-model="searchList" @query="queryList" :safe-area-inset-bottom="true"
		:use-safe-area-placeholder="true">
		<template #top>
			<Navbar :type="1" leftText="通讯录"></Navbar>
			<view class="allPidding">
				<NotificationNavbar placeholder="用户名" @input="input" />
			</view>
		</template>
		<template #empty>
			<uv-empty />
		</template>
		<view class="content">
			<view class="messageBox" @click="toNew">
				<uv-avatar size="96rpx" mode="aspectFill" :default-url="newIcon" />
				<view class="messageTxt">新的朋友</view>
				<image class="messageIcon" :src="rightIcon" />
			</view>
			<view v-for="(item, index) in searchList" :key="item.id" @click="toUser(item)">
				<view class="messageItem">
					<uv-avatar :src="prefixFilePath(item.friend_avatar)"
						size="96rpx" mode="aspectFill" />
					<view class="messageRight">
						<view class="messageItemContent">
							<view class="itemName">{{ item.friend_nickname }}
							</view>
							<view class="itemTips">{{ item.friend_bio }}</view>
						</view>
						<view v-if="!item.is_best_friend" class="messageBtn"
							@tap.stop="() => onBtn(item, type)">同意</view>
						<!-- <view v-else-if="item.is_best_friend" class="messageBtn"
							@tap.stop="() => onBtn(item, type)">删除朋友</view> -->
					</view>
				</view>
			</view>
		</view>
	</z-paging>
</template>

<script setup>
	import {
		ref,
		onMounted,
		watch
	} from 'vue';
	import {
		useStore
	} from 'vuex';
	import {
		getFriendList
	} from '@/api/message';
	import MyUserItem from '@/components/my-user-item/MyUserItem.vue';
	import newIcon from '@/static/icon/<EMAIL>';
	import rightIcon from '@/static/icon/<EMAIL>';

	const store = useStore();
	const messageList = ref([]);
	const searchList = ref([]);

	const input = (value) => {
		searchList.value = !value ?
			messageList.value :
			messageList.value.filter(item => item.friend_nickname.includes(value))
		paging.value.complete(searchList.value);
	}
	// 获取好友列表
	const getList = async () => {
		const {
			data
		} = await getFriendList()
		messageList.value = data;
		searchList.value = data;
		if (code === 20000) {
			paging.value.complete(data);
		} else {
			paging.value.complete([]);
		}
	}
	// 去新的朋友
	const toNew = () => {
		uni.navigateTo({
			url: `/pages/tabbar/notification/newFriend`
		});
	}
	// 跳转用户主页
	function toUser(item) {
		uni.navigateTo({
			url: `/pages/home-pages/user-mine?id=${item.friend_id}`
		});
	}
	// 当前环境域名和资源地址拼接
	const prefixFilePath = (url) => {
		if (!url) return '';
		return store.state.common.cloudFileUrl + url;
	}

	function onBtn() {}
	onMounted(() => {
		getList()
	});
</script>
<style lang="scss" scoped>
	.allPidding {
		padding: 32rpx 24rpx 8rpx;
	}

	.content {
		padding: 0 32rpx;

		.messageBox {
			display: flex;
			align-items: center;
			padding: 32rpx 0;
			border-bottom: 2rpx solid #eeeeee;

			.messageTxt {
				flex: 1;
				margin-left: 24rpx;
			}

			.messageIcon {
				width: 40rpx;
				height: 40rpx;
			}
		}
	}

	.messageItem {
		display: flex;
		align-items: center;

		.messageRight {
			display: flex;
			align-items: center;
			flex: 1;
			min-height: 96rpx;
			padding: 32rpx 0;
			border-bottom: 2rpx solid #eeeeee;
		}

		.messageItemContent {
			margin: 0 24rpx;
			flex: 1;
		}

		.itemName {
			font-size: 32rpx;
			color: #1F1F1F;
			line-height: 32rpx;
		}

		.itemTips {
			margin-top: 16rpx;
			font-size: 28rpx;
			color: #85878D;
			line-height: 28rpx;
		}

		.messageBtn {
			width: 144rpx;
			height: 72rpx;
			text-align: center;
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 72rpx;
			background: #FE58B7;
			border-radius: 48rpx;
		}
	}
</style>