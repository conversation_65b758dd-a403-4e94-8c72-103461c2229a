<template>
	<view class="container-warpper" @tap.stop="clickHandle">
	  <view class="label">
		<slot>{{ props.label }}</slot>
	  </view>
	  <slot name="icon" :selected="selected">
		  <image class="icon" v-if="selected" src="/static/icon/check-on.png"></image>
		  <image class="icon" v-else src="/static/icon/check.png"></image>
	  </slot>
	</view>
</template>

<script setup lang="ts">
	import { ref } from "vue";
	const selected = defineModel<boolean>({default: false});
	const props = defineProps({
		label: {
			type: String,
			require: true
		}
	});
	
	interface Emits {
		(e: "change", selected: boolean): void
	}
	const emits = defineEmits<Emits>();
	const clickHandle = function () {
		selected.value = !selected.value;
		emits("change", selected.value);
	}
	
</script>

<style scoped lang='scss'>
.container-warpper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  width: 100%;
  min-height: 80rpx;
  box-sizing: border-box;
  padding: 24rpx;
  background-color: #F7F8FA;
  border-radius: 16rpx;
  
  .label {
    font-size: 32rpx;
    color: #3D3D3D;
    line-height: 32rpx;
  }

  .icon {
    width: 40rpx;
    height: 40rpx;
  }
}
</style>