// 2025/06/03新增：使用use封装notice管理属性和方法
import { Ref, ref, unref, watchEffect } from "vue";
export default function useNoticeManager(savePrefixKey: string, saveRefKey: Ref<string>, cutdownNum: number = 5) {
	const isNotFirstShow = ref(false);
	const showNotice = ref(false);
	const cutdown = ref(0);

	watchEffect(() => {
		const saveKey = savePrefixKey + unref(saveRefKey);
		isNotFirstShow.value = uni.getStorageSync(saveKey) ? true : false;
		showNotice.value = isNotFirstShow.value ? false : true;
		cutdown.value = isNotFirstShow.value ? 0 : cutdownNum;
	});
	
	function canShowNotice() {
		if (isNotFirstShow.value) return false;
		isNotFirstShow.value = true;
		showNotice.value = true;
		return true;
	}
	
	const onConfirmNotice = () => {
		showNotice.value = false;
		if (isNotFirstShow.value) return
		isNotFirstShow.value = true;
		uni.setStorage({
			key: savePrefixKey + unref(saveRefKey),
			data: true
		});
	}
	
	const onCloseNotice = () => {
		showNotice.value = false;
		if (isNotFirstShow.value) return
		isNotFirstShow.value = true;
		uni.setStorage({
			key: savePrefixKey + unref(saveRefKey),
			data: true
		});
	}
	
	return {cutdown, showNotice, canShowNotice, onConfirmNotice, onCloseNotice}
}
	