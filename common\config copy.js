const Config = {
	// 接口域名
	// hostUrl: 'https://develop.ccycat.com', // 开发环境
	// hostUrl: 'https://staging.ccycat.com', // 测试环境(预发布环境)
	hostUrl: 'https://production.ccycat.com', // 生产环境
	// Websocket
	// wsUrl: 'wss://develop.ccycat.com', // 开发环境
	// wsUrl: 'wss://staging.ccycat.com', // 测试环境(预发布环境)
	wsUrl: 'wss://production.ccycat.com', // 生产环境
	//上传文件访问地址
	// fileUrl: 'https://develop.cos.ccycat.com/', // 开发环境
	// fileUrl: 'https://staging.cos.ccycat.com/', // 测试环境(预发布环境)
	fileUrl: 'https://production.cos.ccycat.com/', // 生产环境

	// cloudFileUrl: 'https://ciyuanxingchen-1306919981.cos.ap-beijing.myqcloud.com/', // 开发环境
	// cloudFileUrl: 'https://staging.cos.ccycat.com/', // 测试环境(预发布环境)
	cloudFileUrl: 'https://production.cos.ccycat.com/', // 生产环境

	amapKey: 'b4988972efa8de3e1544093e33b4e16f',
	tencentKey: '4JLBZ-MIDCM-N7J6M-6B6OS-JPJAK-32FLV',
	mapSearchUrl: "https://apis.map.qq.com/ws/place/v1",	//地图搜索接口
	env_version: 'release', // develop | trial | release
}

export default Config