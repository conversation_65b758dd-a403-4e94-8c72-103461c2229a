import { requestApi } from './request';

/** 查询服务列表 Params */
interface GetDimensionServiceParams {
	/** 分页页码 */
	page: number;
	/** 分页大小 */
	size: number;
	/** 省份 ID */
	province: number;
	/** 城市 ID */
	city: number;
	/** 区县 ID */
	district: number;
	/** 开始时间戳 */
	start_time: number;
	/** 结束时间戳 */
	end_time: number;
	/** 关键字查询 */
	keyword: string;
	/** 分类 ID */
	categories: number[];
	/** 限制大小  */
	limit: number;
	/**  */
	skip: number;
}

/** 查询服务列表 Result */
interface GetDimensionServiceResult {
	results: {
		id: number;
		/** name */
		name: number;
		/** cover */
		cover: number;
		/** score */
		score: number;
		/** views_count */
		views_count: number;
		/** user_id */
		user_id: number;
		/** user_nickname */
		user_nickname: string;
		/** user_avatar */
		user_avatar: string;
		/** items */
		items: Array<object>;
		/** created_at */
		created_at: number;
	}[];
	/** 符合查询条件的查询服务总数 */
	count: number;
}
/** 查询服务列表 */
export const getDimensionServices = (params: GetDimensionServiceParams) => {
	return requestApi<GetDimensionServiceResult>({
		url: `/api/v1/foreground/home/<USER>/service`,
		method: 'GET',
		data: params,
		isToken: false,
		loading: false,
	});
};
/** 查询服务列表 end */

/** 查询服务详情 Result */
interface GetDimensionServiceDetailResult {
	id: number;
	/** name */
	name: number;
	/** cover */
	cover: number;
	/** score */
	score: number;
	/** review_count */
	review_count: number;
	/** tags */
	tags: Array<string>;
	/** editor */
	editor: string;
	/** detail */
	detail: string;
	/** user_id */
	user_id: number;
	/** user_avatar */
	user_avatar: string;
	/** items */
	items: Array<object>;
}
/** 查询服务详情 */
export const getDimensionServiceDetail = (id: number) => {
	return requestApi<GetDimensionServiceDetailResult>({
		url: `/api/v1/foreground/home/<USER>/service/${id}`,
		method: 'GET',
		isToken: false
	});
};
/** 查询服务详情 end */

/** 查询服务项目 Result */
interface GetDimensionServiceItemsResult {
	/** 服务项总数量 */
	count: number;
	results: {
		id: number;
		/** name */
		name: number;
		/** cover */
		cover: number;
		/** price */
		price: number;
		/** bond */
		bond: number;
		/** sold */
		sold: number;
	}[];
}

/** 查询服务项目 */
export const getDimensionServiceItems = (id: number) => {
	return requestApi<GetDimensionServiceItemsResult>({
		url: `/api/v1/foreground/home/<USER>/service/${id}/item/`,
		method: 'GET',
		isToken: false
	});
};
/** 查询服务项目 end */

/** 查询服务评论 Params */
interface GetDimensionServiceReviewwsParams {
	/** 父级评论 ID (0: 根评论) */
	parent: number;
	/** 页码 默认 0 */
	page: number;
	/** 分页大小 */
	size: number;
	/** 条数 (page ==0 && size == 0 生效) 默认 10  */
	limit: string;
	/** 跳过 (page ==0 && size == 0 生效) 默认 0 */
	skip: string;
}

/** 查询服务评论 Result */
interface GetDimensionServiceReviewwsResult {
	results: {
		/** 服务评论 ID */
		id: number;
		/** 评论发布者 ID */
		user_id: number;
		/** 评论发布者头像 */
		user_avatar: number;
		/** 服务评论用户昵称 */
		user_nickname: number;
		/** 服务评论内容编辑器 */
		editor: number;
		/** 服务评论内容(违规/删除 为空) */
		content: number;
		/** 服务平均评分(100百分制) */
		rating: string;
		/** 是否默认好评 */
		is_default_positive: string;
		/** 是否违规 */
		is_violation: number;
		/** 是否被删除 */
		is_deleted: number;
		/** 父级评论 ID */
		parent_id: number;
		/** 该评论的发表者是不是当前用户 */
		is_current_user: number;
		/** 评论时间 */
		created_at: number;
	}[];
	/** 查询服务评论总数 */
	count: number;
}

/** 查询服务评论 */
export const getDimensionServiceReviews = (id: number, params: GetDimensionServiceReviewwsParams) => {
	return requestApi<GetDimensionServiceReviewwsResult>({
		url: `/api/v1/foreground/home/<USER>/service/${id}/review`,
		method: 'GET',
		data: params,
		isToken: false,
	});
};
/** 查询服务评论 end */

/** 收藏服务 */
export const collectDimensionService = (service_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/home/<USER>/service/${service_id}/favorite`
	});
};
/** 收藏服务 end */
/** 摄影服务类别 */
export const dimensionCategory = () => {
	return requestApi({
		url: `/api/v1/foreground/home/<USER>/service/category`,
		method: 'GET',
		isToken: false
	});
};
/** 摄影服务类别 end */

/** 获取作品 Results */
interface GetArtworksResult {
	count: number;
	results: {
		id: number;
		service_id: number;
		type: number;
		mime_type: string;
		url: string;
		created_at: number;
		updated_at: number;
	}[];
}

interface GetArtworksParams {
	page: number;
	size: number;
	limit: number;
	skip: number;
}

/** 获取作品集 */
export const getArtworks = (service_id: number, params: GetArtworksParams) => {
	return requestApi<GetArtworksResult>({
		url: `/api/v1/foreground/mine/service/${service_id}/artwork`,
		method: 'GET',
		data: params
	});
};

interface AddArtworksParams {
	type: number;
	mime_type: string;
	url: string;
}
/** 添加作品 */
export const addArtworks = (service_id: number, params: AddArtworksParams) => {
	return requestApi({
		url: `/api/v1/foreground/mine/service/${service_id}/artwork`,
		data: params
	});
};

/** 删除作品 */
export const deleteArtworks = (service_id: number, artwork_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/mine/service/${service_id}/artwork/${artwork_id}`,
		method: 'DELETE'
	});
};
