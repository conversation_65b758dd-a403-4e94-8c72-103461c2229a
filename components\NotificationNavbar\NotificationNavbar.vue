<template>
	<view class="box" @click="tosearch()">
		<uni-search-bar class="flex-1" v-model="searchValue" radius="8" :placeholder="placeholder"
			:clearButton="clearButton" :focus="focu" :cancelButton="cancelButton" bgColor="#fff" @confirm="search" @input="input"
			@cancel="cancel" @clear="clear" @focus="focus" @blur="blur">
			<template #searchIcon>
				<image class="icon-search" src="/static/icon/<EMAIL>" />
			</template>
		</uni-search-bar>
	</view>
</template>
<script>
	export default {
		options: {
			// 微信小程序中 options 选项
			styleIsolation: 'shared', // 启动样式隔离。
		}

	}
</script>
<script setup>
	import {
		ref
	} from 'vue'

	defineOptions({
		name: 'notificationNavbar',
	})

	const props = defineProps({
		// 是否跳转
		state: {
			type: Boolean,
			default: false, // 默认 1 返回上一页，如果要自定义返回，则 传 0
		},
		// 返回的页数
		goBackTier: {
			type: Number,
			default: 1, // 默认 1 返回上一页，如果要自定义返回，则 传 0
		},

		// 搜索占位符
		placeholder: {
			type: String,
			default: '',
		},
		// 显示取消按钮
		cancelButton: {
			type: String,
			default: 'none',
		},
		clearButton: {
			type: String,
			default: 'none',
		},
		focu: {
			type: Boolean,
			default: false,
		},
	})

	const searchValue = ref('')

	const emit = defineEmits(['onBack', 'confirm', 'input', 'cancel', 'clear', 'focus', 'blur', 'tosearch'])

	function onBack() {
		// 返回的页数
		if (props.goBackTier !== 0) {
			uni.navigateBack({
				delta: props.goBackTier
			})
		} else {
			emit('onBack')
		}
	}

	function tosearch() {
		if (props.state) {
			uni.navigateTo({
				url: `/pages/tabbar/notification/search`
			})
		}else {
			emit('tosearch')
		}
	}

	function search(value) {
		emit('confirm', value)
	}

	function input(value) {
		emit('input', value)
	}

	function cancel(value) {
		emit('cancel', value)
	}

	function clear(value) {
		emit('clear', value)
	}

	function focus(value) {
		emit('focus', value)
	}

	function blur(value) {
		emit('blur', value)
	}
</script>
<style lang="scss" scoped>
	:deep(.uni-searchbar__box) {
		background-color: rgba(0, 0, 0, 0) !important;
	}

	:deep(.uni-searchbar) {
		padding: 0 20rpx !important;
	}

	.box {
		width: 100%;
		height: 72rpx;
		background: #FFFFFF;
		border-radius: 16rpx;
		border: 4rpx solid #1D2129;
		display: flex;
		align-items: center;
	}

	.icon-search {
		width: 32rpx;
		height: 32rpx;
	}

	.back {
		display: flex;
		align-items: center;
	}

	.icon-back {
		width: 44rpx;
		height: 46rpx;
	}
</style>