<template>
	<view class="container">
		<textarea class="textarea" @keyboardheightchange="onKeyboardHeightChange" v-model="comment"></textarea>

		<view class="image-wrapper" v-if="image">
			<image class="preview" :src="image" @click="onUploadImage"></image>
			<image class="close-icon" src="/static/icon/dimension/close.png" @click="image = ''"></image>
		</view>

		<view class="footer">
			<view class="flex">
				<!-- 				<image class="icon" src="/static/icon/dimension/picture.png" @click="onUploadImage"></image -->
				<image class="icon" src="/static/icon/dimension/emoji.png" @click="showEmoji = !showEmoji"></image>
			</view>
			<button class="btn" @click="sendComment">发送</button>
		</view>
		<view class="emoji-wrapper" :class="{ active: showEmoji }">
			<view class="emoji" v-for="(emoji, index) in emojiList" :key="index" @click="() => onEmojiClick(emoji)">
				{{ emoji }}
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue';
import { uploadFile } from '@/api/request';
import emojiList from '/static/emoji/emoji.json';

const emit = defineEmits('onSubmit', 'keyboardHeightChange');
const comment = ref('');
const showEmoji = ref(false);
const image = ref('');

const sendComment = async () => {
	if (image.value) {
		const res = await uploadFile(image.value);
		if (res.code === 20000) {
			image.value = '';
			emit('onSubmit', res.data.url, 'file');
		}
	}
	if (comment.value) {
		emit('onSubmit', comment.value);
		comment.value = '';
	}
};

const onEmojiClick = (emoji) => {
	showEmoji.value = false;
	comment.value += emoji;
};

const onKeyboardHeightChange = (e) => {
	const height = e.detail.height;
	emit('keyboardHeightChange', height);
};

const onUploadImage = () => {
	uni.chooseImage({
		count: 1,
		success: (res) => {
			image.value = res.tempFilePaths[0];
		}
	});
};
</script>

<style scoped lang="scss">
.container {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	padding: 28rpx 40rpx;

	.textarea {
		width: 100%;
		height: 132rpx;
		background: #f3f3f3;
		border-radius: 40rpx;
		padding: 16rpx;
		box-sizing: border-box;
	}

	.footer {
		margin-top: 28rpx;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;

		& > view {
			gap: 56rpx;
			align-items: center;
			flex: 1;

			.icon {
				width: 56rpx;
				height: 56rpx;
			}
		}
	}

	.emoji-wrapper {
		margin-top: 24rpx;
		display: flex;
		flex-wrap: wrap;
		box-sizing: border-box;
		background-color: #f4f4f4;
		border-radius: 32rpx;
		font-size: 32rpx;
		height: 0;
		overflow-y: auto;
		transition: height 0.3s;

		.emoji {
			padding-left: 24rpx;
			padding-top: 24rpx;
		}

		&.active {
			height: 400rpx;
			padding-bottom: 24rpx;
		}
	}

	.image-wrapper {
		margin-top: 24rpx;
		width: 128rpx;
		height: 128rpx;
		border-radius: 16rpx;
		position: relative;

		.preview {
			width: 100%;
			height: 100%;
		}

		.close-icon {
			position: absolute;
			top: 0;
			right: 0;
			width: 32rpx;
			height: 32rpx;
		}
	}

	.btn {
		width: 136rpx;
		height: 64rpx;
		background: #fe58b7;
		border-radius: 32rpx;
		line-height: 64rpx;
		text-align: center;
		color: #fff;
		font-size: 32rpx;
	}
}
</style>
