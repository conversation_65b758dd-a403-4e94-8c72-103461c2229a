<template>
	<view class="container">
		<publish-navigate-row v-for="item in props.data" :key="item.value" :label="item.text" @click="selectHandle(item)" />
	</view>
</template>

<script lang="ts" setup>
	import { ref, PropType } from "vue";
	interface DateItem {
		text: string;
		value: string;
	}
	interface Props {
		data: DateItem[]
	}
	const props = defineProps<Props>();

	interface Emits {
		(e: "select", value: DateItem);
	}
	const emits = defineEmits<Emits>();
	const selectHandle = (value: DateItem) => {
	
		emits("select", value);
	}
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		gap: 24rpx;
		padding: 32rpx 0;
	}
</style>