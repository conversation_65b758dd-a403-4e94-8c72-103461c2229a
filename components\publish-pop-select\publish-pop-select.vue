<template>
	<view class="container" @click.stop>
		<view class="main">
			<view class="header-warpper">
				<view class="header-title">
					<text class="cancle" @click="closeHandle">取消</text>
					<text>{{props.title}}</text>
					<text class="confirm" @click="confirmHandle">确定</text>
					<!-- <image class="close" src="@/static/icon/publish/<EMAIL>" mode="aspectFit" @click="closeHandle" /> -->
				</view>
				
				<view class="header-slot" v-if="props.showSearch">
					<!-- <slot name="header">
						<publish-search-input v-model="search"></publish-search-input>
					</slot> -->
					<publish-search-input class="search" v-model="search"></publish-search-input>
				</view>
			</view>
			<scroll-view scroll-y="true" class="body" 
				:refresher-enabled="props.enableLoad" 
				:refresher-triggered="isRefresh" 
				:show-scrollbar="false"
				@refresherrefresh="onRefresh" @scrolltolower="onLoadMore"
			>
				<view class="item" v-for="(item, index) in selectOptions" @click="itemClick(item)" :key="index">
					<slot :data="item">
						<text :class="{'selcted': item.selected}">{{item.text}}</text>
						<image class="image" v-if="item.selected" src="@/pages/packages-publish/static/<EMAIL>" mode="aspectFit" />
					</slot>
				</view>
				<uni-load-more v-if="props.enableLoad && selectOptions.length" :status="loadMoreState" @clickLoadMore="onLoadMore"></uni-load-more>
			</scroll-view>

			<!-- <view class="footer">
				<slot name="footer">
					<view class="makesure" @click="closeHandle">确认</view>
				</slot>
			</view> -->
		</view>
	</view>
</template> 
<script setup lang="ts">
import { ref, computed, onMounted, PropType, watchEffect, watch } from 'vue';

interface PropItem {
	text: string;
	value: string | number;
}

interface Props {
	title?: string;			// 标题
	localdata: PropItem[] // 下拉框选项组
	multiple?: boolean;		// 是否可以多选,
	// 加载相关
	enableLoad?: boolean;	// 是否有上拉和下拉夹杂
	pageNum?: number;		// 默认页码为1
	pageSize?: number; 		// 默认页码为2
	auto: boolean; 			// 挂载时自动触发queryList 默认为true
	showSearch: boolean;	// 是否显示搜索框，默认为false
	queryList: (pageNum: number, pageSize: number) => void
}

interface SelectItem extends PropItem {
	selected: boolean;
}

const props = withDefaults(defineProps<Props>(), {
	title: "选择服务",
	multiple: false,
	enableLoad: false,
	pageNum: 1,
	pageSize: 10,
	auto: true,
	showSearch: false
});

type ValueType = string | number;
const model = defineModel<ValueType | ValueType[]>();
const slots = defineSlots<{
	default(props: SelectItem): any;
	heder(): any;
	footer(): any;
}>();

defineExpose({
	reload: onRefresh,
	loadMore: onLoadMore,
	complete: complete,
});
onMounted(() => {
	if (!props.auto) return;
	onRefresh()
});

const search = ref("");
const selectOptions = ref<SelectItem[]>([]);

watchEffect(() => {
	const localData = props.localdata || [];
	if (!localData) {
		return selectOptions.value = [];
	};
	
	const searchText = search.value;
	console.log("----1-", searchText)
	const modeIsArray = Array.isArray(model.value);
	const selectedKeys: Array<string | number> = modeIsArray ? model.value as any : [model.value];
	
	selectOptions.value = localData.map(item => {
		// 判断是否包含搜索字段
		const isContainerSearch = searchText ? item.text.includes(searchText) : true;
		if (!isContainerSearch) return undefined;
		const findeIndex = selectedKeys.findIndex(value => value == item.value);
		return {
			text: item.text,
			value: item.value,
			selected: findeIndex >= 0
		}
	}).filter(Boolean);
});

interface Emits {
	(e: "close"): void;
	(e: "confirm", value: SelectItem[] | SelectItem | undefined): void;
	(e: "change", value: SelectItem): void;
}
const emits = defineEmits<Emits>();
const closeHandle = () => {
	emits("close");
};
const confirmHandle = () => {
	const newSlectedValues: Array<string | number> = [];
	const selectedItems: SelectItem[]  = []
	selectOptions.value.forEach(option => {
		if (!option.selected) return;
		newSlectedValues.push(option.value);
		selectedItems.push({...option});
	});
	// 同步model
	if (props.multiple) {
		model.value = newSlectedValues;
	} else {
		model.value = newSlectedValues.length ? newSlectedValues[0] : "";
	}
	emits("confirm", props.multiple ? selectedItems : selectedItems[0]);
}

const itemClick = (item: SelectItem) => {
	// 单选时清楚选中状态
	!props.multiple && selectOptions.value.forEach(option => {
		if (!option.selected) return;
		option.selected = false;
	});
	const itemSelected = !item.selected;
	item.selected = itemSelected;
	emits("change", item);
}


// 上拉和下拉相关
let isRefresh = ref(false);
let pageNo: number = props.pageNum;
let pageSize: number = props.pageSize;

function onRefresh() {
	if (!props.enableLoad) return console.log("11");
	if (isRefresh.value) return console.log("22");
	isRefresh.value = true;
	// srcollView中refresher-triggered设置为ture需要间隔下在设置为fals才会隐藏显示
	if (!props.queryList) return setTimeout(() => {
		isRefresh.value = false;
		console.log("33")
	}, 10);
	pageNo = 1;
	props.queryList(pageNo, pageSize);
};

// loadMore显示状态
const enum LoadMoreState {
	more = "more",
	loading = "loading",
	noMore = "noMore"
}

const loadMoreState = ref(LoadMoreState.more);
function onLoadMore() {
	if (!props.enableLoad) return;
	// 如果正在刷新则不能加载更多
	if (isRefresh.value) return;
	if (loadMoreState.value !== LoadMoreState.more) return;
	if (!props.queryList) return loadMoreState.value = LoadMoreState.more;
	loadMoreState.value = LoadMoreState.loading;
	pageNo += 1;
	props.queryList(pageNo, pageSize);
}

function complete(canLoadMore = true, loadFail = false) {
	setTimeout(() => isRefresh.value = false);
	loadMoreState.value = canLoadMore ? LoadMoreState.more : LoadMoreState.noMore;
	if (!loadFail) return;
	// 加载失败重新请求该页
	pageNo -= 1;
}

</script>

<style lang="scss" scoped>
.container {
	position: fixed;
	display: flex;
	top: 0;
	left: 0;
	height: 100%;
	width: 100%;
	flex-direction: column;
	justify-content: flex-end;
	width: 100%;
	background-color: rgba(0, 0, 0, 0.4);
	z-index: 100;
	box-sizing: border-box;
}

.main {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 750rpx;
	left: 0;
	background-color: white;
	border-radius: 32rpx 32rpx 0rpx 0rpx;
	overflow: hidden;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
	.header-warpper {
		flex: 0 0 auto;
		padding: 12rpx 32rpx;
		color: rgba(31, 31, 31, 1);
		.header-title {
			display: flex;
			justify-content: space-between;
			align-items: center;
			position: relative;
			min-height: 80rpx;
			font-size: 32rpx;
			text-align: center;
			.cancle {
				color: rgba(170, 170, 170, 1);
			}
			.confirm {
				color: rgba(254, 88, 183, 1)
			}
			.close {
				position: absolute;
				padding: 10rpx;
				right: 28rpx;
				top: 16rpx;
				height: 48rpx;
				width: 48rpx;
			}
		}
		
		.header-slot {
			display: flex;
			align-items: center;
			justify-content: stretch;
			padding-top: 12rpx;
		}
		.search {
			flex: auto;
		}
	}

	.body {
		display: flex;
		flex: 1 1 auto;
		flex-direction: column;
		justify-content: flex-start;
		column-gap: 1px;
		padding: 0 32rpx;
		box-sizing: border-box;
		overflow-x: hidden;

		.item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 80rpx;
			width: 100%;
			
			font-size: 32rpx;
			color: rgba(31, 31, 31, 1);
			border-bottom: 1px solid rgba(238, 238, 238);
			.selcted {
				color: rgba(254, 88, 183, 1);
			}
			.image {
				height: 40rpx;
				width: 40rpx;
			}
		}

	}
	::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
		color: transparent;
	}
	
	.footer {
		display: flex;
		padding: 20rpx 44rpx;
		margin-bottom: 20rpx;
		
		.makesure {
			width: 100%;
			height: 78rpx;
			line-height: 78rpx;
			border-radius: 40rpx;
			background-color: rgba(254, 88, 183);
			font-size: 32rpx;
			text-align: center;
			color: white;
		}
	}
}
</style>
