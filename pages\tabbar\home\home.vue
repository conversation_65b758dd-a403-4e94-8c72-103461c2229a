<template>
	<view class="app-container">
		<z-paging ref="paging" :auto="false" :fixed="true" :refresher-enabled="false" :loading-more-enabled="false"
			:hide-empty-view="true">
			<template #top>
				<Navbar searchTo="/pages/packageA/search/search" @input="searchInput" :type="0"
					:showSearch="true" placeholder="漫展" :rightWidth="rightWidth"/>
				<view class="header px-24">
					<!-- <picker @change="bindPickerChange" @columnchange="bindPickerColumnChange" mode="multiSelector"
						:range="provinceNames" :value="provinceIndex">
						<view class="location-container" @click="handleLocation">
							<image class="icon" src="/static/icon/icon_wz.png"></image>
							<text>{{ city.name }}</text>
							<image class="icon small-icon" src="/static/icon/dust_icon_arrow_down.png"></image>
						</view>
					</picker> -->
					<view class="address">
						<dust-location-picker @confirm="bindPickerChange">
							<template #trigger>
								<view class="location-container" @click="handleLocation">
									<image class="icon" src="/static/icon/icon_wz.png"></image>
									<text class="address-lable">{{ city.name }}</text>
									<image class="icon small-icon" src="/static/icon/dust_icon_arrow_down.png"></image>
								</view>
							</template>
						</dust-location-picker>
					</view>
					<view class="tabs-container">
						<dust-tabs v-model="activeTab" :list="tabList" tabType="primary"></dust-tabs>
					</view>
				</view>
			</template>

			<view class="flex-1 px-30" :style="{ height: contentHeight }">
				<!-- 日常区 -->
				<dimension-plaza v-if="activeTab === 0" :search="search" class="flex-1" />
				<!-- 漫展专区 -->
				<anime-expo v-if="activeTab === 1" :search="search" class="flex-1" />
			</view>

			<template #bottom>
				<cc-myTabbar :tabBarShow="0"></cc-myTabbar>
			</template>
		</z-paging>
		<quick-login-dialog></quick-login-dialog>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import { onShow } from '@dcloudio/uni-app';
import DimensionPlaza from './components/dimension-plaza';
import AnimeExpo from './components/anime-expo';
import useContentHeight from '@/composables/useContentHeight.ts'
import { isApp } from '@/common/utils'

const store = useStore();
const rightWidth = computed(() => isApp() ? '0' : undefined);
const commonStore = computed(() => store.state.common);
const city = computed(() => store.state.location.city || '');
const province = computed(() => store.state.location.province || '');
const provinceIndex = computed(() => {
	const i = store.state.location.provinces.findIndex((item) => item.id === province.value.id);
	const j = store.state.location.cities?.findIndex((item) => item.id === store.state.location.city.id);
	return [i, j];
});
const provinceNames = computed(() => [store.state.location.provinces.map((item) => item.abbreviation), store.state.location.cities?.map((item) => item.name) || []]);
const initRef = ref(false)
const activeTab = ref(0);
const tabList = ref([{
	name: '日常区',
	value: 0
},
{
	name: '漫展专区',
	value: 1
}
]);
const { contentHeight } = useContentHeight({
  baseSpacing: '204rpx', // 可调整的值
});

onShow(() => {
	uni.hideTabBar();
	if (initRef.value) {
		store.dispatch('common/getAdminSetting')
	}
});

onMounted(() => {
	uni.hideTabBar();
	console.log(city.value, 'test onMounted')
	initRef.value = true
});

function onBack() {
	console.log('自定义返回');
}

const search = ref("");
function searchInput(v) {
	search.value = v;
	console.log('searchInput:', typeof search.value, search.value === "");
}

function bindPickerChange(e) {
	const [p, c] = e;
	
	// 使用Promise或等待城市数据更新完成后再触发事件
	store.dispatch('location/setLocationByCode', `${p?.code}${c?.code}`).then(() => {
		// 确保位置数据已更新
		setTimeout(() => {
			// 漫展数据重新获取
			uni.$emit('location-change-refresh-expos');
			// 日常区数据重新获取
			uni.$emit('location-change-refresh-dimension');
		}, 100);
	});
}

function bindPickerColumnChange(e) {
	console.log(e);
	if (e.detail.column === 0) {
		const p = store.state.location.provinces[e.detail.value];
		store.dispatch('location/fetchCities', p.id);
	}
}

const handleLocation = () => {
	// TODO
};
</script>

<style lang="scss" scoped>
.app-container {
	.header {
		height: 100rpx;
		display: flex;
		align-items: center;
		// justify-content: space-between;
		padding-top: 28rpx;
		width: 100%;
		overflow: hidden;
		box-sizing: border-box;
		gap: 40rpx;
		.address {
			flex: none;
			font-size: 28rpx;
			color: #1D2129;
			max-width: 8em;
			overflow: hidden;
		}
		.tabs-container {
			flex: none;
			height: 32rpx;
			line-height: 32rpx;
		}
		.empty {
			flex: 1 1 0;
		}
		.location-container {
			display: flex;
			align-items: center;
			gap: 8rpx;
			overflow: hidden;
			.address-lable {
				overflow: hidden;
				white-space: nowrap;    /* 不换行 */
				overflow: hidden;       /* 超出容器的部分隐藏 */
				text-overflow: ellipsis;
			}
			.icon {
				width: 24rpx;
				height: 24rpx;
				flex: 0 0 auto;

				&.small-icon {
					width: 22rpx;
					height: 20rpx;
				}
			}
		}
	}
}
</style>
