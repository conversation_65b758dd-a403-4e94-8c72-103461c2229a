<template>
  <view class="wrapper">
    <Navbar :type="1" leftText="提现"></Navbar>
    <view class="container">
      <view class="header">
        <view class="label">提现方式</view>
        <view class="payment-wrapper" @click="() => handleTo('bind-card')">
          <image class="icon" :src="bankTypeIcon"></image>
          <text>{{ bankcardName }}</text>
        </view>
      </view>

      <view class="content">
        <view class="label">提现金额</view>
        <view class="input-wrapper">
          <view class="input-prefix">￥</view>
          <input class="input-value" type="digit" v-model="amount">
        </view>
        <view class="tip">
          可提现金额 ¥{{ maxBalance }}
        </view>
      </view>

      <view class="footer" @click="handleCommit">确认</view>

      <view class="quesion-documentation">常见问题</view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import bankTypeData from '../wallet-bankcard/bankType.json'
import { withdrawalToBank } from '@/api/wallet'

const amount = ref('')
const maxBalance = ref(0)
const bankcardName = ref('')
const bankCardId = ref('')

onLoad((option) => {
  bankcardName.value = option.bankcardName
  bankCardId.value = option.bankcardId
  const maxBalanceData = parseFloat(option.balance)
  maxBalance.value = isNaN(maxBalanceData) ? 0 : maxBalanceData
})

const bankTypeIcon = computed(() => {
  let icon = ''
  for (const key in bankTypeData) {
    if (bankTypeData[key] === bankcardName.value) {
      icon = key
      break
    }
  }
  if (icon) {
    return `https://mdn.alipayobjects.com/fin_instasset/uri/img/as/instasset/${icon}/BANK_LOGO/PURE?zoom=40w_40h.png`
  } else {
    return ''
  }
})

const handleCommit = async () => {
  if (isNaN(Number(amount.value))) {
    return
  }

  if(+amount.value > maxBalance.value) {
    uni.showToast({
      title: `提现金额不能超过 ${maxBalance.value}`,
      icon: 'none'
    })
    return
  }

  try {
    const params = {
      amount: amount.value * 100,
      bank_id: +bankCardId.value
    }
    const res = await withdrawalToBank(params)
    if (res.code === 20000) {
      uni.$emit('wallet-page-reload-data')
      uni.navigateBack({
        delta: 1
      })
    }
  } catch (error) {
    console.log(error, 'withdrawalToBank')
  }
}

const handleTo = (type) => {
  let url = ''
  if (type === 'bind-card') {
    url = '/pages/packageA/wallet-bankcard/wallet-bankcard?notBind=false'
  }

  url && uni.navigateTo({
    url
  })
}

</script>

<style scoped lang='scss'>
.wrapper {
  display: flex;
  flex-direction: column;
  gap: 58rpx;
  width: 100%;
  height: 100%;

  .container {
    flex: 1;
    width: 100%;
    box-sizing: border-box;
    padding: 0 24rpx;
    position: relative;

    .header {
      width: 100%;
      box-sizing: border-box;
      padding-left: 32rpx;
      padding-right: 8rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .label {
        font-weight: 500;
        font-size: 32rpx;
        color: #1D2129;
      }

      .payment-wrapper {
        display: flex;
        align-items: center;
        gap: 16rpx;
        font-size: 32rpx;
        line-height: 48rpx;
        color: #3D3D3D;

        .icon {
          width: 42rpx;
          height: 42rpx;
        }
      }
    }

    .content {
      width: 100%;
      height: 278rpx;
      background: #F7F8FA;
      border-radius: 24rpx;
      box-sizing: border-box;
      padding: 32rpx;
      margin-top: 34rpx;

      .label {
        font-weight: 500;
        font-size: 32rpx;
        color: #1D2129;
        margin-bottom: 36rpx;
      }

      .input-wrapper {
        width: 100%;
        display: flex;
        align-items: flex-end;
        height: 64rpx;

        .input-prefix {
          font-weight: 500;
          font-size: 48rpx;
          line-height: 48rpx;
          color: #1F1F1F;
        }

        .input-value {
          font-weight: 500;
          font-size: 64rpx;
          color: #1F1F1F;
          height: 100%;
        }
      }

      .tip {
        margin-top: 24rpx;
        padding-top: 24rpx;
        font-size: 28rpx;
        color: #85878D;
        border-top: 2rpx solid #D8D8D8;
      }
    }

    .footer {
      margin-top: 56rpx;
      width: 100%;
      height: 96rpx;
      background: #FE58B7;
      border-radius: 48rpx;
      color: #fff;
      line-height: 96rpx;
      text-align: center;
    }

    .quesion-documentation {
      position: absolute;
      bottom: 68rpx;
      left: 0;
      width: 100%;
      text-align: center;
      color: #5E94CE;
      font-weight: 500;
      font-size: 32rpx;
    }
  }
}
</style>