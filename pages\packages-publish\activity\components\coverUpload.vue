<template>
	<view class="container">
		<div class="aspect-content">
			<view class="upload" v-if="props.url" @tap="previewHandle(props.url)">
				<image class="image" :src="props.url" :mode="props.mode"></image>
				<!-- 删除按钮 -->
				<image class="delete" src="@/pages/packages-publish/static/<EMAIL>"
					@tap.stop="deleteItem" />
			</view>
			
			<view class="upload" @tap="uploadHandle">
				<view class="icon-uplaod-container">
					<view class="icon-uplaod" />
					<view class="icon-uplaod" />
				</view>
				<text>上传封面</text>
			</view>
		</div>
	</view>
</template>

<script lang="ts" setup>
import { ref, PropType, computed, watch } from "vue";
import { uploadFile } from '@/api/request';
import { chooseMedia, Resource } from "@/components/image-upload/utils.ts";

interface Props {
	url?: string;
	mode?: "aspectFill" | "aspectFit"; // 预览显示样式
};
const props = withDefaults(defineProps<Props>(), {
	mode: "aspectFill",
});

interface Emits {
	(e: "uploadSucess", value: string): void;
	(e: "delete"): void;
}

const emits = defineEmits<Emits>();
// 删除图片
const deleteItem = function () {
	emits("delete");
}

let timer: any = 0
const uploadHandle = function () {
	timer && clearTimeout(timer);
	timer = setTimeout(() => {
		chooseMedia(false, 1, "image").then(uploadRequest).catch(console.log);
	}, 100)
};

const uploadRequest = function (data: Resource[]) {
	if (!data || !data.length) return;
	// 超过个数不能上传
	const limitSize = 10 * 1024 * 1024; // 服务器单张最大上传为10M
	const limitSource = data.filter(item => item.size > limitSize);
	if (limitSource.length) return uni.showToast({
		title: "大小超过了10M",
		icon: "error"
	});

	// 多资源上传处理
	// 根据uploadReponse中上传的url是否为空判断是否上传成功
	const uploadReponses: Array<{url: string, errMsg?: string}> = [];
	const multipleRequest: Promise<any>[] = [];

	data.forEach((item, index) => {
		uploadReponses.push({url: ""});
		const request = uploadFile<{ completeUrl: string }>(item.url).then(res => {
			const isLoadSucessed = res.code === 20000;
			const response = uploadReponses[index];
			if (!isLoadSucessed) return (response.errMsg = res.msg);
			response.url = res.data.completeUrl;
		});
		multipleRequest.push(request);
	});

	// 上传成功后续处理
	Promise.all(multipleRequest).finally(() => {
		// 是否全部上传完成
		console.log("图片上传结果：", uploadReponses);
		const sucessResource = uploadReponses.filter(item => item.url);
		if (!sucessResource.length) return uni.showToast({
			icon: "error",
			title: `图片上传失败`
		});
		emits("uploadSucess", sucessResource[0].url);
	});
};

const previewHandle = function (url: string) {
	uni.previewImage({
		urls: [url],
		fail() {
			uni.showToast({
				icon: "error",
				title: "预览图片失败"
			})
		},
	});
}

</script>

<style lang="scss" scoped>
.container {
	width: 100%;
	height: 0;
	padding-bottom: 55.555%;
	position: relative;
	
	.aspect-content {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 16rpx;
		background-color: rgba(247, 248, 250, 1);
		overflow: hidden;
		box-sizing: border-box;
		
	}
	
	.delete {
		position: absolute;
		top: 0;
		right: 0;
		width: 44rpx;
		height: 44rpx;
	}
	
	.image {
		display: block;
		width: 100%;
		height: 100%;
	}

	.upload {
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		gap: 16rpx;

		font-size: 28rpx;
		color: rgba(61, 61, 61);
	}

	.icon-uplaod-container {
		position: relative;
		height: 48rpx;
		width: 48rpx;

		.icon-uplaod:first-child {
			position: absolute;
			top: 50%;
			left: 0;
			width: 100%;
			height: 3px;
			background-color: rgba(133, 135, 141);
			border-radius: 3px;
			transform: translate(0, -50%);
		}

		.icon-uplaod:last-child {
			position: absolute;
			top: 50%;
			left: 0%;
			width: 100%;
			height: 3px;
			background-color: rgba(133, 135, 141);
			border-radius: 3px;
			transform: translate(0, -50%) rotate(90deg);
		}

	}

	
}
</style>