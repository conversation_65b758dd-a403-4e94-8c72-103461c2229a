<template>
	<view class="container" :style="props.customStyle">
		<view class="placeholder-container" v-if="!model && !focus">
			<slot name="serch">
				<image class="image" src="/pages/packages-publish/static/<EMAIL>" mode="aspectFill" />
			</slot>
			<text>{{props.placeholder}}</text>
		</view>
		<input class="input" type="text" v-model="model" @blur="focus = false;" @focus="focus = true;"/>
		<slot name="clear" v-if="props.showClear" >
			<view class="clear" v-if="model" @click="model = ''">
				<image  class="image" src="/static/icon/publish/<EMAIL>" mode="aspectFill" />
			</view>
		</slot>
	</view>
</template>

<script setup lang="ts">
	import { ref } from "vue";
	const props = defineProps({
		placeholder: {
			type: String,
			default: "搜索"
		},
		
		showClear: {
			type: <PERSON><PERSON>an,
			default: true
		},
		
		customStyle: {
			type: String
		}
	});
	const focus = ref(false);
	const emits = defineEmits(["click"]);
	const model = defineModel<string>();
</script>

<style lang="scss">
	.container {
		position: relative;
		display: flex;
		justify-content: stretch;
		align-items: center;
		gap: 16rpx;

		min-height: 72rpx;
		width: 100%;
		border: 4rpx solid rgba(29, 33, 41);
		border-radius: 16rpx;
		font-size: 28rpx;
		color: rgba(31, 31, 31);
		box-sizing: border-box;
		padding: 0 24rpx;
		
		.placeholder-container {
			position: absolute;
			display: flex;
			top: 0;
			left: 0;
			justify-content: flex-start;
			align-items: center;
			gap: 16rpx;
			height: 100%;
			width: 100%;
			padding: 0 24rpx;
			
		}
		.clear {
			padding: 10rpx;
			margin-right: -10rpx;
		}
		
		.image {
			flex: 0 0 auto;
			height: 32rpx;
			width: 32rpx;
		}
	
		.input {
			flex: 1 1 auto;
		}
		
	}
</style>