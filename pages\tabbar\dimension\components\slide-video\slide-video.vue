<template>
  <view class="wrapper">
    <swiper class="swiper-wrapper" vertical @change="onSwiperChange" :current="currentSwiperIndex" ref="swiperRef">
      <swiper-item v-for="(item, index) in displayList" :key="item.src">
        <view class='video-wrapper'>
          <slide-image v-if="item.type === 2" :resources="item?.resources" :cover="item?.cover"></slide-image>

          <!-- 目前还没支持上传视频 -->
          <!-- <video v-if="item.type === 1" loop :id="'dust-video-' + index" :src="prefixFilePath(item?.cover)" object-fit="contain" :controls="false"
            :enable-progress-gesture="false" :show-loading="false" :show-play-btn="false" :show-center-play-btn="false"
            :show-fullscreen-btn="false" :vslide-gesture-in-fullscreen="false" :show-progress="false"
            @play="() => onPlaying(index)" @tap="() => onVideoClick(index)"></video> -->
          <view class="video-action-wrapper">
            <view class="avatar-wrapper">
              <image :src="prefixFilePath(item?.user?.avatar)" @tap="toUserInfoPage(item?.user)"></image>
            </view>
            <!-- <view class="action-wrapper">
              <image src="/static/icon/dimension/icon_dz.png"></image>
              <text>100</text>
            </view>
            <view class="action-wrapper" @tap="() => openPopup(1)">
              <image src="/static/icon/dimension/icon_pl.png"></image>
              <text>100</text>
            </view>
            <view class="action-wrapper">
              <image src="/static/icon/dimension/icon_sc.png"></image>
              <text>100</text>
            </view>
            <view class="action-wrapper">
              <image src="/static/icon/dimension/icon_fx.png"></image>
              <text>100</text>
            </view>
            <view class="action-wrapper" @tap="() => openPopup(2)">
              <image class="more-icon" src="/static/icon/dimension/icon_gd.png"></image>
            </view> -->
          </view>
          <view class="video-info-wrapper" @tap="() => openPopup(0)">
            <view class="author">{{ item?.user?.nickname || item?.user?.username || '用户' + (item?.user?.account || item?.user?.uid || item?.user?.id) }}</view>
            <view class="description">{{ item?.description || '' }}</view>
          </view>
        </view>
      </swiper-item>
    </swiper>
    <!-- <view class="mask-pause-wrapper" v-if="!videoState.isPlaying" @tap="() => onPlayVideo(videoState.currentIdx)">
      <image class="mask-pause-icon" src="/static/icon/dimension/icon_sc.png"></image>
    </view> -->
    <view class="mask-notification-wrapper">
      <slot name="notification"></slot>
    </view>
  </view>
  <uv-popup ref="popup" mode="bottom" bgColor="none">
    <video-detail :info="displayList[currentIndex]" v-if="popupType === 0"></video-detail>
    <video-comment v-if="popupType === 1" @close="closePopup"></video-comment>
    <video-more v-if="popupType === 2" text="举报" @close="closePopup"></video-more>
  </uv-popup>
</template>

<script setup>
import { getCurrentInstance, ref, onMounted, computed, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import VideoDetail from '../video-detail/video-detail.vue';
import VideoMore from '../video-more/video-more.vue';
import VideoComment from '../video-comment/video-comment.vue';
import SlideImage from './slide-image.vue';

const store = useStore()
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

function prefixFilePath(url) {
  if (!url) return '';
  return cloudFileUrl.value + url;
}


const VIDEO_PREFIX = 'dust-video-'

const instance = getCurrentInstance()
const props = defineProps({
  list: {
    type: Array,
    default: () => {
      return []
    }
  },
})

const emit = defineEmits(['loadMore'])

const popup = ref(null)
const swiperRef = ref(null)
const currentSwiperIndex = ref(0) // 控制swiper当前显示的索引

/*
* 0 -> video-detail
* 1 -> video-comment
* 2 -> video-report
* 3 -> video-delete-comment
*/
const popupType = ref(null)

const openPopup = (type) => {
  popup.value.open()
  popupType.value = type
}

const closePopup = () => {
  popup.value.close()
  popupType.value = null
}

const videoState = ref({
  currentIdx: 0,
  isPlaying: false
})

const displayList = computed(() => {
  return props.list
})

const onPlaying = (idx) => {
  videoState.value.currentIdx = idx
}

const currentIndex = ref(0)

// 监听数据变化，重置swiper位置
watch(() => props.list.length, (newLength, oldLength) => {
  if (newLength !== oldLength && newLength > 0) {
    resetSwiperToFirst()
  }
}, { immediate: false })

// 重置swiper到第一项
const resetSwiperToFirst = async () => {
  currentSwiperIndex.value = 0
  currentIndex.value = 0
  videoState.value.currentIdx = 0
  
  await nextTick()
}

const onSwiperChange = (e) => {
  const currentIdx = e.detail.current
  currentIndex.value = currentIdx
  currentSwiperIndex.value = currentIdx
  
  if (currentIdx === 2 && videoState.value.currentIdx < currentIdx) {
    emit('loadMore')
  }

  // 暂停上一个视频
  if (videoState.value.currentIdx !== currentIdx) {
    onPauseVideo(videoState.value.currentIdx)
  }

  onPlayVideo(currentIdx)
}

const onPlayVideo = (idx) => {
  const videoCtx = uni.createVideoContext(VIDEO_PREFIX + idx, instance)
  videoCtx.play()
  videoState.value.isPlaying = true
}

const onPauseVideo = (idx) => {
  const videoCtx = uni.createVideoContext(VIDEO_PREFIX + idx, instance)
  videoCtx.pause()
  videoState.value.isPlaying = false
}

const onVideoClick = (idx) => {
  if (videoState.value.isPlaying) {
    onPauseVideo(idx)
  } else {
    onPlayVideo(idx)
  }
}

function toUserInfoPage(user) {
	if (user.id) {
		uni.navigateTo({
			url: '/pages/home-pages/user-mine?id=' + user.id,
		})
	}
}

onMounted(() => {
  // 默认第一个自动播放
  // onPlayVideo(0)
})
</script>

<style scoped lang='scss'>
.wrapper {
  width: 100%;
  height: 100%;
  background-color: #000;
  position: relative;

  .swiper-wrapper {
    width: 100%;
    height: 100%;

    .video-wrapper {
      max-width: 100%;
      height: 100%;
      position: relative;

      video {
        width: 100%;
        height: 100%;
      }

      .video-cover {
        width: 100%;
        height: 100%;
      }

      .video-action-wrapper {
        position: absolute;
        right: 12rpx;
        bottom: 82rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 24rpx;

        .avatar-wrapper {
          width: 96rpx;
          height: 96rpx;
          border-radius: 50%;
          background-color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;

          image {
            width: 92rpx;
            height: 92rpx;
            border-radius: 50%;
          }
        }

        .action-wrapper {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #fff;

          image {
            width: 80rpx;
            height: 80rpx;
          }

          .more-icon {
            width: 80rpx;
            height: 40rpx;
          }
        }
      }

      .video-info-wrapper {
        position: absolute;
        left: 32rpx;
        bottom: 55rpx;
        color: #fff;
        width: 65%;
        // background-color: rgba(0, 0, 0, 0.5);
        padding: 24rpx;
        box-sizing: border-box;

        .author {
          font-weight: 600;
          font-size: 32rpx;
          line-height: 32rpx;
        }

        .description {
          margin-top: 12rpx;
          font-weight: 400;
          font-size: 28rpx;
          line-height: 42rpx;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          line-clamp: 2;
          -webkit-line-clamp: 2;
          position: relative;

          &::after {
            content: "展示";
            position: absolute;
            right: 0;
            bottom: 0;
            padding-left: 8px;
          }
        }
      }
    }
  }

  .mask-pause-wrapper {
    width: 128rpx;
    height: 128rpx;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;

    .mask-pause-icon {
      width: 128rpx;
      height: 128rpx;
      opacity: 0.65;
    }
  }

  .mask-notification-wrapper {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 56rpx;
  }
}
</style>