<template>
	<uni-nav-bar class="uni-nav-bar uni-nav-bar-set" :fixed="true" :backgroundColor="backgroundColor" :border="false"
		:statusBar="true" :leftWidth="leftWidth" :rightWidth="rightWidth">
		<template #left>
			<image v-if="type === 0" class="main-logo" src="/static/logo.png" mode="widthFix" />
			<view v-if="type === 1" class="back" @tap="onBack">
				<image class="icon-back" src="/static/icon/<EMAIL>" />
				<text v-if="leftText !== ''" class="back-text">{{ leftText }}</text>
			</view>
			<view v-if="type === 3" class="back user" @tap="onBack">
				<image class="icon-back" src="/static/icon/<EMAIL>" />
				<image class="user-img" :src="userImg" @tap.stop="onLeftTextClick"></image>
				<text v-if="leftText !== ''" class="back-text" @tap.stop="onLeftTextClick">{{ leftText }}</text>
			</view>
		</template>
		<view v-if="type === 4" class="flex justify-center">
			<text v-if="leftText !== ''" class="back-text">{{ leftText }}</text>
		</view>
		<template v-if="showSearch">
			<uni-search-bar v-if="searchTo === ''" class="flex-1" v-model="searchValue" radius="8"
				:placeholder="placeholder" :clearButton="clearButton" :cancelButton="cancelButton" bgColor="#fff"
				@confirm="search" @input="input" @cancel="cancel" @clear="clear" @focus="focus" @blur="blur">
				<template #searchIcon>
					<image class="icon-search" src="/static/icon/<EMAIL>" />
				</template>
			</uni-search-bar>
			<view v-else class="search-bar" @tap="toSearch">
				<view class="search-box">
					<image class="icon-search" src="/static/icon/<EMAIL>" />
					<text class="search-placeholder">{{ placeholder }}</text>
				</view>
			</view>
		</template>
	</uni-nav-bar>
</template>
<script setup>
import {
	ref
} from 'vue'

defineOptions({
	name: 'navbar',
})
 
const props = defineProps({
	// 自定义 nav 类型
	type: {
		type: Number,
		default: 0, // 0 首页 1 普通返回 2 返回带搜索 3 消息界面用户名 4 标题居中
	},
	// 背景颜色
	backgroundColor: {
		type: String,
		default: 'rgba(255,255,255,1)',
	},
	// 返回的页数
	goBackTier: {
		type: Number,
		default: 1, // 默认 1 返回上一页，如果要自定义返回，则 传 0
	},
	// 定义左边返回位置的宽度
	leftWidth: {
		type: String,
		default: '268rpx',
	},
	rightWidth: {
		type: String,
		default: undefined,
	},
	// 用户头像
	userImg: {
		type: String,
		default: '',
	},
	// 左边返回是否显示具体的文字
	leftText: {
		type: String,
		default: '',
	},
	// 是否显示搜索
	showSearch: {
		type: Boolean,
		default: false,
	},
	// 搜索占位符
	placeholder: {
		type: String,
		default: '',
	},
	// 显示清除按钮
	clearButton: {
		type: String,
		default: 'auto',
	},
	// 显示取消按钮
	cancelButton: {
		type: String,
		default: 'none',
	},
	searchTo: {
		type: String,
		default: '',
	}
})

const searchValue = ref('')

const emit = defineEmits(['onBack', 'confirm', 'input', 'cancel', 'clear', 'focus', 'blur'])

function onBack() {
	// 返回的页数
	if (props.goBackTier !== 0) {
		uni.navigateBack({
			delta: props.goBackTier
		})
	} else {
		emit('onBack')
	}
}

function search(value) {
	emit('confirm', value)
}

function input(value) {
	emit('input', value)
}

function cancel(value) {
	emit('cancel', value)
}

function clear(value) {
	emit('clear', value)
}

function focus() {
	emit('focus')
}

function blur(value) {
	emit('blur', value)
}

function toSearch() {
	if (props.searchTo) {
		uni.navigateTo({
			url: props.searchTo,
		})
	}
}
</script>

<style lang="scss" scoped>
.uni-nav-bar {
	// flex: 1;
}

.main-logo {
	width: 268rpx;
	height: 82rpx;
}

.icon-search {
	width: 32rpx;
	height: 32rpx;
}

.back {
	display: flex;
	align-items: center;
}

.icon-back {
	width: 44rpx;
	height: 46rpx;
}

.back-text {
	flex: 1;
	color: #1F1F1F;
	font-size: 36rpx;
	font-weight: 700;
	margin-left: 12rpx;
}

.user-img {
	width: 56rpx;
	height: 56rpx;
	border-radius: 50%;
	margin-left: 20rpx;
	margin-right: 12rpx;
}

.search-bar {
	flex: 1;
	display: flex;
	flex-direction: row;
	position: relative;
}

.search-box {
	display: flex;
	box-sizing: border-box;
	justify-content: left;
	overflow: hidden;
	position: relative;
	flex: 1;
	flex-direction: row;
	align-items: center;
	border: 4rpx solid #000;
	height: 64rpx;
	padding: 0 10rpx;
	border-radius: 8px;
	background-color: #fff;
}

.search-placeholder {
	font-size: 26rpx;
	color: #b3b3b3;
	margin-left: 5px;
	text-align: left;
}
</style>