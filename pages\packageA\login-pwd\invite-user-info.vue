<template>
	<view v-if="inviteUserInfo.id" class="wrapper">
		<view class="bg-wrap">
			<image :style="`width: ${sysInfo.screenWidth}px;`" :src="prefixFilePath(inviteUserInfo.background_url)"
				mode="widthFix"></image>
			<view class="matter-view" />
			<view class="info-wrap">
				<view class="avatar-wrap">
					<image class="avatar" :src="prefixFilePath(inviteUserInfo.avatar)" mode="widthFix"></image>
				</view>
				<view class="name-wrap">
					<text class="name-text">{{inviteUserInfo.nickname}}</text>
					<image v-if="inviteUserInfo.gender === 1" class="gender-icon" src="/static/my/<EMAIL>" />
					<image v-if="inviteUserInfo.gender === 2" class="gender-icon" src="/static/my/<EMAIL>" />
				</view>
				<view class="uid-wrap">
					<text class="uid">UID：{{inviteUserInfo.account}}</text>
				</view>
				<view class="addr-wrap" v-if="!!inviteUserInfo.city">
					<view class="addr">
						<text>{{ inviteUserInfo.city.name }}·{{ inviteUserInfo.district ? inviteUserInfo.district.name : '' }}</text>
					</view>
				</view>
				<view class="bio">
					<text>{{inviteUserInfo.bio}}</text>
				</view>
			</view>
		</view>
		<view class="content px-40">
			<button class="btn-submit btn-fen" @tap="handleFollow">关注Ta并进入主页</button>
			<button class="btn-submit btn-black mt-30" @tap="handleToHome">直接进入主页</button>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
	} from 'vue';
	import {
		useStore
	} from 'vuex';
	import {
		onLoad,
		onBackPress
	} from '@dcloudio/uni-app';
	import {
		getInviteUserInfo,
		addFollow,
	} from '@/api/user';
	const store = useStore();
	const sysInfo = computed(() => store.state.common.sysInfo);
	const commonStore = computed(() => store.state.common);
	const inviteCode = ref('')
	const inviteUserInfo = ref('')

	onLoad((option) => {
		console.log('onLoad:', option);
		if (option.inviteCode) {
			inviteCode.value = option.inviteCode
		} else {
			console.error('inviteCode 邀请码必传');
		}
		console.log('sysInfo:', sysInfo.value)
		getData()
	});

	function getData() {
		getInviteUserInfo(inviteCode.value).then(res => {
			console.log('getInviteUserInfo res:', res)
			if (res.code === 20000) {
				inviteUserInfo.value = res.data
			}
		})
	}

	function prefixFilePath(url) {
		if (!url) return '';
		return commonStore.value.cloudFileUrl + url;
	}
	
	function handleFollow() {
		addFollow(inviteUserInfo.value.id).then(res => {
			if (res.code === 20000) {
				uni.reLaunch({
					url: '/pages/tabbar/mine/mine'
				})
			}
		})
	}
	
	function handleToHome() {
		uni.reLaunch({
			url: '/pages/tabbar/mine/mine'
		})
	}
</script>

<style lang="scss">
	.wrapper {
		display: flex;
		flex-direction: column;
		min-height: 100%;
	}

	.bg-wrap {
		position: fixed;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: 0;
	}
	
	.matter {
		
	}
	
	.info-wrap {
		position: absolute;
		left: 0;
		right: 0;
		top: 248rpx;
		z-index: 2;
	}
	
	.avatar-wrap {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.avatar {
		background-color: #fff;
		border-radius: 176rpx;
		border-width: 4rpx;
		border-color: rgba(255, 255, 255, 0.5);
		width: 176rpx;
		height: 176rpx;
	}

	.name-wrap {
		margin-top: 44rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.name-text {
		color: #1F1F1F;
		font-size: 48rpx;
		font-weight: 700;
		margin-right: 18rpx;
	}
	
	.gender-icon {
		width: 48rpx;
		height: 48rpx;
	}
	
	.uid-wrap {
		margin-top: 16rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.uid {
		color: rgba(31, 31, 31, 0.7);
		font-size: 28rpx;
	}
	
	.addr-wrap {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 26rpx;
	}
	
	.addr {
		border-radius: 48rpx;
		padding: 12rpx 16rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 6rpx 12rpx 0rpx rgba(0,0,0,0.12);
		border: 1rpx solid #FFFFFF;
		color: #4E5969;
		font-size: 24rpx;
	}
	
	.bio {
		color: rgba(31, 31, 31, 0.7);
		font-size: 32rpx;
		line-height: 48rpx;
		padding-left: 48rpx;
		padding-right: 48rpx;
		margin-top: 38rpx;
	}

	.content {
		background-color: #fff;
		border-top-left-radius: 32rpx;
		border-top-right-radius: 32rpx;
		margin-top: 936rpx;
		flex: 1;
		position: relative;
		z-index: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
	}

	.btn-submit {
		margin-left: 0;
		margin-right: 0;
	}

	.btn-fen,
	.btn-black {
		border-radius: 96rpx;
		color: #fff;
		font-size: 36rpx;
		height: 96rpx;
		line-height: 96rpx;
		align-items: center;
	}

	.btn-fen {
		background-color: #fe58b7 !important;
	}

	.btn-black {
		background-color: #1f1f1f !important;
	}
</style>