<template>
	<view class="chat-wrapper">
		<view class="top">
			<Navbar
				:type="3"
				leftWidth="500rpx"
				:leftText="orderInfo?.[type === '2' ? 'user' : 'vendor']?.nickname || ''"
				:userImg="prefixFilePath(orderInfo?.[type === '2' ? 'user' : 'vendor']?.avatar)"
				:onLeftTextClick="() => toUser(orderInfo?.[type === '2' ? 'user' : 'vendor']?.id)"
			></Navbar>
			<view class="topDetails allPidding">
				<view class="imageView">
					<dust-image-base :src="orderInfo.service_item.cover" :local="false" aspect-ratio="1:1"></dust-image-base>
				</view>
				<view class="topDetailsBox">
					<text class="txtellipsis">{{ orderInfo.service_item.name }}</text>
					<text>商家保证金{{ orderInfo.service_item.bond }}% </text>
					<view class="priceText">
						<text class="price">￥</text>
						<text>{{ orderInfo.service_item.price / 100 }}</text>
					</view>
					<view class="btn" v-if="!orderCreate & (type == 1)" @click="toPut">
						<text>更换项目</text>
					</view>
					<view class="btn" v-if="!orderCreate & (type == 2)" @click="toCreate">
						<text>创建订单</text>
					</view>
					<view class="btn"
						v-if="orderCreate & (type == 1) & (orderInfo.order?.status == 1) || orderCreate & (type == 1) & (orderInfo.order?.status == 256)"
						@click="topay">
						<text>立即支付</text>
					</view>
					<view class="btn"
						v-if="orderCreate & (orderInfo.order?.status != 1 && orderInfo.order?.status != 256) & (type == 1)"
						@click="toOrderInfo">
						<text>查看订单</text>
					</view>
					<view class="btn" v-if="orderCreate & (type == 2)" @click="toOrderInfoSell">
						<text>查看订单</text>
					</view>
				</view>
			</view>
			<view class="tips allPidding">
				<view class="tipsBox">
					<image src="../../../static/icon/<EMAIL>" mode=""></image>
					{{ tips }}
				</view>
			</view>
		</view>

		<scroll-view class="message-view" scroll-y scroll-with-animation :scroll-into-view="scollId">
			<view>
				<view class="message-block" :class="{ 'is-myself': isMyself(item.from_id) }" v-for="(item, idx) in messageList" :key="idx">
					<image v-if="!isMyself(item.from_id)" class="avatar" :src="prefixFilePath(formatAvatarAndName(item.from_id)?.avatar)" @click="toUser(item.from_id)"></image>
					<view class="content">
						<view class="name" @click="toUser(item.from_id)">{{ formatAvatarAndName(item.from_id)?.nickname }}</view>
						<view class="message" v-if="item.msg_type === 'text'">{{ item.message }}</view>
						<image class="message-image" v-if="item.msg_type === 'file'" :src="prefixFilePath(item.message)"
							@click="() => previewImage(prefixFilePath(item.message))" />
					</view>
					<image v-if="isMyself(item.from_id)" class="avatar"  @click="toUser(item.from_id)"
						:src="prefixFilePath(formatAvatarAndName(item.from_id)?.avatar)"></image>
				</view>
			</view>
			<view id="bottom-view"></view>
		</scroll-view>

		<view class="footer allPidding bottom" :style="{ paddingBottom: keyboardHeight + 'px' }">
			<bottom-comment-input @keyboardHeightChange="onKeyboardHeightChange" @onSubmit="onSubmit" />
		</view>
	</view>
</template>

<script setup>
import { getConsultInfo } from '../../../api/notification';
import { getMineBalance } from '../../../api/wallet';
import { payOrder } from '../../../api/order';
import { ref, computed, nextTick } from 'vue';
import { useStore } from 'vuex';
import { onLoad, onShow } from '@dcloudio/uni-app';
import { useChat } from './hooks/useChat';

const type = ref(null);
const orderCreate = ref(false);
const tips = ref(null);
let pageType = ref(null);
let balance = ref(0);
let orderInfo = ref({
	service_item: {
		cover: ''
	}
});
const consultid = ref(null);
const store = useStore();
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

const richtextPopup = ref(null);
const scollId = ref('');

const { messageList, keyboardHeight, onKeyboardHeightChange, isMyself, formatAvatarAndName, onSubmit, previewImage } = useChat({
	orderInfo,
	type,
	richtextPopup
});

function prefixFilePath(url) {
	if (!url) return '';
	return cloudFileUrl.value + url;
}
onLoad(async (e) => {
	type.value = e.type;
	consultid.value = e.consultid;
	pageType.value = e.pageType;
	setTimeout(() => {
		scrollToBottom();
	}, 300)
});

uni.$on('orderNotificationDetail:scrollToBottom', () => {
	scrollToBottom();
});

uni.$on('orderNotificationDetail:refreshOrderInfo', (order) => {
	orderInfo.value.order = order;
	isorderCreate();
	valueInit();
});

function scrollToBottom() {
	if (scollId.value) {
		scollId.value = '';
	}
	nextTick(() => {
		scollId.value = 'bottom-view';
	});
}

onShow(() => {
	valueInit();
	getInfo();
	MineBalance();
});
function MineBalance() {
	getMineBalance().then((res) => {
		balance.value = res.data.balance - res.data.freeze;
		console.log(balance.value);
	});
}
function isorderCreate() {
	if (
		(orderInfo.value.order.number && orderInfo.value.order.number != '')
		&&
		!([4, 5, 6, 7, 8].includes(orderInfo.value.order.status))
	) {
		orderCreate.value = true;
	}
}
function toPut() {
	uni.navigateTo({
		url: `/pages/packageA/photographyDetails/photographyDetails?id=${orderInfo.value.service_id}&pageType=notification`
	});
}
function valueInit() {
	if (!orderCreate.value) {
		if (type.value == 1) {
			tips.value = '您可以向商家进行咨询确认价格、时间、地点等相关事宜。双方确认后，由商家提交订单，您再进行订单支付。为保障服务质量与你的合法权益，会话可能会被记录。';
		} else {
			tips.value = `${orderInfo.value?.user?.nickname || ''}#向你发起了服务项目的咨询，为保障服务质量与你的合法权益，会话可能会被记录。`;
		}
	} else {
		tips.value = `订单编号：${orderInfo.value.order.number}   订单状态：${typeSwitch(orderInfo.value.order.status)}`;
	}
}
function toCreate() {
	uni.navigateTo({
		url: `/pages/tabbar/notification/createOrders?consult_id=${consultid.value}&service_item_id=${orderInfo.value.service_item.id}&user_id=${orderInfo.value.user.id}&amount=${orderInfo.value.service_item.price}&bond=${orderInfo.value.service_item.bond}&name=${orderInfo.value.service_item.name}`
	});
}
function typeSwitch(type) {
	let txt = '';
	switch (type) {
		case 1:
			txt = '待支付';
			break;
		case 2:
			txt = '待完成';
			break;
		case 4:
			txt = '已使用';
			break;
		case 8:
			txt = '已完成';
			break;
		case 16:
			txt = '已取消';
			break;
		case 32:
			txt = '已弃用';
			break;
		case 64:
			txt = '用户违约';
			break;
		case 128:
			txt = '商家违约';
			break;
		case 256:
			txt = '支付中';
			break;
		default:
			break;
	}
	return txt;
}
const openRichtextPopup = () => {
	richtextPopup.value.open();
};
//获取咨询项目内容
function getInfo() {
	// let role = type.value == 1 ? 'user' : 'vendor';
	getConsultInfo(consultid.value).then((res) => {
		orderInfo.value = res.data;
		isorderCreate();
		valueInit();
	});
}
//跳转用户订单详情
function toOrderInfo() {
	uni.navigateTo({
		url: `/pages/packageA/orderDetails/orderDetails?id=${orderInfo.value.order.id}&type=buy`
	});
}
//跳转商家服务订单详情
function toOrderInfoSell() {
	uni.navigateTo({
		url: `/pages/packageA/orderDetails/orderDetails?id=${orderInfo.value.order.id}&type=sell`
	});
}
const getLoginCode = () => {
	return new Promise((resolve, reject) => {
		uni.login({
			success: (res) => {
				if (!res.code) {
					reject(res);
				}
				resolve(res.code);
			},
			fail: (err) => {
				reject(err);
			}
		});
	});
};
function topay() {
	uni.showModal({
		title: '提示',
		content: `是否支付订单金额${orderInfo.value.order.amount / 100}元`,
		success: async function (r) {
			if (r.confirm) {
				let d = {
					password: '8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92'
				};
				if (balance.value / 100 < orderInfo.value.order.amount / 100) {
					//平台支付
					d.pay_method = 2;
					const code = await getLoginCode();
					d.code = code;
					payOrder(orderInfo.value.order.id, d).then((res) => {
						uni.requestPayment({
							provider: 'wxpay',
							orderInfo: res.data.wx,
							timeStamp: res.data.wx.timeStamp,
							nonceStr: res.data.wx.nonceStr,
							package: res.data.wx.package,
							signType: res.data.wx.signType,
							paySign: res.data.wx.paySign,
							success: async (res) => {
								if (res.errMsg === 'requestPayment:ok') {
									uni.showToast({
										title: '支付成功',
										duration: 2000
									});
									valueInit();
									getInfo();
									MineBalance();
								}
							},
							fail: (err) => {
								console.log(err, 'fail');
							}
						});
					});
				} else {
					//余额支付
					d.pay_method = 1;
					payOrder(orderInfo.value.order.id, d).then((res) => {
						uni.showToast({
							title: '支付成功',
							duration: 2000
						});
						valueInit();
						getInfo();
						MineBalance();
					});
				}
			} else if (r.cancel) {
				console.log('用户点击取消');
			}
		}
	});
}
// 跳转用户主页
function toUser(userId) {
	uni.navigateTo({
		url: `/pages/home-pages/user-mine?id=${userId}`
	});
}
</script>

<style lang="scss" scoped>
.chat-wrapper {
	display: flex;
	flex-direction: column;
	height: 100vh;

	.bottom,
	.top {
		flex-shrink: 0;
	}

	.message-view {
		flex: 1;
	}
}

.allPidding {
	padding: 0 32rpx;
}

.tipsBox {
	margin-top: 32rpx;
	background-color: rgba(254, 30, 66, 0.12);
	border-radius: 24rpx;
	padding: 20rpx;

	font-size: 24rpx;
	color: #fe1e42;

	image {
		width: 25rpx;
		height: 25rpx;
		margin-right: 4rpx;
		margin-bottom: 4rpx;
	}
}

.topDetails {
	margin-top: 32rpx;
	display: flex;

	.imageView {
		width: 208rpx;
		height: 208rpx;
		border-radius: 20rpx;
		margin-right: 32rpx;
		overflow: hidden;
		background-color: #000;

		image {
			width: 208rpx;
			height: 208rpx;
		}
	}

	.topDetailsBox {
		flex: 1;
		position: relative;
		display: flex;
		flex-direction: column;

		text:nth-child(1) {
			font-weight: 700;
			font-size: 32rpx;
			color: #3d3d3d;
		}

		text:nth-child(2) {
			margin-top: 16rpx;
			font-size: 28rpx;
			color: #ffce59;
		}

		.priceText {
			margin-top: 22rpx;

			text {
				font-weight: 700;
				font-size: 36rpx;
				color: #fe58b7 !important;
				margin-top: 0;
			}

			.price {
				font-size: 24rpx;
				color: #fe58b7;
			}
		}

		.btn {
			width: 144rpx;
			height: 56rpx;
			background: #fe58b7;
			border-radius: 48rpx;
			position: absolute;
			right: 0;
			bottom: 0;
			display: flex;
			justify-content: center;
			align-items: center;

			text {
				font-size: 24rpx !important;
				color: #ffffff;
			}
		}
	}
}

.footer {
	box-sizing: border-box;
	// padding: 24rpx 24rpx 68rpx;
	padding: 0;
	width: 100%;
	border-top: 2rpx solid #f4f4f4;
	display: flex;
	align-items: center;
	justify-content: center;

	.comment-wrapper {
		width: 100%;
		height: 88rpx;
		box-sizing: border-box;
		background: #f3f3f3;
		border-radius: 40rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-left: 32rpx;
		padding-right: 40rpx;

		.placeholder {
			font-weight: 400;
			font-size: 32rpx;
			color: #85878d;
		}

		.icon-wrapper {
			display: flex;
			align-items: center;
			gap: 28rpx;

			.icon {
				width: 56rpx;
				height: 56rpx;
			}
		}
	}
}

.mention-container {
	height: fit-content;
	background: #ffffff;
	border-radius: 32rpx 32rpx 0rpx 0rpx;
}

.message-view {
	width: 100%;
	flex: 1;
	box-sizing: border-box;
	padding: 24rpx;
	overflow-y: auto;

	.message-block {
		margin-top: 16rpx;
		width: 100%;
		display: flex;
		gap: 16rpx;
		align-items: flex-start;

		&.is-myself {
			.content {
				align-items: flex-end;
			}
		}

		.avatar {
			width: 56rpx;
			height: 56rpx;
			border-radius: 50%;
			background-color: #eee;
		}

		.content {
			flex: 1;
			display: flex;
			flex-direction: column;
			gap: 8rpx;

			.name {
				width: fit-content;
				font-size: 24rpx;
				color: #aaa;
			}

			.message {
				width: fit-content;
				font-size: 32rpx;
				color: #1f1f1f;
				padding: 16rpx;
				border-radius: 16rpx;
				background-color: #f7f8fa;
				white-space: pre-wrap;
				word-break: break-word;
			}

			.message-image {
				width: 128rpx;
				height: 128rpx;
				border-radius: 16rpx;
			}
		}
	}
}
</style>
