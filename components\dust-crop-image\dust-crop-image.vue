<template>
  <view class="dust-crop-image">
    <view @click="openCropper">
      <slot name="trigger"></slot>
    </view>
    <qf-image-cropper ref="cropperRef" v-show="visible" rotatable :width="width" :zIndex="999" :height="height" :radius="radius"
      @crop="handleCrop">
    </qf-image-cropper>
  </view>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  width: {
    type: Number,
    default: 500
  },
  height: {
    type: Number,
    default: 500
  },
  radius: {
    type: Number,
    default: 0
  },
  sourceType: {
    type: Array,
    default: ['album', 'camera']
  }
});

const emit = defineEmits(['crop']);

const visible = ref(false);
const cropperRef = ref(null);

function openCropper() {
  visible.value = true;
  cropperRef.value.chooseImage({ sourceType: props.sourceType });
}

function handleCrop(result) {
  emit('crop', result);
  visible.value = false;
}

// 暴露方法给父组件调用
defineExpose({
  openCropper,
  closeCropper: handleCrop
});
</script>

<style lang="scss" scoped>
.dust-crop-image {
  width: 100%;
}
</style>
