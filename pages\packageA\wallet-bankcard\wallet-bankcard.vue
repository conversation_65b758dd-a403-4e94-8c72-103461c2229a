<template>
  <z-paging class="app-container" ref="paging" :fixed="false">
    <template #top>
      <Navbar :type="1" leftText="银行卡"></Navbar>
    </template>

    <view class="wrapper">
      <view class="empty-card-wrapper" v-if="!hadBankcard">
        <view class="empty-card-container">
          <image class="icon" src="../static/service/<EMAIL>"></image>
          <text>您还没绑定银行卡</text>
        </view>
        <view class="btn" @click="() => handleTo('bind-card')">立即绑卡</view>
      </view>

      <view class="card-list-wrapper" v-else>
        <view class="add-card-item" @click="() => handleTo('bind-card')">
          <image class="icon" src="../static/service/<EMAIL>"></image>
          <text>添加银行卡</text>
        </view>
        <view class="card-item" v-for="(item, index) in cardList" :key="index">
          <image class="image-bg"
            :src="index % 2 == 0 ? '../static/service/yebg-red.png' : '../static/service/yebg-green.png'">
          </image>
          <view class="header">
            <image class="more-action" src="../static/service/<EMAIL>" @click="() => openPopup(item.id)"></image>
            <image class="bank-logo" :src="bankTypeIcon(item.bank_name)">
            </image>
            <view class="info">
              <view class="title">{{ item.bank_name }}</view>
              <view class="desc">{{ bankTypeMapTitle[item.type] }}</view>
            </view>
          </view>
          <view class="card-id">{{ formatBankNo(item.card_no) }}</view>
        </view>
      </view>
    </view>

    <uv-popup ref="popup" mode="bottom" bgColor="none">
      <view class="popup-wrapper">
        <view class="action" @click="unbindCard">解除绑定</view>
        <view class="divider"></view>
        <view class="action" @click="closePopup">取消</view>
      </view>
    </uv-popup>
  </z-paging>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app';
import bankTypeData from './bankType.json'
import { getBankCard, unbindBankCard } from '@/api/wallet'

const hadBankcard = ref(false)
const popup = ref(null)
const selectionId = ref(null)
const cardList = ref([])
const bankTypeMapTitle = {
  'DC': '储蓄卡',
  'CC': '信用卡',
  'SCC': '准贷记卡',
  'PC': '预付费卡'
}

onLoad((option) => {
  hadBankcard.value = (option.notBind !== 'true')
  if ((option.notBind !== 'true')) {
    queryBankCardList()
  }
})

const formatBankNo = (bankNo) => {
  if (!bankNo) return ''
  return bankNo.replace(/.{4}/g, '$& ').trim()
}

const bankTypeIcon = (name) => {
  let icon = ''
  for (const key in bankTypeData) {
    if (bankTypeData[key] === name) {
      icon = key
      break
    }
  }
  if (icon) {
    return `https://mdn.alipayobjects.com/fin_instasset/uri/img/as/instasset/${icon}/BANK_LOGO/PURE?zoom=40w_40h.png`
  } else {
    return ''
  }
}

uni.$on('wallet-bankcard-reload-data', () => {
  queryBankCardList()
})

const openPopup = (id) => {
  popup.value.open()
  selectionId.value = id
}

const closePopup = () => {
  popup.value.close()
  selectionId.value = null
}

const unbindCard = async () => {
  try {
    const res = await unbindBankCard(selectionId.value)
    if (res.code === 20000) {
      queryBankCardList()
      uni.$emit('wallet-page-reload-data')
      closePopup()
    }
  } catch (error) {
    
  }
}

const handleTo = type => {
  let url = ``
  if (type === 'bind-card') {
    url = `/pages/packageA/wallet-bankcard/bind-card`
  }

  url && uni.navigateTo({
    url
  })
}

async function queryBankCardList() {
  try {
    const res = await getBankCard()
    if (res.code === 20000) {
      cardList.value = res.data
      if (res.data.length > 0) {
        hadBankcard.value = true
      } else {
        hadBankcard.value = false
      }
    }
  } catch (error) {
    console.log(error, 'getBankCard')
  }
}
</script>

<style scoped lang='scss'>
.wrapper {
  flex: 1;
  box-sizing: border-box;
  padding: 0 24rpx;

  .empty-card-wrapper {
    width: 100%;
    height: 80%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .empty-card-container {
      width: 380rpx;
      height: 400rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #1F1F1F;
      position: relative;

      .icon {
        width: 100%;
        height: 100%;
      }

      text {
        position: absolute;
        bottom: 0;
      }
    }
  }

  .card-list-wrapper {
    margin-top: 32rpx;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 24rpx;

    .add-card-item {
      width: calc(100% - 48rpx);
      margin: 0 auto;
      height: 108rpx;
      background-color: #F7F8FA;
      border: 16rpx;
      display: flex;
      align-items: center;
      gap: 24rpx;
      font-size: 32rpx;
      padding-left: 40rpx;
      .icon {
        width: 40rpx;
        height: 40rpx;
      }
    }

    .card-item {
      width: 100%;
      height: 240rpx;
      position: relative;
      border-radius: 16rpx;
      overflow: hidden;

      .image-bg {
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: -1;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
      }

      .header {
        box-sizing: border-box;
        padding-left: 40rpx;
        padding-top: 32rpx;
        width: 100%;
        display: flex;
        gap: 16rpx;
        align-items: center;
        position: relative;

        .more-action {
          position: absolute;
          right: 40rpx;
          top: 50%;
          transform: translateY(-50%);
          width: 32rpx;
          height: 32rpx;
        }

        .bank-logo {
          width: 64rpx;
          height: 64rpx;
          background-color: #fff;
          border-radius: 50%;
        }

        .info {
          font-size: 28rpx;
          color: #FFFFFF;

          .desc {
            font-size: 20rpx;
          }
        }
      }

      .card-id {
        box-sizing: border-box;
        padding-left: 40rpx;
        font-weight: 600;
        font-size: 48rpx;
        color: #FFFFFF;
        margin-top: 36rpx;
      }
    }
  }

  .btn {
    width: 100%;
    height: 96rpx;
    background: #FE58B7;
    border-radius: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36rpx;
    color: #FFFFFF;
    margin-top: 96rpx;
  }
}

.popup-wrapper {
  width: 100%;
  height: fit-content;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  box-sizing: border-box;

  .action {
    width: 100%;
    height: 96rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36rpx;
    color: #1F1F1F;
  }

  .divider {
    width: 100%;
    height: 2rpx;
    background-color: #eee;
  }
}
</style>