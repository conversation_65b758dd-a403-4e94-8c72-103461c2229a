<template>
	<view class="app-container">
		<Navbar :type="1" :leftText="detail.title" leftWidth="400rpx"></Navbar>
		<view class="content-scroll">
			<scroll-view class="scroll-view" :scroll-y="true">
				<view class="content">
					<mp-html :content="detail.content" />
				</view>
			</scroll-view>
		</view>
		<uv-safe-bottom></uv-safe-bottom>
	</view>
</template>

<script setup>
	import {
		ref,
	} from 'vue';
	import {
		onLoad,
	} from "@dcloudio/uni-app";
	// import {
	// 	getContractByType
	// } from '@/api/common'

	const detail = ref({
		title: '儿童/青少年个人信息保护规则',
		remark: '',
		content: `
		<div>
		儿童/青少年使用须知
		</div>
		<div>
		欢迎你选择由长沙次元星尘科技有限公司（以下简称“我们”或“平台”）开发、运营、提供的相关产品和服务！我们非常重视未成年人的保护。为保护未成年人个人信息的安全，并提供更加丰富、多元的优质内容，我们开发了“未成年人模式”（以下简称“未成年人模式”或“该模式”）。本《未成年人使用须知》旨在向法定监护人或未成年人的父母（以下简称“监护人”或“家长”）和未成年人（以下又称“被监护人”）说明该模式的运行方式，我们将挖掘更多的适合未成年人观看的优质内容，为监护人提供更丰富的管理手段，确保儿童的信息收集、使用在监护人的监督下进行。
		</div>
		<div>
		本须知同时适用于次元星尘及其关联产品。
		</div>
		<div>
		</div>
		<div>
		1.与你携手共同守护未成年人
		</div>
		<div>
		1.1 未满14周岁的儿童
		</div>
		<div>
		如果你的被监护人是未满14周岁的儿童，他/她使用次元星尘的普通模式，需要经过你的明确同意；如果你不同意他/她使用次元星尘的普通模式，请你为他/她设置未成年人模式。同时，平台将根据你或被监护人提供的年龄信息等情形，启用为未满14周岁的儿童自动进入未成年人模式的功能。
		</div>
		<div>
		在实名认证阶段，若你的被监护人需要完成实名认证，需要经过你的单独明确同意，我们会通过合理的核验措施验证你与你的被监护人的监护关系，若你认为我们不当地处理了你或你的被监护人的信息，请通过文末的联系方式及时联系我们。
		</div>
		<div>
		1.2 已满14周岁不满18周岁的青少年
		</div>
		<div>
		如果你的被监护人是已满14周岁不满18周岁的青少年，你和你的被监护人可以根据自主需要，选择是否设置“未成年人”模式。
		</div>
		<div>
		1.3 协助监护人更好地守护未成年人
		</div>
		<div>
		为帮助监护人管理未成年人的应用使用时间，在该模式下，每日22时至次日早6时期间无法使用次元星尘。
		</div>
		<div>
		进入该模式后，系统将自动开启时间锁，时间锁默认设置的单日使用时长为40分钟。单日使用时长按您在次元星尘使用时长计算。当单日使用时长超过设定时长时，各端均需要输入密码才可以继续使用。密码由你设置并掌握，确保你能有效引导未成年人健康使用次元星尘。
		</div>
		<div>
		你也可以进入“亲子平台”，根据实际需要为你的被监护人设置浏览使用时长。
		</div>
		<div>
		你进入“亲子平台”后，可以通过监护人账号和被监护人账号（在平台提供的部分功能和服务中，又称“家长账号”和“儿童账号”）的绑定，选择帮助自己的被监护人打开或关闭未成年人模式，实现对被监护人使用次元星尘的远程管理。
		</div>
		<div>
		你进入“亲子平台”并完成监护人账号和被监护人账号的绑定后，在未成年人模式下，你可以根据自主需要，选择帮助自己的被监护人关注次元星尘号，你的被监护人可通过关注页浏览已关注的次元星尘号作品。
		</div>
		<div>
		2.保护未成年人个人信息安全
		</div>
		<div>
		2.1 严格限制收集未成年人个人信息的类型
		</div>
		<div>
		该模式运行仅会收集最小必要的未成年人个人信息，包括：
		</div>
		<div>
		a.设备信息、年龄信息、播放视频的类型、内容、播放完成度等，确保多元、优质内容更好地触达未成年人。
		</div>
		<div>
		b.平台相关日志信息，包括点赞、收藏、关注等，用于保障推荐内容的质量并向未成年人推荐可能感兴趣的视频及相关信息。
		</div>
		<div>
		c.其他必要情形下，次元星尘在收集未成年人信息前会征得你的同意，我们不会收集未经你授权的信息。
		</div>
		<div>
		2.2 严格限定未成年人个人信息的使用目的
		</div>
		<div>
		a.开启该模式后，收集的信息将不会被用于任何商业目的（包括但不限于：广告、营销），不会基于任何商业目的而与第三方共享。
		</div>
		<div>
		b.收集的部分设备信息、网络信息可能会被用于安全与反作弊目的，但是我们不会将以这些目的收集的信息用于其他不相关的领域。
		</div>
		<div>
		2.3 严格的措施和制度保护未成年人个人信息安全
		</div>
		<div>
		a.我们在境内运营过程中收集和产生的未成年人个人信息存储于中华人民共和国境内，不会传输或存储至境外。
		</div>
		<div>
		b.存储所收集儿童的浏览日志信息为6个月，但是法律法规另有规定的除外。
		</div>
		<div>
		c.我们会使用高强度的加密技术、匿名化处理及相关合理可行的手段保护未成人的信息，并使用安全保护机制防止恶意攻击。
		</div>
		<div>
		d.我们会建立专门的安全部门、专门针对未成年人信息的安全管理制度、数据安全流程保障未成年人信息安全。我们采取严格的数据使用和访问制度，确保只有授权人员才可访问，并适时对数据和技术进行安全审计。
		</div>
		<div>
		3.保障未成年人健康使用
		</div>
		<div>
		我们会提供更多元与适合未成年人的优质作品，我们会继续致力于对向未成年人推荐内容的算法优化，让未成年人看到更多不同领域的优质内容，拓展未成年人的视野。
		</div>
		<div>
		4.退出未成年人模式
		</div>
		<div>
		如果你为你的被监护人解除或退出未成年人模式，你的被监护人将可以使用次元星尘的全部功能，收集、处理、共享的信息类型与使用目的也将遵循《次元星尘隐私政策》以及其他相关法律法规。
		</div>
		<div>
		如果你要求删除我们收集的不满14周岁的儿童个人信息，或进行投诉，请通过平台客服联系我们，我们在核验你的身份后，会根据你的请求和法律法规的规定进行处理。
		</div>
		<div>
		`,
	})

	onLoad((option) => {
		console.log('onLoad:', option);
		getInfo(option.type)
	});

	function handleBack() {
		uni.navigateBack()
	}

	function getInfo(type) {
		if (type === '1') {
			detail.value.title = '用户协议'
		} else if (type === '2') {
			detail.value.title = '隐私协议'
		} else if (type === '3') {
			detail.value.title = '儿童/青少年个人信息保护规则'
		}
		// getContractByType({
		// 	configurationType: type
		// }).then(res => {
		// 	console.log('getContractByType:', res)
		// 	if (res.code === 200) {
		// 		detail.value = res.data
		// 	}
		// })
	}
</script>

<style lang="scss">
	.app-container {
		background-color: #fff;
	}

	.content-scroll {
		flex: 1;
		overflow: scroll;
	}

	.scroll-view {
		height: 100%;
	}

	.content {
		padding: 30rpx 40rpx 0;
	}
</style>