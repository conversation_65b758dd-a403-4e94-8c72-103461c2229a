<template>
  <view class="" @click="open">
    <slot name="trigger">
      <view class="flex items-center justify-between">
        <text>选择城市</text>
      </view>
    </slot>
  </view>

  <uv-picker ref="pickerRef" :closeOnClickOverlay="closeOnClickOverlay" :columns="columns" :round="round"
    :confirmColor="confirmColor" :defaultIndex="defaultColumnIdx" :visibleItemCount="visibleItemCount" keyName="name" @change="change" @confirm="confirm" />

</template>

<script setup>
import { ref, computed, watch, watchEffect } from 'vue'
import { useStore } from 'vuex'
import { getSystemCity, getSystemDistrict } from '@/api/common'

const props = defineProps({
  value: {
    type: Object,
    default: () => ({
      pid: null,
      cid: null,
      did: null
    })
  },
  confirmColor: {
    type: String,
    default: '#FE58B7'
  },
  round: {
    type: String,
    default: '32rpx'
  },
  closeOnClickOverlay: {
    type: Boolean,
    default: true
  },
  mode: {
    type: String,
    default: 'province-city', // Options: 'province', 'province-city', 'province-city-district'
    validator: value => ['province', 'province-city', 'province-city-district'].includes(value),
  },
  visibleItemCount: {
	  type: Number,
	  default: 10,
  }
})

const emit = defineEmits(['confirm'])

const store = useStore()
const location = computed(() => store.state.location)

const defaultColumnIdx = computed(() => {
  const pid = props.value.pid || location.value.province.id
  const cid = props.value.cid || location.value.city.id
  const did = props.value.did || location.value.district.id

  const pIdx = location.value.provinces.findIndex(item => item.id === pid)
  const cIdx = location.value.cities.findIndex(item => item.id === cid)
  const dIdx = location.value.districts.findIndex(item => item.id === did)
  if (props.mode === 'province') return [pIdx === -1 ? 0 : pIdx]
  if (props.mode === 'province-city') return [pIdx === -1 ? 0 : pIdx, cIdx === -1 ? 0 : cIdx]
  return [pIdx === -1 ? 0 : pIdx, cIdx === -1 ? 0 : cIdx, dIdx === -1 ? 0 : dIdx]
})

const pickerRef = ref(null)

const open = () => {
  pickerRef.value.open()
}

const close = () => {
  pickerRef.value.close()
}

const columns = ref([])

const updateColumns = () => {
  if (props.mode === 'province') {
    columns.value = [location.value.provinces]
  } else if (props.mode === 'province-city') {
    columns.value = [location.value.provinces, location.value.cities]
  } else {
    columns.value = [location.value.provinces, location.value.cities, location.value.districts]
  }
}

const cache = new Map()
const fetchCities = async (id) => {
  try {
    if (cache.has('city' + id)) {
      return cache.get('city' + id)
    }
    const res = await getSystemCity(id)
    if (res.code === 20000) {
      cache.set('city' + id, res.data)
      return res.data
    }
    return []
  } catch (error) {
    return []
  }
}

const fetchDistricts = async (id) => {
  try {
    if (cache.has('district' + id)) {
      return cache.get('district' + id)
    }
    const res = await getSystemDistrict(id)
    if (res.code === 20000) {
      cache.set('district' + id, res.data)
      return res.data
    }
    return []
  } catch (error) {
    return []
  }
}

const change = async (e) => {
  if (props.mode === 'province') return

  if (e.columnIndex === 0) {
    const citiesList = await fetchCities(e.value[0].id)
    columns.value[1] = citiesList
    if (props.mode === 'province-city-district') {
      columns.value[2] = [] // Reset districts when province changes
      if (citiesList.length > 0) {
        const districtsList = await fetchDistricts(citiesList[0].id)
        columns.value[2] = districtsList
      }
    }
  } else if (e.columnIndex === 1) {
    if (props.mode === 'province-city-district') {
      const districtsList = await fetchDistricts(e.value[1].id)
      columns.value[2] = districtsList
    }
  }
}

const confirm = (e) => {
  emit('confirm', e.value)
  close()
}

const initialize = async () => {
  if (props.mode !== 'province') {
    const provinces = location.value.provinces
    const pid = props.value.pid || location.value.province.id
	// console.log('&&&&&&&&&&&&&pid:', pid, typeof pid)
    const currentProvince = provinces.find(item => item.id === pid)
    if (currentProvince) {
      // 从 store 中获取 cities，如果为空，则从 api 获取
      // const cities = location.value.cities
	  // console.log('location.value.cities:', location.value.cities)
      const firstProvince = currentProvince || provinces[0]
      // const citiesList = cities.length > 0 ? cities : await fetchCities(firstProvince.id)
      const citiesList = await fetchCities(firstProvince.id)
	  // console.log('&&&&&&&&&&citiesList:', citiesList)
      columns.value[1] = citiesList
      if (props.mode === 'province-city-district' && citiesList.length > 0) {
        const cid = props.value.cid || location.value.city.id
        const currentCity = citiesList.find(item => item.id === cid)
        const firstCity = currentCity || citiesList[0]
        const districtsList = await fetchDistricts(firstCity.id)
        columns.value[2] = districtsList
      }
    }
  }
}

watchEffect(() => {
  let shouldUpdate = false
  // console.log('***************store.state.location:', store.state.location)
  if (store.state.location.provinces && store.state.location.provinces.length > 0) {
    shouldUpdate = true
  }
  
  const { pid, cid, did } = props.value
  // console.log('pid, cid, did:', pid, cid, did)
  if (pid !== undefined || cid !== undefined || did !== undefined) {
    shouldUpdate = true
  }

  if (shouldUpdate) {
    updateColumns()
    initialize()
  }
})
</script>

<style scoped lang='scss'></style>