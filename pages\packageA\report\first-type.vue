<!-- 活动发布主界面 -->
<template>
	<view class="container">
		<Navbar :type="1"  leftText="举报"></Navbar>
		<view class="main">
			<type-select style="height: 100%;" :data="violationData" @select="selectHandle"/>
		</view>
	</view>
	
</template>
<script setup lang="ts">
	import { ref, toRaw, onUnmounted, watchEffect, computed } from 'vue';
	import { reportService, reportComiket } from "@/api/report";
	import { onLoad } from "@dcloudio/uni-app";
	import typeSelect from "./components/type-select.vue";
	import violationData from "./const";
	import { ReportType, QueryParma } from "./const";
	
	
	
	type Data = typeof violationData;
	type DataItem = Data[number];
	const templateQuery: QueryParma = {id: "", type: ReportType.Service};
	onLoad((query: QueryParma) => {
		templateQuery.id = query.id;
		templateQuery.type = query.type;
		console.log("接受的采纳数", query)
		if (query.id && query.type) return;
		uni.showToast({
			icon:'exception',
			title: !query.id ? "未传递id" : "未传递type",
			duration: 3000
		});
	})
	const selectHandle = (item: DataItem) => {
		uni.navigateTo({
			url: `/pages/packageA/report/second-type?id=${templateQuery.id}&type=${templateQuery.type}&firtstTypeCode=${item.value}`
		})
	}
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		height: 100%;
		box-sizing: border-box;
		
		padding-bottom: constant(safe-area-inset-bottom);;
		padding-bottom: env(safe-area-inset-bottom);
	}
	
	.main {
		flex: 1 1 auto;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		box-sizing: border-box;
		padding: 32rpx 32rpx 24rpx;
		
		overflow-y: hidden;
	}
</style>
