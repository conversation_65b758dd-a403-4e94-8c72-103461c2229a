<template>
	<view class="container">
		<Navbar :type="1"  leftText="首页"></Navbar>
		<view class="header">
			<image class="banner" mode="aspectFit" :src="dataInfo.cover"></image>
		</view>
		<view class="main">
			<view class="brand">
				<image class="left logo" :src="dataInfo.author.avatar" mode="aspectFit"></image>
				<view class="middle">
					<view class="title">{{dataInfo.author.username}}</view>
					<view class="nickname">{{dataInfo.author.nickname}}</view>
				</view>
				<view class="right" @tap="followBrand">
					<view class="btn">关 注</view>
				</view>
			</view>
			<view class="content">
				<view class="text-wrapper">{{dataInfo.detail.text}}</view>
				<template v-if="dataInfo.detail.images">
					<view class="image-wrapper" v-for="item of dataInfo.detail.images">
						<image class="image" :src="item" mode="aspectFill" />
					</view>
				</template>
				<Comment :id="announcementId" :commentsInfo="comments" @checkAll="navigateToCommentList"/>
			</view>

		</view>
	</view>
</template>

<script setup lang="ts">
	import { ref, computed } from 'vue';
	import { onLoad, onShow } from "@dcloudio/uni-app";
	import Comment from "./components/comment.vue";
	import {
		getAnnouncementDetail,
		getAnnouncementComments,
		publishAnnouncementComment,
	} from "@/api/announcement";
	import { addFollow } from '@/api/user'
	import type {
		AnnouncementItem,
		CommentsItem,
	} from "@/api/announcement";
	import { getCompletedImageUrl } from '@/common/utils';
	interface Content {
		images?: string[], 
		text: string
	}
	const dataInfo = ref<AnnouncementItem & {detail: Content}>({author: {}, detail: {}} as any);
	interface CommentsInfo {
		count: number, 
		results: Array<CommentsItem>
	}
	const announcementId = ref("");
	const comments = ref<CommentsInfo>({count: 0, results: []});
	onLoad((query: {id: string}) => {
		if (!query.id) return uni.showToast({
			icon: "error",
			title: "跳转时参数错误"
		});
		announcementId.value = query.id;
		loadDetail(+query.id);
		loadComments(+query.id);
		
	});
	// 是否跳转到评论列表页，是的话在显示时需要重新刷新
	let isNavigateToCommentsList = false;
	onShow(() => {
		if (!isNavigateToCommentsList) return;
		isNavigateToCommentsList = true;
		loadComments(+announcementId.value);
	});
	
	function navigateToCommentList() {
		isNavigateToCommentsList = true;
		uni.navigateTo({
			url: `/pages/announcement/comment-list?id=${announcementId.value}`
		})
	}
	
	
	// 加载详情
	function loadDetail(id: number) {
		return getAnnouncementDetail(id).then(res => {
			const isSucessed = res.code === 20000;
			if (!isSucessed) return uni.showToast({
				icon: "error",
				title: "获取公告失败"
			});
			const data = res.data!;
			if (!data) return;
			// editor为json时，默认格式为字符串 {text: 1, images: []}
			data.cover = getCompletedImageUrl(data.cover);
			data.author.avatar = getCompletedImageUrl(data.author.avatar);
			
			const isJson = data.editor === "json";
			const detail: Content = isJson ? JSON.parse(data.content) : {text: data.content, images: []};
			// detail.images = ["test/2.jpg", "test/2.jpg"];
			detail.images && (detail.images = detail.images.map(getCompletedImageUrl));
			
			dataInfo.value = {...data, detail: detail};
		});
	}
	// 加载评论
	function loadComments(id: number) {
		return getAnnouncementComments(id).then(res => {
			const isSucessed = res.code === 20000;
			if (!isSucessed) return uni.showToast({
				icon: "error",
				title: "获取评论失败"
			});
			const results = res.data?.results || [];
			if (!results.length) return;
			comments.value.count = res.data.count;
			comments.value.results = results;
		})
	}
	
	function followBrand() {
		if (!dataInfo.value.author) return console.error("关注的品牌用户不存在");
		// 关注
		const userId = dataInfo.value.author.id;
		addFollow(+userId).then(res => {
			const isSucessed = res.code === 20000;
			if (!isSucessed) return uni.showToast({
				icon: "error",
				title: "关注用户失败"
			});
			return uni.showToast({
				title: "关注用户成功"
			});
		});
	}
</script>

<style scoped lang='scss'>
	.container {
		display: flex;
		flex-direction: column;
		height: 100%;
		box-sizing: border-box;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		
		.header, .main {
			flex: none;
		}
		
		.header {
			display: flex;
			padding: 0 24rpx;
			height: 396rpx;
			border-radius: 20rpx 20rpx 20rpx 20rpx;
			justify-content: stretch;
			align-items: stretch;
			.banner {
				display: block;
				height: auto;
				width: auto;
				flex:  auto;
				border-radius: 20rpx;
			}
		}
		
		.main {
			display: flex;
			flex-direction: column;
			flex: auto;
			overflow-y: hidden;
			padding: 20rpx 0;
			.brand {
				flex: 0 0 auto;
				display: flex;
				padding: 12rpx 24rpx;
				gap: 20rpx;
				overflow: hidden;
				border: 2rpx solid rgba(216, 216, 216, 1);
				.logo {
					height: 100rpx;
					width: 100rpx;
					border-radius: 50%;
					border: 2rpx solid rgba(216, 216, 216, 1);
				}
				
				.left {
					flex: none;
				}
				
				.middle {
					display: flex;
					flex: auto;
					flex-direction: column;
					justify-content: center;
					gap: 16rpx;
					font-size: 28rpx;
					color: rgba(31, 31, 31, 1);
					.title {
						font-size: 32rpx;
						color: rgba(61, 61, 61, 1);
						font-weight: 700;
					}
				}
				
				.right {
					display: flex;
					flex: none;
					justify-content: center;
					align-items: center;
					.btn {
						height: 60rpx;
						line-height: 60rpx;
						width: 100rpx;
						text-align: center;
						border-radius: 14rpx;
						color: white;
						background-color: rgba(254, 88, 183, 1);
					}
				}
				
			}
			.content {
				flex: auto;
				overflow-y: auto;
				padding: 24rpx;
				
				.text-wrapper {
					line-height: 48rpx;
				}
				.image-wrapper {
					margin-top: 20rpx;
					height: 368rpx;
					width: 100%;
					border-radius: 32rpx;
					overflow: hidden;
					.image {
						width: 100%;
						height: 100%;
					}
				}
			}
		}
	}
</style>