<template>
	<view class="wrapper">
		<view class="feature-item-wrapper">
			<view class="label">服务区域设置说明</view>
			<image class="icon" src="/static/icon/<EMAIL>"></image>
		</view>
		<dust-location-picker :value="{ cid: selectedCity.id || 1, pid: selectedProvince.id || 1 }"
			@confirm="locationConfirm">
			<template #trigger>
				<view class="city-wrapper">
					<view class="city-box">
						<text>{{ displayLocation }}</text>
						<image class="icon" src="/static/icon/<EMAIL>"></image>
					</view>
				</view>
			</template>
		</dust-location-picker>
		<view class="area-wrapper">
			<view class="area-item" :class="{ active: isAreaSelected(item.id) }" v-for="item in areaList" :key="item.id"
				@tap="() => toggleAreaSelection(item.id)">
				<text>{{ item.name }}</text>
				<image v-if="isAreaSelected(item.id)" class="checked-icon" src="/static/icon/<EMAIL>"></image>
			</view>
		</view>
	</view>
	<view class="footer flex">
		<view class="primary btn" @click="handleConfirm">保存</view>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		onMounted,
		watch
	} from 'vue'
	import {
		useStore
	} from 'vuex'
	import {
		getSystemDistrict,
		getSystemCity
	} from '@/api/common';

	const store = useStore()
	const location = computed(() => store.state.location)

	const props = defineProps({
		locales: {
			type: Object,
			default: () => ({})
		}
	})

	const emit = defineEmits(['cancel', 'confirm'])

	const areaList = ref([])
	// 当前选中的省市
	const selectedProvince = ref({
		id: 1,
		name: '北京市',
		idStr: '1'
	})
	const selectedCity = ref({
		id: 1,
		name: '北京市',
		idStr: '1'
	})
	// 内部存储locales结构
	const internalLocales = ref({})
	// 显示位置文本
	const displayLocation = computed(() => {
		if (!selectedProvince.value || !selectedCity.value) return '请选择省/市'
		return selectedCity.value?.name || ''
	})

	// 初始化位置信息
	function initLocation() {
		// 尝试从store获取位置
		const province = location.value?.province
		const city = location.value?.city
		console.log('location.value:', location.value)
		// 设置默认位置为北京（省id=1，市id=1）
		if (!province || !city) {
			selectedProvince.value = {
				id: 1,
				name: '北京市',
				idStr: '1'
			}
			selectedCity.value = {
				id: 1,
				name: '北京市',
				idStr: '1'
			}
		} else {
			selectedProvince.value = {
				...province,
				idStr: String(province.id || 1),
			}
			selectedCity.value = {
				...city,
				idStr: String(city.id || 1)
			}
		}
		console.log('selectedProvince.value:', selectedProvince.value)
		console.log('selectedCity.value:', selectedCity.value)
		// 初始化当前城市的区域结构
		const provinceId = selectedProvince.value.idStr
		const cityId = selectedCity.value.idStr

		if (!internalLocales.value[provinceId]) {
			internalLocales.value[provinceId] = {}
		}

		if (!internalLocales.value[provinceId][cityId]) {
			internalLocales.value[provinceId][cityId] = []
		}
		console.log('******internalLocales.value:', internalLocales.value)
		// 获取城市列表
		// getCity(selectedProvince.value.id || 1, selectedCity.value.id || 1)
		// 获取区域列表
		getDistrict(selectedCity.value.id || 1)
	}

	watch(() => props.locales, (newLocales) => {
		console.log('watch locales:', newLocales)
		// 清除之前的内部状态
		const newInternalLocales = {}

		// 递归转换所有键为字符串
		for (const provinceId in newLocales) {
			const provinceIdStr = String(provinceId)
			newInternalLocales[provinceIdStr] = {}

			for (const cityId in newLocales[provinceId]) {
				const cityIdStr = String(cityId)
				newInternalLocales[provinceIdStr][cityIdStr] = [...newLocales[provinceId][cityId]]
			}
		}

		// 更新内部状态
		internalLocales.value = newInternalLocales

		// 如果有选中的省市区，确保当前省市的位置结构存在
		if (selectedProvince.value && selectedCity.value) {
			const provinceId = selectedProvince.value.idStr
			const cityId = selectedCity.value.idStr

			if (provinceId && cityId) {
				if (!internalLocales.value[provinceId]) {
					internalLocales.value[provinceId] = {}
				}

				if (!internalLocales.value[provinceId][cityId]) {
					internalLocales.value[provinceId][cityId] = []
				}
			}
		}

		// 设置选中省市（如果反显数据中有值）
		if (Object.keys(internalLocales.value).length > 0) {
			const firstProvinceId = Object.keys(internalLocales.value)[0]
			if (firstProvinceId && internalLocales.value[firstProvinceId]) {
				const firstCityId = Object.keys(internalLocales.value[firstProvinceId])[0]

				if (firstProvinceId && firstCityId) {
					selectedProvince.value = {
						id: Number(firstProvinceId),
						idStr: firstProvinceId,
						name: `省份${firstProvinceId}`
					}

					selectedCity.value = {
						id: Number(firstCityId),
						idStr: firstCityId,
						name: `城市${firstCityId}`
					}

					getCityNames()
				}
			}
		}
	}, {
		immediate: true,
		deep: true
	})

	// 获取当前省市的真实名称
	async function getCityNames() {
		if (!selectedProvince.value || !selectedCity.value) return

		try {
			// 获取城市信息
			const res = await getSystemCity(selectedProvince.value.id)
			if (res.code === 20000) {
				// 更新省份名称
				const provinceInfo = res.data.find(p => p.id === selectedProvince.value.id)
				if (provinceInfo) {
					selectedProvince.value.name = provinceInfo.province?.name || provinceInfo.name
				}

				// 更新城市名称
				const cityInfo = res.data.find(c => c.id === selectedCity.value.id)
				if (cityInfo) {
					selectedCity.value.name = cityInfo.name
				}
			}
		} catch (error) {
			console.error('获取省市信息失败', error)
		}
	}

	// 组件挂载时初始化
	onMounted(() => {
		initLocation()
	})

	async function getCity(pid, cid) {
		try {
			const res = await getSystemCity(pid)
			if (res.code === 20000) {
				selectedCity.value = res.data.find(item => item.id === cid)
			}
		} catch (error) {
			console.log(error, 'getSystemCity')
		}
	}

	async function getDistrict(cid) {
		try {
			const res = await getSystemDistrict(cid)
			if (res.code === 20000) {
				areaList.value = res.data
			}
		} catch (error) {
			console.log(error, 'getSystemDistrict')
		}
	}

	const locationConfirm = (e) => {
		const [province, city] = e

		if (!province || !city || !province.id || !city.id) {
			console.error('位置选择器返回无效数据')
			return
		}
		console.log('locationConfirm province, city:', province, city)
		selectedProvince.value = {
			...province,
			idStr: String(province.id)
		}

		selectedCity.value = {
			...city,
			idStr: String(city.id)
		}

		const provinceId = selectedProvince.value.idStr
		const cityId = selectedCity.value.idStr

		// 初始化新省市的区域结构
		if (!internalLocales.value[provinceId]) {
			internalLocales.value[provinceId] = {}
		}

		if (!internalLocales.value[provinceId][cityId]) {
			internalLocales.value[provinceId][cityId] = []
		}
		console.log('locationConfirm internalLocales.value:', internalLocales.value)
		getDistrict(selectedCity.value.id)
	}

	// 检查区域是否选中
	function isAreaSelected(areaId) {
		if (!selectedProvince.value || !selectedCity.value) return false
		const provinceId = selectedProvince.value.idStr
		const cityId = selectedCity.value.idStr
		return internalLocales.value[provinceId]?.[cityId]?.includes(areaId)
	}

	// 切换区域选择状态
	function toggleAreaSelection(areaId) {
		if (!selectedProvince.value || !selectedCity.value) return
		const provinceId = selectedProvince.value.idStr
		const cityId = selectedCity.value.idStr
		console.log('toggleAreaSelection selectedProvince:', selectedProvince.value)
		console.log('toggleAreaSelection selectedCity:', selectedCity.value)
		// 确保存在对应结构
		if (!internalLocales.value[provinceId]) {
			internalLocales.value[provinceId] = {}
		}

		if (!internalLocales.value[provinceId][cityId]) {
			internalLocales.value[provinceId][cityId] = []
		}

		console.log('internalLocales:', internalLocales.value)

		// 更新选择状态
		const areas = internalLocales.value[provinceId][cityId]
		const index = areas.indexOf(areaId)

		if (index > -1) {
			areas.splice(index, 1)

			// 清除空结构
			if (areas.length === 0) {
				delete internalLocales.value[provinceId][cityId]

				if (Object.keys(internalLocales.value[provinceId]).length === 0) {
					delete internalLocales.value[provinceId]
				}
			}
		} else {
			areas.push(areaId)
		}
	}

	// 确认选择
	function handleConfirm() {
		// 创建新的对象并清除空省市
		const cleanLocales = {};

		for (const provinceId in internalLocales.value) {
			// 1. 收集该省份下有选区的城市
			const validCities = {};

			for (const cityId in internalLocales.value[provinceId]) {
				const districts = internalLocales.value[provinceId][cityId];
				// 2. 仅保留有选区的城市
				if (districts && districts.length > 0) {
					validCities[cityId] = [...districts]; // 浅拷贝
				}
			}

			// 3. 仅当该省份下有选区城市时才保留
			if (Object.keys(validCities).length > 0) {
				cleanLocales[provinceId] = validCities;
			}
		}

		// 4. 验证最终结果是否为空
		if (Object.keys(cleanLocales).length === 0) {
			uni.showToast({
				title: '请至少选择一个区域',
				icon: 'none'
			});
			return;
		}

		console.log('*******cleanLocales:', cleanLocales);
		emit('confirm', cleanLocales)
	}
</script>

<style scoped lang='scss'>
	.wrapper {
		width: 100%;
		box-sizing: border-box;
		padding: 0 32rpx;
		margin-top: 32rpx;

		.feature-item-wrapper {
			width: 100%;
			height: 64rpx;
			box-sizing: border-box;
			padding: 16rpx 24rpx;
			background: #F7F8FA;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.label {
				font-size: 24rpx;
				color: #1F1F1F;
			}

			.icon {
				width: 30rpx;
				height: 30rpx;
			}
		}

		.city-wrapper {
			display: flex;

			.city-box {
				margin-top: 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 8rpx;
				height: 60rpx;
				background: #F7F8FA;
				border-radius: 36rpx 36rpx 36rpx 36rpx;
				font-size: 28rpx;
				color: #1D2129;
				padding: 0 20rpx;
			}

			.icon {
				width: 24rpx;
				height: 24rpx;
			}
		}

		.area-wrapper {
			margin-top: 32rpx;
			display: flex;
			flex-direction: column;
			gap: 24rpx;
			height: 394rpx;
			overflow-y: auto;

			.area-item {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding-bottom: 24rpx;
				border-bottom: 2rpx solid #EEEEEE;
				font-size: 32rpx;
				color: #1F1F1F;

				&:last-child {
					border: none;
				}

				&.active {
					color: #FE58B7;
				}

				.checked-icon {
					width: 40rpx;
					height: 40rpx;
				}
			}
		}
	}

	.footer {
		box-sizing: border-box;
		padding: 20rpx 64rpx;
		width: 100%;
		height: 118rpx;
		gap: 16rpx;
		margin-top: 40rpx;
		border-top: 2rpx solid #EEEEEE;

		.btn {
			flex: 1;
			height: 78rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 40rpx;
			font-weight: 700;
			font-size: 32rpx;
			color: #1F1F1F;

			&.primary {
				background: #FE58B7;
				color: #fff;
			}

			&.secondary {
				background-color: #eee;
			}
		}
	}
</style>