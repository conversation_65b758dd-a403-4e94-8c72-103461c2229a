import { requestApi } from './request';

//订单列表
export const ordersList = (params) => {
	return requestApi({
		url: `/api/v1/foreground/mine/order`,
		method: 'GET',
		data: params,
		loading: false,
	});
};
//订单详情
export const orderInfo = (id, type) => {
	let url = `/api/v1/foreground/mine/order/${id}`;
	if (type) {
		url = `/api/v1/foreground/mine/order/${id}?type=${type}`;
	}
	return requestApi({
		url: url,
		method: 'GET'
	});
};

// 创建订单
export const createOrder = (consult_id, data) => {
	return requestApi({
		url: `/api/v1/foreground/message/consult/${consult_id}/order`,
		method: 'POST',
		data
	});
};

// 支付订单
export const payOrder = (orderid, data) => {
	return requestApi({
		url: `/api/v1/foreground/mine/order/${orderid}/pay`,
		method: 'POST',
		data
	});
};

/** 创建服务评论 Params */
interface createServiceReviewParams {
	service_item_id: number;
	editor: string;
	content: any;
	rating: number;
	is_default_positive: boolean;
	is_anonymous: boolean;
	parent: number;
}

/** 创建服务评论 */
export const createServiceReview = (id: number, data: createServiceReviewParams) => {
	return requestApi({
		url: `/api/v1/foreground/mine/order/${id}/review`,
		method: 'POST',
		data
	});
};
/** 创建服务评论 end */
/** 商家修改回复评论 */
export const replyServiceReview = (order_id, review_id, data) => {
	return requestApi({
		url: `/api/v1/foreground/mine/order/${order_id}/review/${review_id}`,
		method: 'PUT',
		data
	});
};
/** 商家修改回复评论 end */
/** 删除服务评论 */
export const deleteServiceReview = (id: number, review_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/mine/order/${id}/review/${review_id}`,
		method: 'DELETE'
	});
};
/** 删除服务评论 end */

/** 商家执行 */
export const executeOrder = (order_id) => {
	return requestApi({
		url: `/api/v1/foreground/mine/order/${order_id}/finish`,
		method: 'PUT'
	});
};
/** 商家取消订单/违约 */
export const cancelOrder = (order_id) => {
	return requestApi({
		url: `/api/v1/foreground/mine/order/${order_id}/break`,
		method: 'PUT'
	});
};
// 用户使用订单
export const userExecuteOrder = (order_id) => {
	return requestApi({
		url: `/api/v1/foreground/mine/order/${order_id}/use`,
		method: 'PUT'
	});
};
// 用户取消订单
export const userCancelOrder = (order_id) => {
	return requestApi({
		url: `/api/v1/foreground/mine/order/${order_id}/cancel`,
		method: 'PUT'
	});
};
// 用户违约
export const userDefaultCancelOrder = (order_id) => {
	return requestApi({
		url: `/api/v1/foreground/mine/order/${order_id}/default`,
		method: 'PUT'
	});
};
