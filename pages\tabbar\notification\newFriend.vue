<template>
	<z-paging ref="paging" v-model="dataList" @query="queryList" :safe-area-inset-bottom="true"
		:use-safe-area-placeholder="true" :hide-empty-view="hideEmptyView">
		<template #top>
			<Navbar :type="1" leftText="新的朋友"></Navbar>
			<view class="searchBox">
				<view class="search">
					<NotificationNavbar placeholder="用户昵称" @tosearch="tosearch" />
				</view>
			</view>
			<view class="tabBox">
				<view v-for="(item, index) in tabList" :key="index" :class="{ 'tabItem': true, 'tabItemActive': tabActive === item.value }" @tap="onChangeTab(item)">
					<image class="tabIcon" v-if="tabActive === item.value" :src="item.iconActive" />
					<image class="tabIcon" v-else :src="item.icon" />
					<text>{{item.label}}</text>
				</view>
			</view>
		</template>
		<view v-for="(item, index) in dataList" class="itemBox" :key="item.id" @tap="toUser(item)">
			<MyUserItem :data="item" :isBtn="true" :onBtn="onAddFriend" :type="tabActive"/>
		</view>
	</z-paging>
</template>

<script setup>
import { ref } from 'vue';
import MyUserItem from '@/components/my-user-item/MyUserItem.vue';
import { onLoad, onShareAppMessage } from '@dcloudio/uni-app';
import { getFollowList, getFansList, replyFansApi, delFollow } from '@/api/user';
import { addFriend } from '@/api/message'
import iocn_yq from '@/static/icon/<EMAIL>';
import follow from '@/static/icon/follow.png';
import followActive from '@/static/icon/followActive.png';
import fans from '@/static/icon/fans.png';
import fansActive from '@/static/icon/fansActive.png';

const dataList = ref([]);
const searchList = ref([]);
const overlayShow = ref(false);
const inviteCode = ref(null);
const isFans = ref(false);
const paging = ref(null);
const hideEmptyView = ref(true);
const inputValue = ref('');
const tabActive = ref('1')
const tabList = ref([{
	value: '1',
	label: '关注',
	icon: '/static/icon/follow.png',
	iconActive: '/static/icon/followActive.png',
}, {
	value: '2',
	label: '粉丝',
	icon: '/static/icon/fans.png',
	iconActive: '/static/icon/fansActive.png',
}])

onLoad((option) => {
	console.log('new Friend option:', option)
	if (option.tab) {
		tabActive.value = option.tab
	}
})

const onFollow = () => {
	isFans.value = false;
	paging.value.clear();
	hideEmptyView.value = true;
	paging.value.reload();
};

const onFans = () => {
	isFans.value = true;
	paging.value.clear();
	hideEmptyView.value = true;
	paging.value.reload();
};

const onAdd = () => {
	overlayShow.value = true;
}

const onAddFriend = async (item, type) => {
	if (type === '1') {
		await addFriend(item.user_id || item.id)
		uni.showToast({
			title: '已发送好友申请',
			icon: 'none'
		})
		return
	}
	if (type === '2') {
		if (item.is_followed_back) {
			await delFollow(item.user_id || item.id)
		} else {
			await replyFansApi(item.user_id || item.id)
		}
		paging.value.reload();
		return
	}
}

const queryList = async (pageNum, pageSize) => {
	// 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
	// 这里的pageNum和pageSize会自动计算好，直接传给服务器即可
	// 模拟请求服务器获取分页数据，请替换成自己的网络请求
	const params = { page: pageNum, size: 10 }
	const { data: { results }, code } = tabActive.value === '2' ? (await getFansList(params)) : (await getFollowList(params));
	if (code === 20000) {
		paging.value.complete(results);
	} else {
		paging.value.complete([]);
	}
	hideEmptyView.value = false
};

// 跳转用户主页
function toUser(item) {
	uni.navigateTo({
		url: `/pages/home-pages/user-mine?id=${item.user_id}`
	});
}

// 跳转用户搜索
const tosearch = () => {
	uni.navigateTo({
		url: '/pages/tabbar/notification/addFriend'
	});
}

function onChangeTab(item) {
	tabActive.value = item.value
	paging.value.clear();
	hideEmptyView.value = true;
	paging.value.reload();
}
</script>
<style lang="scss" scoped>
.searchBox {
	display: flex;
	align-items: center;
	padding: 32rpx 24rpx 0;

	.search {
		flex: 1;
		margin-right: 28rpx;
	}

	.searchYQ {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		.addIcon {
			width: 64rpx;
			height: 64rpx;
		}

		.addTxt {
			font-size: 20rpx;
			color: #3D3D3D;
			line-height: 20rpx;
		}
	}
}

.tabBox {
	padding: 40rpx 52rpx 8rpx;
	display: flex;
	gap: 56rpx;

	.tabItem {
		display: flex;
		align-items: center;
		justify-content: center;
		flex: 1;
		height: 96rpx;
		font-size: 32rpx;
		color: #1F1F1F;
		line-height: 32rpx;
		border-radius: 48rpx;

		.tabIcon {
			width: 84rpx;
			height: 84rpx;
			margin-right: 8rpx;
		}
	}

	.tabItemActive {
		color: #fff;
		background: #FE58B7;
	}
}

.itemBox {
	padding: 0 32rpx;
}

.serve {
	position: fixed;
	bottom: 0;
	padding-bottom: 30rpx;
	border-radius: 32rpx 32rpx 0rpx 0rpx;
	width: 100%;
	background-color: #fff;
	font-size: 32rpx;
	color: #3d3d3d;
	height: 598rpx;

	.serveList {
		height: 144rpx;
		border-bottom: 2rpx solid #eeeeee;
		display: flex;
		justify-content: space-between;
		align-items: center;

		view {
			display: flex;
			align-items: center;

			text:nth-child(1) {
				font-weight: 400;
				font-size: 32rpx;
				color: #1f1f1f;
			}

			text:nth-child(2) {
				font-weight: 400;
				font-size: 28rpx;
				color: #85878d;
			}

			view {
				display: flex;
				flex-direction: column;
				align-items: start;
			}

			image {
				width: 96rpx;
				height: 96rpx;
				margin-right: 24rpx;
			}
		}
	}

	.bottomBox {
		.serveRedTitle {
			height: 64rpx;
			background: rgba(254, 30, 66, 0.12);
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			display: flex;
			align-items: center;

			image {
				width: 32rpx;
				height: 32rpx;
				margin-left: 20rpx;
				margin-right: 16rpx;
			}

			text {
				font-weight: 400;
				font-size: 24rpx;
				color: #fe1e42;
			}
		}
	}

	.serveTitle {
		border-radius: 32rpx 32rpx 0rpx 0rpx;
		width: 100%;
		height: 108rpx;
		background-color: #fff;
		z-index: 999;
		position: fixed;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 28rpx;

		image {
			position: fixed;
			width: 48rpx;
			height: 48rpx;
			right: 32rpx;
		}
	}
}
</style>
