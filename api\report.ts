// 举报相关接口
import { requestApi } from './request';

export interface ReportParams {
	"reason_type": number;
	"reason": string; 		// 上传图片数组字符串
	"description": string;  // 违规描述
}

export const reportService = (serviceId: string, params: ReportParams) => {
	return requestApi({
		url: `/api/v1/foreground/home/<USER>/service/${serviceId}/report`,
		data: params,
		loading: false,
	});
}

export const reportComiket = (id: string, params: ReportParams) => {
	return requestApi({
		url: `/api/v1/foreground/home/<USER>/expo/${id}/report`,
		data: params,
		loading: false,
	});
}
