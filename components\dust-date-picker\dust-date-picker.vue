<template>
	<view class="dust-calendar">
		<view class="header">
			<view class="item" @tap="() => yearPicker.open()">
				<text>{{ currentDayjs.year() }}年</text>
				<image class="icon" src="/static/icon/<EMAIL>"></image>
			</view>
			<view class="item" @tap="() => monthPicker.open()">
				<text>{{ currentDayjs.month() + 1 }}月</text>
				<image class="icon" src="/static/icon/<EMAIL>"></image>
			</view>
		</view>
		<uv-calendars :date="defaultDate" ref="calendar" insert :mode="mode" :showMonth="false" color="#FE58B7"
			@change="handleChange" :allowSameDay="allowSameDay" />
		<view class="time-list" v-if="showTimeSlot">
			<view v-for="(item, index) in timeArr" :key="index"
				:class="activeTimeId[index] == item.value ? 'activeTiem' : ''" @tap="addTime(index)">
				<text>{{ item.name }}</text>
				<text>{{ item.time }}</text>
			</view>
		</view>
		<slot name="footer">
			<view class="footer flex">
				<view class="primary btn" @tap="handleConfirm">{{ confirmText }}</view>
			</view>
		</slot>
	</view>

	<uv-picker closeOnClickOverlay ref="monthPicker" :columns="monthColumns" round="32rpx" confirmColor="#FE58B7"
		@confirm="monthConfirm" :defaultIndex="[currentDayjs.month()]"></uv-picker>
	<uv-picker closeOnClickOverlay ref="yearPicker" :columns="yearColumns" round="32rpx" confirmColor="#FE58B7"
		@confirm="yearConfirm" :defaultIndex="[yearColumns[0].indexOf(currentDayjs.year())]"></uv-picker>
</template>

<script setup>
	import {
		ref,
		computed,
		onMounted,
		watchEffect
	} from 'vue'
	import dayjs from 'dayjs'
	const props = defineProps({
		showTimeSlot: {
			type: Boolean,
			default: false
		},
		timeSlotCannotNull: {
			type: Boolean,
			default: true
		},
		timeSlot: {
			type: Number,
			default: 0
		},
		dateSlot: {
			type: [Array, String],
			required: true
		},
		/** false 不允许选择的日期早于当天，true 允许选择早于当天的日期 */
		canEarlyToday: {
			type: Boolean,
			default: false
		},
		confirmText: {
			type: String,
			default: '保存'
		},
		/**
		 * 年份范围
		 * 第一个值表示最小年份
		 * 第二个值表示最大年份
		 */
		yearRange: {
			type: Array,
			default: [0, 10]
		},
		mode: {
			validator(value, props) {
				// 如果mode为range，则dateSlot必须是一个数组
				if (['range', 'multiple'].includes(value) && !Array.isArray(props.dateSlot)) {
					return false
				}
				// 如果mode为single，则dateSlot必须是一个字符串
				if (value === 'single' && typeof props.dateSlot !== 'string') {
					return false
				}
				return ['single', 'multiple', 'range'].includes(value)
			},
			default: 'multiple'
		},
		// 如果没有选择日期，是否需要设置默认当天，默认 true，所有模式均有效
		isDay: {
			type: Boolean,
			default: true,
		},
		// 是否允许日期范围的起止时间为同一天，mode = range时有效
		allowSameDay: {
			type: Boolean,
			default: true,
		},
		// 脏数据过滤年份
		filterDirtyDates: {
			type: String,
			default: '', // 默认不进行过滤
		}
	})
	const emit = defineEmits(['cancel', 'confirm'])
	// 添加当前年份/月份的缓存
	const currentDayjs = ref(dayjs())
	const calendar = ref(null)
	const dateSelection = ref([])
	const defaultDate = ref('')
	const monthPicker = ref(null)
	const yearPicker = ref(null)
	const monthColumns = ref([Array.from({
		length: 12
	}, (_, i) => i + 1)])
	const timeArr = ref([{
			name: '上午',
			time: '8:00-12:00',
			value: 1
		},
		{
			name: '下午',
			time: '12:00-18:00',
			value: 2
		},
		{
			name: '晚上',
			time: '18:00-23:00',
			value: 4
		},
		{
			name: '夜间',
			time: '23:00-8:00',
			value: 8
		}
	]);
	const activeTimeId = ref([0, 0, 0, 0]);
	// 标记用户是否已主动选择日期
	const userHasSelected = ref(false);
	// 年份列计算
	const yearColumns = computed(() => {
		const currentYearVal = dayjs().year()
		const years = []
		for (let i = props.yearRange[0]; i <= props.yearRange[1]; i++) {
			years.push(currentYearVal + i)
		}
		return [years]
	})
	// 月份确认处理
	const monthConfirm = (e) => {
		const month = parseInt(e.value[0], 10)
		currentDayjs.value = currentDayjs.value.month(month - 1)
		calendar.value.setDate(currentDayjs.value.format('YYYY/MM/DD'))
	}
	// 年份确认处理
	const yearConfirm = (e) => {
		const year = parseInt(e.value[0], 10)
		currentDayjs.value = currentDayjs.value.year(year)
		calendar.value.setDate(currentDayjs.value.format('YYYY/MM/DD'))
	}
	// 时间选择
	const addTime = (index) => {
		const timeValue = timeArr.value[index].value
		activeTimeId.value[index] = activeTimeId.value[index] === timeValue ? 0 : timeValue
	}
	// 二进制时间检测
	const checkBinary = (n) => {
		if (!n) return [0, 0, 0, 0]
		return [1, 2, 4, 8].map(bit => (n & bit) ? bit : 0)
	}
	// 日历变更处理
	const handleChange = (e) => {
		console.log('handleChange:', e)
		// 标记用户已主动选择日期
		userHasSelected.value = true;
		if (props.mode === 'multiple') {
			dateSelection.value = e.multiple.data
		} else if (props.mode === 'range') {
			dateSelection.value = e.range
		} else {
			dateSelection.value = e.fulldate
		}
	}
	// 日期是否早于今天检查
	const checkEarlyToday = (dateStr) => {
		// 获取选择的日期
		const selectedDate = dayjs(dateStr, 'YYYY/MM/DD').startOf('day')

		// 获取今天的日期
		const today = dayjs().startOf('day')

		// 当不允许早于今天时，检查是否早于今天
		if (!props.canEarlyToday && selectedDate.isBefore(today)) {
			return false
		}

		return true
	}
	// 确认操作
	const handleConfirm = () => {
		console.log('handleConfirm dateSelection:', dateSelection.value)
		const timeSlot = activeTimeId.value.reduce((pre, cur) => pre + cur, 0)
		console.log('handleConfirm timeSlot:', timeSlot)
		// 处理用户未主动选择日期的情况
		if (!userHasSelected.value && props.isDay) {
			const today = dayjs().format('YYYY/MM/DD')

			if (props.mode === 'single') {
				dateSelection.value = today
			} else if (props.mode === 'multiple') {
				dateSelection.value = [today]
			} else if (props.mode === 'range') {
				dateSelection.value = {
					after: today,
					before: today,
					data: [today]
				}
			}
		}
		// 检查日期选择情况
		const hasValidDateSelection = (() => {
			switch (props.mode) {
				case 'single':
					return !!dateSelection.value

				case 'multiple':
					return Array.isArray(dateSelection.value) && dateSelection.value.length > 0

				case 'range':
					// 确保选择了范围且包含日期
					const isValid = !!dateSelection.value &&
						typeof dateSelection.value === 'object' &&
						dateSelection.value.data?.length > 0

					if (!isValid) return false

					// 额外检查是否允许同一天
					if (!props.allowSameDay && dateSelection.value.after === dateSelection.value.before) {
						return false
					}

					return true
				default:
					return false
			}
		})()
		// 如果没有有效日期选择
		if (!hasValidDateSelection) {
			let errorMessage = ''
			if (props.mode === 'range' && !props.allowSameDay &&
				dateSelection.value?.after === dateSelection.value?.before) {
				errorMessage = '请选择不同的开始和结束日期'
			} else {
				errorMessage = {
					single: '请选择一个日期',
					multiple: '请至少选择一个日期',
					range: '请选择一个时间范围'
				} [props.mode] || '请选择日期'
			}

			uni.showToast({
				title: errorMessage,
				icon: 'none'
			})
			return
		}

		// 验证时间段（如果开启）
		if (props.showTimeSlot && !timeSlot && props.timeSlotCannotNull) {
			return uni.showToast({
				title: '请至少选择一个时间段',
				icon: 'none'
			})
		}

		// 获取需要检查的日期
		const getDatesToCheck = () => {
			if (props.mode === 'range' && dateSelection.value) {
				// 处理起止时间相同的特殊情况
				const uniqueDates = [...new Set([
					dateSelection.value.after,
					dateSelection.value.before
				])].filter(Boolean)

				return uniqueDates.length > 0 ? uniqueDates : dateSelection.value.data
			} else if (props.mode === 'multiple') {
				return dateSelection.value
			} else {
				return [dateSelection.value]
			}
		}

		const datesToCheck = getDatesToCheck().filter(Boolean)
		const allDatesValid = datesToCheck.every(date => checkEarlyToday(date))

		if (!allDatesValid) {
			uni.showToast({
				title: '选择的日期不能早于今天',
				icon: 'none'
			})
			return
		}
		const getDateSlotData = () => {
			if (props.mode === 'range') {
				// 时间范围获取的是 data
				return dateSelection.value.data
			}
			return dateSelection.value
		}
		console.log('********:', getDateSlotData())
		// 发射事件
		emit('confirm', {
			dateSlot: getDateSlotData(),
			...(props.showTimeSlot && {
				timeSlot
			})
		})
	}
	// 检查dateSlot日期是否有效，如果大于2025年的，就是脏数据
	const isValidDate = (dateStr) => {
		if (!dateStr) return false

		try {
			const date = dayjs(dateStr, 'YYYY/MM/DD')
			// 检查年份是否早于2025年
			return date.isValid() && date.year() >= 2025
		} catch (e) {
			return false
		}
	}
	// 过滤脏数据
	const filterDirtyDates = (dates) => {
		// 如果没有配置过滤年份，直接返回原数据
		if (!props.filterDirtyDates) return dates

		const filterYear = parseInt(props.filterDirtyDates, 10)

		if (Array.isArray(dates)) {
			return dates.filter(dateStr => {
				try {
					const date = dayjs(dateStr, 'YYYY/MM/DD')
					return date.isValid() && date.year() >= filterYear
				} catch (e) {
					return false
				}
			})
		} else if (typeof dates === 'string') {
			try {
				const date = dayjs(dates, 'YYYY/MM/DD')
				return date.isValid() && date.year() >= filterYear ? dates : ''
			} catch (e) {
				return ''
			}
		}
		return dates
	}
	onMounted(() => {
		console.log('**********:', dayjs(0, 'YYYY/MM/DD'))
		console.log('onMounted:', props.mode, props.dateSlot)
		// 过滤脏数据
		const cleanDateSlot = filterDirtyDates(props.dateSlot)
		// 处理传入的日期
		if (props.mode === 'single' && cleanDateSlot) {
			defaultDate.value = dayjs(cleanDateSlot).format('YYYY/MM/DD')
			currentDayjs.value = dayjs(cleanDateSlot)
			dateSelection.value = defaultDate.value
			// 标记初始状态
			userHasSelected.value = !!dateSelection.value;
		}

		if (props.mode === 'multiple' && Array.isArray(cleanDateSlot) && cleanDateSlot.length) {
			defaultDate.value = cleanDateSlot.map(d => dayjs(d).format('YYYY/MM/DD'))
			currentDayjs.value = dayjs(cleanDateSlot[0])
			dateSelection.value = defaultDate.value
			// 标记初始状态
			userHasSelected.value = !!dateSelection.value;
		}

		console.log('onMounted cleanDateSlot:', cleanDateSlot)
		console.log('onMounted defaultDate.value:', defaultDate.value)

		// 初始化时间选择
		activeTimeId.value = checkBinary(props.timeSlot)
	})
</script>

<script>
	export default {
		options: {
			styleIsolation: "shared"
		}
	}
</script>

<style lang='scss' scoped>
	.dust-calendar {
		width: 100%;
		box-sizing: border-box;
		padding: 20rpx 40rpx 28rpx;

		.header {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.item {
				width: 166rpx;
				height: 80rpx;
				display: flex;
				gap: 14rpx;
				align-items: center;
				justify-content: center;
				font-size: 28rpx;
				color: #1F1F1F;
				line-height: 48rpx;

				.icon {
					width: 40rpx;
					height: 40rpx;
				}
			}
		}

		.time-list {
			display: flex;
			align-items: center;
			gap: 24rpx;
			margin-top: 48rpx;

			.activeTiem {
				background-color: #fe58b7;

				text {
					color: #fff;
				}
			}

			view {
				display: flex;
				flex-direction: column;
				flex: 1;
				height: 72rpx;
				background: #f8f7fa;
				border-radius: 16rpx;
				justify-content: center;
				align-items: center;

				text {
					font-size: 24rpx;
					color: #1f1f1f;
				}

				text:nth-child(2) {
					font-size: 20rpx;
				}
			}
		}
	}

	.footer {
		width: 100%;
		box-sizing: border-box;
		gap: 24rpx;
		margin-top: 40rpx;
		padding-top: 20rpx;
		border-top: 2rpx solid #EEEEEE;
		;

		.btn {
			flex: 1;
			height: 78rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 40rpx;
			font-weight: 700;
			font-size: 32rpx;
			color: #1F1F1F;

			&.primary {
				background: #FE58B7;
				color: #fff;
			}

			&.secondary {
				background-color: #eee;
			}
		}
	}

	:deep() {
		.uv-calendar__header {
			display: none !important;
		}

		.uv-calendar-item__weeks-box {
			border-radius: 80rpx;
		}

		.uv-calendar-item__weeks-box-item {
			height: 80rpx !important;
			width: 96rpx !important;
		}

		.uv-calendar-item__weeks-lunar-text {
			display: none !important;
		}

		.uv-calendar__weeks-day {
			border: none;
		}
	}
</style>