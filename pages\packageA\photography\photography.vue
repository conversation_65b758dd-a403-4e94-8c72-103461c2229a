<template>
	<z-paging class="app-container" ref="paging" v-model="dataList" @query="queryList" :safe-area-inset-bottom="dataList.length > 0" :use-safe-area-placeholder="dataList.length > 0">
		<template #top>
			<Navbar :type="1" leftWidth="74px" :showSearch="true" placeholder="漫展" :leftText="titName"></Navbar>
			<!-- <view class="filtrate">
				<view class="filtrateListBox" v-for="(item, index) in filtrateList" :key="index" @click="filtrateShow(item.value)">
					<text>{{ item.name }}</text>
					<view class="triangle"></view>
				</view>
			</view> -->
			<view class="filtrate">
				<view v-for="(item, index) in filtrateList" :key="index">
					<view v-if="index == 0 && expo_id != null"></view>
					<view v-else class="filtrateListBox" @click="filtrateShow(item.value)">
						<text>{{ item.name }}</text>
						<view class="triangle"></view>
					</view>
				</view>
			</view>
			<!-- <view class="tabs">
				<dust-tabs v-model="activeTab" :list="tabList" tabType="secondary" lineGradientBorder showActiveStar></dust-tabs>
			</view> -->
		</template>
		<uv-overlay :show="overlayShow">
			<Navbar :type="1" :showSearch="true" :leftText="titName"></Navbar>
			<view class="filtrate">
				<view v-for="(item, index) in filtrateList" :key="index">
					<view v-if="index == 0 && expo_id != null"></view>
					<view v-else class="filtrateListBox" :class="filtrateActive == index ? 'filtrateListBoxActive' : ''" @click="filtrateBoxShow(item.value)">
						<text>{{ item.name }}</text>
						<view class="triangle" :class="filtrateActive == index ? 'triangleActive' : ''"></view>
					</view>
				</view>
			</view>
			<view class="filtrateWindow">
				<view class="filtrateWindowContent">
					<view class="filtrateList" v-show="filtrateActive == 0">
						<scroll-view scroll-y="true" style="height: 672rpx">
							<view class="region-box" v-for="(item, index) in regionList" :key="index" @click="activeRegion(index)">
								<text :style="regionId.includes(item.id) ? 'color:#FE58B7;' : ''">{{ item.name }}</text>
								<image v-if="regionId.includes(item.id)" src="../static/photography/<EMAIL>" mode=""></image>
							</view>
						</scroll-view>
					</view>
					<view class="filtrateList" v-show="filtrateActive == 1">
						<view class="region-box" v-for="(item, index) in category" :key="index" @click="activeCategory(index)">
							<text :style="categoryId == item.id ? 'color:#FE58B7;' : ''">{{ item.name }}</text>
							<image v-if="categoryId == item.id" src="../static/photography/<EMAIL>" mode=""></image>
						</view>
					</view>
					<view class="date" v-show="filtrateActive == 2">
						<view class="dateCalendar" v-if="is_daily == 'true'">
							<uv-calendars mode="multiple" insert :showMonth="false" ref="calendar" :date="date" @change="calendarChange" :startDate="startDate"></uv-calendars>
						</view>
						<view class="arrCalendar" v-else>
							<scroll-view enable-flex scroll-y="true" class="arrCalendarScroll" style="height: 100%">
								<view class="scroll">
									<view
										v-for="(item, index) in arrDate"
										:key="index"
										@click="activecalendarsBox(item.time, index)"
										class="calendarsBox"
										:class="item.choose == true ? 'activeColor' : 'calendarsBoxColor'"
									>
										<text>{{ item.year }}</text>
										<text>{{ item.txt }}</text>
									</view>
								</view>
							</scroll-view>
						</view>
						<view class="tiemList">
							<view v-for="(item, index) in timeArr" :key="index" :class="activeTimeId[index] == item.value ? 'activeTiem' : ''" @click="addTime(index)">
								<text>{{ item.name }}</text>
								<text>{{ item.time }}</text>
							</view>
						</view>
					</view>
					<view class="filtrateList" v-show="filtrateActive == 3">
						<view class="region-box" v-for="(item, index) in sexList" :key="index" @click="activeSex(index)">
							<text :style="sexId == index ? 'color:#FE58B7;' : ''">{{ item }}</text>
							<image v-if="sexId == index" src="../static/photography/<EMAIL>" mode=""></image>
						</view>
					</view>
				</view>
				<view class="filtrateWindowBottom">
					<button @click="filtrateReset()">
						<text>取消</text>
					</button>
					<button @click="search()">
						<text>确认</text>
					</button>
				</view>
			</view>
		</uv-overlay>

		<template #empty>
			<!-- <uv-empty /> -->
			<empty-list>
				<text>这里还暂无服务哦 \n 点击首页发布，来添加第一个服务吧！</text>
			</empty-list>
		</template>
		<uni-list style="margin-top: 20rpx" :border="false">
			<uni-list-item v-for="(item, index) in dataList" :key="item.id" :border="false">
				<template v-slot:body>
					<view class="listBox" @click="toDetails(item.id)">
						<view class="listBox-top" style="position: relative">
							<dust-image-base :src="item.cover" :local="false" aspect-ratio="16:9"></dust-image-base>
							<!-- <image :src="prefixFilePath(item.cover)" style="height: 338rpx; width: 100%" mode="aspectFit"></image> -->
							<view class="count">
								<text>NO: {{ item.index }}</text>
							</view>
						</view>
						<view class="listBox-body">
							<image :src="prefixFilePath(item.user_avatar)" class="userImg"></image>
							<view class="listBox-body-right">
								<view class="listBox-body-right-name">
									<text class="listBox-body-right-title textHide">{{ item.name }}</text>
									<text class="listBox-body-right-score" v-if="(item.score / 20).toFixed(1) == 0">暂无评分</text>
									<text class="listBox-body-right-score" v-else>{{ (item.score / 20).toFixed(1) }}分推荐</text>
								</view>
								<view class="detail" v-for="(item3, index3) in item.items" :key="index3">
									<text>
										<text class="price">￥</text>
										<text>{{ item3.price / 100 }}</text>
									</text>
									<text class="textHide">{{ item3.name }}</text>
								</view>
							</view>
						</view>
					</view>
				</template>
			</uni-list-item>
		</uni-list>
	</z-paging>
</template>

<script setup>
import dayjs from 'dayjs';
import { onMounted, ref } from 'vue';
import { getDimensionServices, dimensionCategory } from '@/api/dimensionService';
import { getSystemDistrict } from '@/api/common';
import { computed } from 'vue';
import { formatCountToString } from '@/common/utils';
import { useStore } from 'vuex';
const store = useStore();
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);
import { onLoad } from '@dcloudio/uni-app';
import { number } from '../../../uni_modules/uv-ui-tools/libs/function/test';
let titName = ref('约拍');
let type = ref(1);
const startDate = ref();
let is_daily = ref(false);
let expo_id = ref(null);
let arrDate = ref([]);
let activeArrDate = ref([]);
onLoad((e) => {
	type.value = e.type;
	is_daily.value = e.is_daily;
	if (e.type == 2) {
		titName.value = '约妆';
	}
	if (e.expo_id) {
		expo_id.value = e.expo_id;
	}
	const d = new Date();
	const year = d.getFullYear();
	let month = d.getMonth() + 1;
	const date = d.getDate();
	startDate.value = `${year}-${month}-${date}`;
	if (is_daily.value == 'false') {
		console.log(e.start_datetime, e.end_datetime);
		let start = dayjs(Number(e.start_datetime)).format('YYYY-MM-DD');
		let end = dayjs(Number(e.end_datetime)).format('YYYY-MM-DD');
		getArrDate(dayjs(start).valueOf(), dayjs(end).valueOf());
	}
});
/** 获取范围日期数组 */
function getArrDate(start, end) {
	let dayArr = [];
	let curr = start;
	let to = dayjs(dayjs().valueOf()).format('YYYY-MM-DD');
	let today = dayjs(to).valueOf();
	while (curr <= end) {
		if (curr >= today) {
			dayArr.push({ year: dayjs(curr).format('YYYY'), txt: dayjs(curr).format('MM-DD'), time: curr, choose: false });
		}
		curr = dayjs(curr).add(1, 'day').valueOf();
	}
	arrDate.value = dayArr;
}
let activeTime2 = ref([]);
function activecalendarsBox(time, index) {
	let arr = activeTime2.value;
	if (arrDate.value[index].choose) {
		arrDate.value[index].choose = false;
		arr.splice(arr.indexOf(time), 1);
	} else {
		arrDate.value[index].choose = true;
		arr.push(time);
	}
	activeTime2.value = arr;
	console.log(activeTime2.value);
}
function prefixFilePath(url) {
	if (!url) return '';
	return cloudFileUrl.value + url;
}
const timeArr = ref([
	{
		name: '上午',
		time: '8:00-12:00',
		value: 1
	},
	{
		name: '下午',
		time: '12:00-18:00',
		value: 2
	},
	{
		name: '晚上',
		time: '18:00-23:00',
		value: 4
	},
	{
		name: '夜间',
		time: '23:00-8:00',
		value: 8
	}
]);
let activeTimeId = ref([0, 0, 0, 0]);
function addTime(index) {
	if (activeTimeId.value[index] == timeArr.value[index].value) {
		activeTimeId.value.splice(index, 1, 0);
	} else {
		activeTimeId.value.splice(index, 1, timeArr.value[index].value);
	}
}
const sexList = ref(['不限', '男', '女']);
const sexId = ref(0);
const overlayShow = ref(false);
const scrollable = ref(true);
const date = ref([]);
const activeDate = ref([]);
const regionList = ref([{ name: '不限', id: 0 }]);
const regionId = ref([0]);
function activeRegion(index) {
	let i = regionId.value.indexOf(regionList.value[index].id);
	if (i > -1) {
		regionId.value.splice(i, 1);
		return;
	}
	if (regionList.value[index].id == 0) {
		regionId.value = [0];
		return;
	}
	let i2 = regionId.value.indexOf(0);
	if (i2 > -1) {
		regionId.value.splice(i2, 1);
	}
	regionId.value.push(regionList.value[index].id);
}
const category = ref([{ name: '不限', id: 0 }]);
const categoryId = ref(0);
function activeCategory(index) {
	categoryId.value = category.value[index].id;
}
const filtrateList = ref([
	{
		name: '区域',
		value: 0
	},
	{
		name: '类别',
		value: 1
	},
	{
		name: '日期',
		value: 2
	},
	{
		name: '性别',
		value: 3
	}
]);
onMounted(() => {
	getCategory();
	getAss();
});
function getCategory() {
	dimensionCategory().then((res) => {
		if (res.code === 20000) {
			res.data.forEach(function (e) {
				if (e.id == type.value) {
					category.value = [{ name: '不限', id: 0 }, ...e.types];
				}
			}, this);
		}
	});
}
let city = ref(null);
let province = ref(null);
//获取区县
function getAss() {
	if (expo_id.value != null) return;
	(city.value = useStore().state.location.city.id), (province.value = useStore().state.location.province.id);
	getSystemDistrict(useStore().state.location.city.id).then((res) => {
		regionList.value = [{ name: '不限', id: 0 }, ...res.data];
	});
}
function calendarChange(e) {
	activeDate.value = e.multiple.data;
}
const filtrateActive = ref(null);

function filtrateReset() {
	filtrateActive.value = null;
	overlayShow.value = false;
}

function filtrateShow(id) {
	overlayShow.value = !overlayShow.value;
	filtrateActive.value = id;
}

function filtrateBoxShow(id) {
	if (id == filtrateActive.value) {
		overlayShow.value = false;
	} else {
		filtrateActive.value = id;
	}
}
const tabActive = ref(0);
const paging = ref(null);
const dataList = ref([]);

function activeSex(index) {
	sexId.value = index;
}
const allData = ref([]); // 模拟数据
function back() {
	uni.navigateBack();
}

function tabClick(item) {
	tabActive.value = item.value;
}

function search() {
	paging.value.reload();
	filtrateReset();
}

async function queryList(pageNo, pageSize) {
	const params = {
		page: pageNo,
		size: 10,
		sex: sexId.value,
		categories: [type.value],
		is_daily: is_daily.value
	};

	if (expo_id.value != null) {
		params.expo_id = expo_id.value;
	} else {
		params.city = city.value;
		params.province = province.value;
	}
	if (categoryId.value != 0) {
		params.type = categoryId.value;
	}
	let sum = activeTimeId.value.reduce((a, b) => a + b);
	if (sum != 0) {
		params.time_slot = sum;
	}
	if (activeDate.value.length != 0) {
		let copyDate = [];
		for (const e of activeDate.value) {
			copyDate.push(Date.parse(new Date(e)));
		}
		copyDate = copyDate.join(',');
		params.datetime = copyDate;
	}
	if (activeTime2.value.length != 0) {
		let cDate = [];
		cDate = activeTime2.value;
		cDate = cDate.join(',');
		params.datetime = cDate;
	}
	let i = regionId.value.indexOf(0);
	if ((regionId.value.length > 0) & (i == -1)) {
		params.district = regionId.value.join(',');
	}
	let res = await getDimensionServices(params);
	if (res.code === 20000) {
		paging.value.complete(res.data.results);
	} else {
		paging.value.complete(false);
	}
}
//跳转详情
function toDetails(id) {
	uni.navigateTo({
		url: `/pages/packageA/photographyDetails/photographyDetails?id=${id}`
	});
}
</script>

<style lang="scss" scoped>
:deep(.uv-calendar-item__weeks-box-item) {
	height: 80rpx !important;
	width: 96rpx !important;
}

:deep(.uv-calendar-item__weeks-lunar-text) {
	display: none !important;
}

:deep(.uv-calendar__weeks-item) {
	flex: none !important;
}

:deep(.uv-calendar__weeks-day) {
	border: 0 !important;
	width: 96rpx !important;
	height: 80rpx !important;
	flex: none !important;
}

:deep(.uv-calendar__header) {
	border: 0 !important;
	height: 48rpx !important;
}

:deep(.uv-calendar__weeks-week) {
	padding: 0 !important;
}

:deep(.uv-calendar-item--isDay-text) {
	color: #fe58b7 !important;
}

:deep(.uv-calendar-item--multiple) {
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 80rpx !important;
	background-color: #fe58b7 !important;
	color: #000 !important;
}

:deep(.uv-calendar__backtoday) {
	display: none;
}

:deep(.uni-navbar__header-container) {
	align-items: center;
	padding: 0 30px 0 10px !important;
}

:deep(.uni-searchbar) {
	padding: 0 !important;
}

:deep(.uni-list-item__container) {
	padding: 0 26rpx;
	margin-bottom: 28rpx;
}

:deep(.dust-tabs-container) {
	.dust-tabs-item {
		&::after {
			border-top-left-radius: 0 !important;
			border-top-right-radius: 0 !important;
		}
	}
}
.count {
	position: absolute;
	display: flex;
	justify-content: center;
	align-items: center;
	bottom: 0;
	right: 0;
	// width: 174rpx;
	// height: 32rpx;
	background: rgba(0, 0, 0, 0.5);
	border-radius: 20rpx 0rpx 0rpx 0rpx;
	color: #fff;
	padding: 8rpx 10rpx 4rpx 10rpx;
	text {
		font-size: 20rpx;
	}
}
uni-search-bar {
	flex: 1;
}

.textHide {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.tabs {
	margin-left: 32rpx;
	margin-top: 34rpx;
	margin-bottom: 20rpx;
}

.listBox {
	width: 704rpx;
	// min-height: 560rpx;
	background: #f2f4f7;
	border-radius: 32rpx;
	overflow: hidden;
}

.listBox-body {
	padding-top: 16rpx;
	padding-bottom: 12rpx;
	width: 100%;
	// min-height: 205rpx;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
}

.userImg {
	width: 56rpx;
	height: 56rpx;
	margin-left: 22rpx;
	border-radius: 50%;
}

.listBox-body-right {
	width: 582rpx;
	margin-right: 26rpx;
}

.listBox-body-right-name {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.listBox-body-right-title {
	font-weight: 700;
	font-size: 32rpx;
	color: #3d3d3d;
	line-height: 44rpx;
	// flex:1;
	width: 438rpx;
}

.listBox-body-right-score {
	font-size: 28rpx;
	color: #fe58b7;
}

.address {
	margin-top: 8rpx;
	font-weight: 400;
	font-size: 28rpx;
	color: #aaaaaa;
	line-height: 40rpx;
	display: flex;
	align-items: center;
}

.address > image {
	margin-right: 8rpx;
	width: 28rpx;
	height: 28rpx;
}

.label {
	margin-top: 18rpx;
	margin-bottom: 20rpx;
	display: flex;
}

.labelBox {
	margin-right: 16rpx;
	padding: 0 16rpx;
	border-radius: 8rpx 8rpx 8rpx 8rpx;
	border: 2rpx solid #fab001;
	font-weight: 350;
	font-size: 24rpx;
	color: #fab001;
	line-height: 34rpx;
}

.detail {
	width: 100%;
	height: 42rpx;
	margin-bottom: 12rpx;
	display: flex;
	align-items: center;
}

.detail > text:nth-child(1) {
	font-weight: 700;
	font-size: 36rpx;
	color: #fe58b7;
	line-height: 34rpx;
	margin-right: 24rpx;
}

.price {
	font-size: 24rpx;
}

.detail > text:nth-child(2) {
	width: 464rpx;
	font-weight: 400;
	font-size: 28rpx;
	color: #3d3d3d;
	line-height: 40rpx;
}

.filtrate {
	padding-top: 32rpx;
	display: flex;
	background-color: #fff;
	padding-left: 24rpx;
	padding-right: 24rpx;
}

.filtrateListBox {
	margin-right: 24rpx;
	width: 124rpx;
	height: 60rpx;
	border-radius: 36rpx;
	background-color: #f7f8fa;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 20rpx;

	text {
		font-weight: 400;
		font-size: 28rpx;
		margin-right: 16rpx;
	}

	.triangle {
		border: solid;
		border-width: 15rpx 10rpx 15rpx 10rpx;
		border-color: transparent transparent #4e5969;
		margin-bottom: 10rpx;
	}

	.triangleActive {
		margin-top: 20rpx;
		margin-bottom: 0;
		transform: rotate(180deg);
	}
}

.filtrateListBoxActive {
	background-color: #d8d8d8;
}

.filtrateWindow {
	min-height: 830rpx;
	border-radius: 0rpx 0rpx 24rpx 24rpx;
	background-color: #fff;

	.filtrateWindowContent {
		.filtrateList {
			height: 672rpx;
			// overflow: auto;

			.region-box {
				height: 96rpx;
				display: flex;
				align-items: center;
				padding-left: 32rpx;
				padding-right: 32rpx;
				font-size: 32rpx;
				line-height: 45rpx;

				text {
					width: 630rpx;
				}

				image {
					width: 40rpx;
					height: 40rpx;
					margin-left: 16rpx;
				}
			}
		}

		.date {
			.dateCalendar {
				padding-left: 40rpx;
				margin-bottom: 20rpx;
			}
		}
	}

	.tiemList {
		padding-left: 40rpx;
		display: flex;
		justify-content: center;
		margin-bottom: 80rpx;

		.activeTiem {
			background-color: #fe58b7;
		}

		view {
			display: flex;
			flex-direction: column;
			width: 136rpx;
			height: 72rpx;
			background: #f8f7fa;
			border-radius: 16rpx;
			justify-content: center;
			align-items: center;
			margin-right: 40rpx;

			text {
				font-size: 24rpx;
				color: #1f1f1f;
			}

			text:nth-child(2) {
				font-size: 20rpx;
			}
		}
	}

	.filtrateWindowBottom {
		height: 158rpx;
		border-top: 2rpx solid #d8d8d8;
		display: flex;
		justify-content: space-between;
		align-items: center;

		button {
			width: 303rpx;
			height: 78rpx;
			background: #eeeeee;
			border-radius: 40rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-weight: 700;
			font-size: 32rpx;
			line-height: 46rpx;
		}

		button::after {
			border: none;
		}

		button:nth-child(1) {
			margin-left: 62rpx;
		}

		button:nth-child(2) {
			margin-right: 62rpx;
			color: #fff;
			background: #fe58b7;
		}
	}
}
.arrCalendar {
	width: 100%;
	height: 500rpx;
	margin-bottom: 20rpx;
	display: flex;
	justify-content: center;
}
.arrCalendarScroll {
	// white-space: nowrap;
	// display: flex;
	// flex-wrap: wrap;

	// grid-auto-rows: auto;
	// gap: 30px;
	// justify-content: space-between;
	display: flex;
	justify-content: center;
}
.scroll {
	width: 95%;
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
	gap: 20rpx;
	grid-auto-rows: 60rpx;
}
.activeColor {
	border: 1rpx solid #fe58b7;
	background-color: #fe58b7;
	color: #000000;
}
.calendarsBoxColor {
	border: 1rpx solid #a3a3a3;
	color: #a3a3a3;
}
.calendarsBox {
	display: inline-block;
	width: 100%;
	height: 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-top: 30rpx;
	text:nth-child(1) {
		font-size: 16rpx;
	}
	text:nth-child(2) {
		font-size: 24rpx;
	}
}
</style>
