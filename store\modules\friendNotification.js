import { timeToString } from '@/common/utils'
import { getFriendConversationList } from '@/api/notification'

const FRIEND_CONVERSATION_CACHE = 'FRIEND_CONVERSATION_CACHE';

const notificationStore = {
  namespaced: true,
  state: {
    friendConversationList: uni.getStorageSync(FRIEND_CONVERSATION_CACHE) || [],
    pendingMessages: [] // 缓存在好友列表加载前收到的消息
  },
  mutations: {
    updateFriendConversationList(state, list) {
      state.friendConversationList = list;
      uni.setStorageSync(FRIEND_CONVERSATION_CACHE, list);
      
      // 处理待处理的消息
      if (state.pendingMessages.length > 0) {
        state.pendingMessages.forEach(msg => {
          state.friendConversationList.forEach(item => {
            if (parseInt(item.friend.id) === parseInt(msg.fromId)) {
              item.count = (item.count || 0) + msg.count;
              if (msg.messageData) {
                item.message = msg.messageData;
                item.time = timeToString(msg.messageData.created_at, 5);
              }
            }
          });
        });
        state.pendingMessages = []; // 清空待处理消息
        uni.setStorageSync(FRIEND_CONVERSATION_CACHE, state.friendConversationList);
      }
    },
    updateFriendUnreadCount(state, { fromId, count, messageData }) {
      const fromIdNum = parseInt(fromId);
      const friend = state.friendConversationList.find(item => parseInt(item.friend.id) === fromIdNum);
      
      if (friend) {
        // 找到好友，直接更新
        const oldCount = friend.count || 0;
        friend.count = oldCount + count;
        if (messageData) {
          friend.message = messageData;
          friend.time = timeToString(messageData.created_at, 5);
        }
        uni.setStorageSync(FRIEND_CONVERSATION_CACHE, state.friendConversationList);
      } else {
        // 好友列表还没加载，缓存消息
        state.pendingMessages.push({ fromId: fromIdNum, count, messageData });
        console.log('缓存待处理消息：', { fromId: fromIdNum, count });
      }
    },
    clearFriendUnreadCount(state, fromId) {
      const fromIdNum = parseInt(fromId);
      state.friendConversationList.forEach(item => {
        if (parseInt(item.friend.id) === fromIdNum) {
          item.count = 0;
        }
      });
      uni.setStorageSync(FRIEND_CONVERSATION_CACHE, state.friendConversationList);
    }
  },
  actions: {
    updateFriendUnreadCount({ commit }, payload) {
      commit('updateFriendUnreadCount', payload);
    },
    async refreshConversationList({ state, commit, dispatch }) {
      try {
        const res = await getFriendConversationList();
        if (res.code === 20000) {
          const oldList = state.friendNotification.friendConversationList;
          const newList = res.data;
          
          // 找出新增的conversation
          const oldConversationIds = oldList.map(item => item.id);
          const newConversations = newList.filter(item => !oldConversationIds.includes(item.id));
          
          // 更新conversation list
          commit('updateFriendConversationList', newList);
          
          
          // 如果有新conversation，触发事件通知notification页面（如果在该页面）
          if (newConversations.length > 0) {
            newConversations.forEach(conversation => {
              uni.$emit('notification:new-conversation', conversation.id);
            });
          }

          return newConversations;
        }
      } catch (error) {
        console.log('刷新conversation list失败:', error);
        return [];
      }
    }
  }
};

export default notificationStore; 