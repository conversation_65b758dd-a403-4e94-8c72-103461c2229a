<template>
	<z-paging class="app-container" ref="paging" v-model="dataList" @query="queryList"
		:safe-area-inset-bottom="dataList.length > 0" :use-safe-area-placeholder="dataList.length > 0">
		<template #top>
			<Navbar :type="1" leftText="约团"></Navbar>
			<view class="filtrate">
				<view v-for="(item, index) in filtrateList" :key="index">
					<view class="filtrateListBox" @tap="filtrateShow(item.value)">
						<text>{{ item.name }}</text>
						<view class="triangle"></view>
					</view>
				</view>
			</view>
		</template>
		<template #empty>
			<empty-list>
				<text>这里还暂无约团哦 \n 点击首页发布，来添加第一个约团吧！</text>
			</empty-list>
		</template>
		<uni-list :border="false">
			<uni-list-item v-for="(item, index) in dataList" :key="item.id" :border="false">
				<template v-slot:body>
					<view class="listBox" @tap="toDetails(item)">
						<view class="listBox-top" style="position: relative">
							<dust-image-base :src="item.cover" :local="false" aspect-ratio="16:9"></dust-image-base>
							<view class="look-bar">
								<image class="icon-hq" src="/static/icon/dust_icon_view.svg"></image>
								<text>{{formatCountToString(item.view_count)}}</text>
							</view>
						</view>
						<view class="header-title">
							<text class="title">{{item.title}}</text>
						</view>
						<view class="user-bar">
							<view class="time-bar">
								<view class="flex items-center">
									<image class="icon-sj" src="/static/img/exhibition-details/icon_sj.png"></image>
									<text>{{dayjs(item.created_at).format('YYYY.MM.DD')}}</text>
								</view>
								<view class="flex items-center">
									<image class="icon-jd" src="/static/icon/<EMAIL>"></image>
									<text>{{item.members.length}}/{{item.max_member_count}}</text>
								</view>
							</view>
							<view class="user-info">
								<image :src="prefixFilePath(item.creator.avatar)" class="userImg"></image>
								<text>{{item.creator.nickname || item.creator.name || item.creator.account}}</text>
							</view>
						</view>
						<view class="labels">
							<view class="label-item" v-for="(itm, idx) in item.tags" :key="idx">
								<text>{{itm}}</text>
							</view>
						</view>
						<view class="line" />
						<view class="characters">
							<view class="characters-item" v-for="(itm, idx) in item.roles" :key="idx">
								<text>{{itm.name}}</text>
							</view>
						</view>
					</view>
				</template>
			</uni-list-item>
		</uni-list>
	</z-paging>
	<uv-overlay :show="overlayShow">
		<Navbar :type="1" leftText="约团"></Navbar>
		<view class="filtrate">
			<view v-for="(item, index) in filtrateList" :key="index">
				<view class="filtrateListBox" :class="filtrateActive == index ? 'filtrateListBoxActive' : ''"
					@tap="filtrateBoxShow(item.value)">
					<text>{{ item.name }}</text>
					<view class="triangle" :class="filtrateActive == index ? 'triangleActive' : ''"></view>
				</view>
			</view>
		</view>
		<view class="filtrateWindow">
			<view class="filtrateWindowContent">
				<view class="filtrateList" v-show="filtrateActive == 0">
					<view class="region-box" v-for="(item, index) in statusList" :key="index" @tap="activeStatus(item)">
						<text :style="statusId == item.id ? 'color:#FE58B7;' : ''">{{ item.name }}</text>
						<image v-if="statusId == item.id" src="../static/photography/<EMAIL>" mode=""></image>
					</view>
				</view>
				<view class="filtrateList" v-show="filtrateActive == 1">
					<view class="region-box" v-for="(item, index) in dateList" :key="index" @tap="activeDate(item)">
						<text :style="dateId == item.id ? 'color:#FE58B7;' : ''">{{ item.name }}</text>
						<image v-if="dateId == item.id" src="../static/photography/<EMAIL>" mode=""></image>
					</view>
				</view>
				<view class="filtrateList" v-show="filtrateActive == 2">
					<view class="region-box" v-for="(item, index) in propertyList" :key="index"
						@tap="activeProperty(item.id)">
						<text :style="isPropertyActive(item.id) ? 'color:#FE58B7;' : ''">{{ item.name }}</text>
						<image v-if="isPropertyActive(item.id)" src="../static/photography/<EMAIL>" mode="">
						</image>
					</view>
				</view>
			</view>
			<view class="filtrateWindowBottom">
				<button @tap="filtrateReset">
					<text>取消</text>
				</button>
				<button @tap="search">
					<text>确认</text>
				</button>
			</view>
		</view>
	</uv-overlay>
</template>

<script setup>
	import {
		ref,
		computed,
	} from 'vue'
	import {
		useStore
	} from 'vuex';
	import {
		onLoad
	} from '@dcloudio/uni-app';
	import dayjs from 'dayjs';
	import {
		getHomeTroupeListApi
	} from '@/api/troupe'
	import {
		formatCountToString
	} from '@/common/utils'

	const store = useStore();
	const userInfo = computed(() => store.state.userInfo)
	const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);
	const dataList = ref([])
	const paging = ref(null)
	const filtrateList = ref([{
			name: '剧团状态',
			value: 0
		},
		{
			name: '执行时间',
			value: 1
		},
		{
			name: '剧团属性',
			value: 2
		},
	]);
	const filtrateActive = ref(null);
	const overlayShow = ref(false);
	const statusList = ref([{
		id: 1,
		name: '招募中',
	}, {
		id: 2,
		name: '已满员',
	}, {
		id: 3,
		name: '已停止',
	}])
	const statusId = ref(-1)
	const propertyList = ref([{
		id: 1,
		name: '多角色|多服装',
	}, {
		id: 2,
		name: '多角色|单服装',
	}, {
		id: 3,
		name: '单角色|多服装',
	}, {
		id: 4,
		name: '单角色|单服装',
	}])
	const propertyId = ref([])
	// 检查属性是否被选中
	const isPropertyActive = (id) => propertyId.value.includes(id)
	// const activeDate = ref('');
	const dateList = ref([{
		id: 1,
		name: '最先发布',
	}, {
		id: 2,
		name: '最新发布',
	}])
	const dateId = ref(2)

	// function calendarChange(e) {
	// 	console.log('calendarChange:', e)
	// 	activeDate.value = e.fulldate;
	// }

	function filtrateReset() {
		filtrateActive.value = null;
		overlayShow.value = false;
	}

	function filtrateShow(id) {
		overlayShow.value = !overlayShow.value;
		filtrateActive.value = id;
	}

	function filtrateBoxShow(id) {
		if (id == filtrateActive.value) {
			overlayShow.value = false;
		} else {
			filtrateActive.value = id;
		}
	}

	function search() {
		paging.value.reload();
		filtrateReset();
	}
	// 剧团状态
	function activeStatus(item) {
		statusId.value = item.id;
	}
	// 发布时间
	function activeDate(item) {
		dateId.value = item.id;
	}
	// 剧团属性
	function activeProperty(id) {
		const index = propertyId.value.indexOf(id)
		if (index > -1) {
			// 取消选中
			propertyId.value.splice(index, 1)
		} else {
			// 选中新选项
			propertyId.value.push(id)
		}
	}

	async function queryList(pageNo, pageSize) {
		const params = {
			page: pageNo,
			size: 10,
			type: 'all',
			property: propertyId.value,
		}
		if (statusId.value > -1) {
			params.status = statusId.value
		}
		if (dateId.value > -1) {
			params.order_by = dateId.value === 2 ? '-start_at' : '-created_at'
		}
		console.log('******params:', params)
		const res = await getHomeTroupeListApi(params)
		console.log('getHomeTroupeListApi:', res)
		if (res.code === 20000) {
			paging.value.complete(res.data.results)
		} else {
			paging.value.complete([])
		}
	}

	function prefixFilePath(url) {
		if (!url) return '';
		if (/^https?:\/\//i.test(url)) {
			return url;
		}
		if (url.startsWith('//')) {
			return window.location.protocol + url;
		}
		if (url.startsWith('/')) {
			return window.location.origin + url;
		}
		return cloudFileUrl.value + url;
	}
	
	function toDetails(item) {
		uni.navigateTo({
			url: '/pages/packageA/group-booking/group-booking-detail?gid=' + item.id,
		})
	}
</script>

<style lang="scss" scoped>
	.listBox-top {
		position: relative;
	}

	.look-bar {
		position: absolute;
		right: 0;
		bottom: 0;
		padding: 0 16rpx;
		height: 32rpx;
		display: flex;
		align-items: center;
		gap: 8rpx;
		background: rgba(0, 0, 0, 0.5);
		border-radius: 20rpx 0rpx 0rpx 0rpx;
		color: #fff;
		font-size: 20rpx;

		.icon-hq {
			width: 24rpx;
			height: 24rpx;
		}
	}

	.filtrate {
		padding-top: 28rpx;
		display: flex;
		background-color: #fff;
		padding-left: 24rpx;
		padding-right: 24rpx;
		gap: 28rpx;
	}

	.filtrateListBox {
		// width: 124rpx;
		height: 60rpx;
		border-radius: 36rpx;
		background-color: #f7f8fa;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-right: 18rpx;
		padding-left: 16rpx;

		text {
			font-weight: 400;
			font-size: 28rpx;
			margin-right: 16rpx;
		}

		.triangle {
			border: solid;
			border-width: 12rpx 8rpx 12rpx 8rpx;
			border-color: transparent transparent #4e5969;
			position: relative;
			top: -6rpx;
		}
		
		.triangleActive {
			margin-top: 20rpx;
			margin-bottom: 0;
			transform: rotate(180deg);
		}
	}

	.filtrateListBoxActive {
		background-color: #d8d8d8;
	}

	.filtrateWindow {
		min-height: 830rpx;
		border-radius: 0rpx 0rpx 24rpx 24rpx;
		background-color: #fff;

		.filtrateWindowContent {
			.filtrateList {
				height: 672rpx;
				// overflow: auto;

				.region-box {
					height: 96rpx;
					display: flex;
					align-items: center;
					padding-left: 32rpx;
					padding-right: 32rpx;
					font-size: 32rpx;
					line-height: 45rpx;

					text {
						width: 630rpx;
					}

					image {
						width: 40rpx;
						height: 40rpx;
						margin-left: 16rpx;
					}
				}
			}

			.date {
				.dateCalendar {
					margin-bottom: 20rpx;
				}
			}
		}

		.tiemList {
			padding-left: 40rpx;
			display: flex;
			justify-content: center;
			margin-bottom: 80rpx;

			.activeTiem {
				background-color: #fe58b7;
			}

			view {
				display: flex;
				flex-direction: column;
				width: 136rpx;
				height: 72rpx;
				background: #f8f7fa;
				border-radius: 16rpx;
				justify-content: center;
				align-items: center;
				margin-right: 40rpx;

				text {
					font-size: 24rpx;
					color: #1f1f1f;
				}

				text:nth-child(2) {
					font-size: 20rpx;
				}
			}
		}

		.filtrateWindowBottom {
			height: 158rpx;
			border-top: 2rpx solid #d8d8d8;
			display: flex;
			justify-content: space-between;
			align-items: center;

			button {
				width: 303rpx;
				height: 78rpx;
				background: #eeeeee;
				border-radius: 40rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-weight: 700;
				font-size: 32rpx;
				line-height: 46rpx;
			}

			button::after {
				border: none;
			}

			button:nth-child(1) {
				margin-left: 62rpx;
			}

			button:nth-child(2) {
				margin-right: 62rpx;
				color: #fff;
				background: #fe58b7;
			}
		}
	}

	.listBox {
		width: 704rpx;
		// min-height: 560rpx;
		background: #f2f4f7;
		overflow: hidden;
		border-radius: 0 0 32rpx 32rpx;

		.header-title {
			padding: 20rpx 24rpx 24rpx;
		}

		.title {
			font-weight: 700;
			font-size: 36rpx;
			color: #1F1F1F;
			line-height: 50rpx;
		}

		.line {
			height: 0rpx;
			border: 2rpx solid #D8D8D8;
			margin: 24rpx 24rpx 0;
		}
	}

	.user-bar {
		padding: 0 24rpx 12rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;

		.time-bar {
			display: flex;
			flex-direction: column;
			gap: 8rpx;
			font-size: 28rpx;
			color: #3D3D3D;

			.icon-sj {
				width: 25.98rpx;
				height: 28rpx;
				margin-right: 8rpx;
			}

			.icon-jd {
				width: 25.87rpx;
				height: 28rpx;
				margin-right: 8rpx;
			}
		}

		.user-info {
			display: flex;
			align-items: center;
			font-weight: 500;
			font-size: 32rpx;
			color: #1F1F1F;

			.userImg {
				width: 56rpx;
				height: 56rpx;
				margin-right: 8rpx;
				border-radius: 50%;
			}
		}
	}

	.labels {
		display: flex;
		align-items: center;
		gap: 16rpx;
		padding: 12rpx 24rpx 0;

		.label-item {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 52rpx;
			padding: 0 16rpx;
			background: #FE58B7;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			font-size: 24rpx;
			color: #FFFFFF;
		}
	}

	.characters {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 20rpx;
		padding: 24rpx 24rpx 32rpx;

		.characters-item {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 52rpx;
			padding: 0 16rpx;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			border: 2rpx solid #48DAEA;
			font-size: 24rpx;
			color: #48DAEA;
		}
	}
</style>