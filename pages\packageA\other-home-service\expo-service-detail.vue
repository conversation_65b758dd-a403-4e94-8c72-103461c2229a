<template>
  <z-paging class="app-container" :safe-area-inset-bottom="true" :use-safe-area-placeholder="true">
    <template #top>
      <Navbar :type="1" leftText="展会详情"></Navbar>
    </template>

    <view class="content">
      <dust-image-base wrapperRadius="20rpx" aspectRatio="16:9" :local="false"
        :src="prefixFilePath(expoDetailInfo.cover)"></dust-image-base>

      <view class="expo-title">
        {{ expoDetailInfo.name }}
      </view>

      <view class="service-wrapper">
        <view class="title">服务详情</view>

        <uni-list class="dust-uni-list" :border="false">
          <uni-list-item :border="false" class="dust-uni-list-item" v-for="(item, index) in detailInfo.items"
            :key="item.id">
            <template v-slot:body>
              <view class="item-wrapper" :class="{ active: selectedServiceItemId === item.id }" @click="handleServiceItemClick(item)">
                <view class="title">{{ item.name }}</view>
                <view class="combo-description">商家保证金{{ item.bond }}%</view>
                <view class="combo-description">
                  <text>参考价：</text>
                  <view class="price-wrapper">
                    <text class="prefix">￥</text>
                    <text class="price">{{ item.price / 100 }}</text>
                  </view>
                </view>
              </view>
            </template>
          </uni-list-item>
        </uni-list>
      </view>

      <view class="time-title">{{ typeMap[type] || '' }}时间</view>

      <view class="time-picker-list">
        <view class="item" v-for="date in expoDates" :key="date.label" :class="{ active: selectedDates.includes(date.value) }"
          @click="handleSelectDate(date.value)">{{ date.label }}</view>
      </view>
    </view>

    <template #bottom>
      <view class="footer-wrapper">
        <view class="btn-submit" @click="handleSubmit">
          立即咨询
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app'
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { getDimensionServiceDetail } from '@/api/dimensionService'
import { getDaysBetweenDates } from '@/pages/packageA/service-expo/hooks/useExpo';
import { getExhibitionDetail } from '@/api/exhibitionExpo'
import { createConsult, putConsult } from '@/api/notification';
import dayjs from 'dayjs'

const store = useStore();

function prefixFilePath(url) {
  if (!url) return '';
  return store.state.common.cloudFileUrl + url;
}
const typeMap = {
  'photography': '约拍',
  'makeup': '约妆',
}
const type = ref('')
const detailInfo = ref({})
const expoDetailInfo = ref({})
const serviceId = ref('')
const expoId = ref('')

const expoDates = ref([])

const selectedDates = ref([])
const selectedServiceItemId = ref('')

// 计算选中日期的显示文案
const selectedDateText = computed(() => {
  if (!selectedDates.value || selectedDates.value.length === 0) return ''
  
  if (selectedDates.value.length === 1) {
    return dayjs(selectedDates.value[0]).format('MM月DD日')
  }
  
  // 对日期进行排序
  const sortedDates = [...selectedDates.value].sort((a, b) => dayjs(a).valueOf() - dayjs(b).valueOf())
  
  // 按月份分组
  const monthGroups = {}
  sortedDates.forEach(date => {
    const month = dayjs(date).format('MM')
    const day = dayjs(date).format('DD')
    if (!monthGroups[month]) {
      monthGroups[month] = []
    }
    monthGroups[month].push(day)
  })
  
  // 生成显示文本
  const monthTexts = []
  Object.keys(monthGroups).forEach(month => {
    const days = monthGroups[month].join('、')
    monthTexts.push(`${month}月${days}日`)
  })
  
  return monthTexts.join('，')
})

function handleSelectDate(date) {
  if (selectedDates.value.includes(date)) {
    selectedDates.value = selectedDates.value.filter(item => item !== date)
  } else {
    selectedDates.value.push(date)
  }
}

onLoad((options) => {
  type.value = options.type
  serviceId.value = options.serviceId
  expoId.value = options.expoId

  Promise.all([fetchDimensionServiceDetail(), fetchExpoDetail()])
})

async function fetchDimensionServiceDetail() {
  try {
    const res = await getDimensionServiceDetail(serviceId.value)
    if (res.code === 20000) {
      detailInfo.value = res.data
    }
  } catch (error) {
    console.log(error, 'fetchDimensionServiceDetail error')
  }
}

async function fetchExpoDetail() {
  try {
    const res = await getExhibitionDetail(expoId.value)
    if (res.code === 20000) {
      expoDetailInfo.value = res.data
      expoDates.value = getDaysBetweenDates(res.data.start_datetime, res.data.end_datetime)
    }
  } catch (error) {
    console.log(error, 'fetchExpoDetail error')
  }
}

function handleServiceItemClick(item) {
  selectedServiceItemId.value = item.id
}

function handleSubmit() {
  const serviceItemId = selectedServiceItemId.value || detailInfo.value.items[0].id
  const dateMessage = selectedDateText.value
  const message = dateMessage ? `你好，问下这个漫展“${expoDetailInfo.value.name}”${dateMessage}这些天的套餐服务情况` : `你好，问下这个漫展“${expoDetailInfo.value.name}”的套餐服务情况`
  if(serviceItemId) {
    createConsult(serviceItemId).then((res) => {
      if(res.code === 20000) {
        putConsult(serviceItemId, res.data.id).then((res2) => {
          if(res2.code === 20000) {
            uni.navigateTo({
              url: `/pages/tabbar/notification/ordersNotificationDetails?type=1&consultid=${res.data.id}&message=${encodeURIComponent(message)}`
            })
          }
        })
      }
    })
  }
}

</script>

<style scoped lang="scss">
.content {
  flex: 1;
  box-sizing: border-box;
  padding: 0 24rpx 50rpx;

  .expo-title {
    font-weight: 700;
    font-size: 36rpx;
    color: #3D3D3D;
    margin-top: 40rpx;
  }

  .service-wrapper {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    margin-top: 40rpx;

    .title {
      font-weight: 500;
      font-size: 32rpx;
      color: #3D3D3D;
    }

    .item-wrapper {
      width: 100%;
      background: #F7F8FA;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      padding: 24rpx;
      box-sizing: border-box;
      border: 2rpx solid transparent;

      &.active {
        border: 2rpx solid #FE58B7;
      }

      .combo-description {
        font-size: 28rpx;
        color: #85878D;
        line-height: 40rpx;
        display: flex;
        align-items: center;
        margin-top: 12rpx;

        .price-wrapper {
          display: flex;
          align-items: flex-end;
          font-weight: 700;
          line-height: 34rpx;
          color: #FE58B7;

          .prefix {
            font-size: 24rpx;
          }

          .price {
            font-size: 36rpx;
          }
        }
      }
    }
  }

  .time-title {
    font-weight: 500;
    font-size: 32rpx;
    color: #3D3D3D;
    margin-top: 40rpx;
  }

  .time-picker-list {
    display: flex;
    flex-wrap: wrap;
    gap: 24rpx;
    margin-top: 24rpx;

    .item {
      width: 100rpx;
      height: 40rpx;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      border: 2rpx solid #AAAAAA;
      font-size: 24rpx;
      color: #85878D;
      text-align: center;
      line-height: 40rpx;
      position: relative;

      &.active {
        background: #FE58B7;
        color: #fff;
        border-color: transparent;
      }

      // 当前时间爆满的样式
      // &.active {
      //   border: 2rpx solid #FE1E42;

      //   &::before {
      //     content: '满';
      //     width: 36rpx;
      //     height: 36rpx;
      //     background: #FE1E42;
      //     border-radius: 50%;
      //     color: #fff;
      //     font-size: 24rpx;
      //     line-height: 32rpx;
      //     text-align: center;
      //     position: absolute;
      //     right: -18rpx;
      //     top: -50%;
      //   }
      // }
    }
  }
}

.footer-wrapper {
  box-sizing: border-box;
  padding: 0 24rpx;

  .btn-submit {
    text-align: center;
  }
}
</style>
