<template>
	<view class="container">
		<z-paging ref="paging" :fixed="true" 
			v-model="dataList" @query="queryList"
			:emptyViewSuperStyle="{'position': 'absolute', 'width': '100%', 'height': '100%'}"
		>
			<template #top>
				<Navbar :type="1" leftText="常见问题"></Navbar>
				<view class="header">
					<uni-search-bar placeholder="搜索问题" v-model="keyword" @cancel="cancelSearch" @confirm="searchConfirm">
						<template #searchIcon>
							<image class="icon-search" src="/static/icon/<EMAIL>" />
						</template>
					</uni-search-bar>
				</view>
			</template>
			
			<view class="main-container">

				<view class="row" v-for="item in dataList" :key="item.key">
					<view class="row-title" @click="selectTitleHandle(item)">
						<text class="row-label">{{item.title}}</text>
						<view class="btn-container">
							<uni-icons class="icon" v-if="!item.selected" type="right" color="#c0c4cc" />
							<uni-icons class="icon" v-else type="down" color="#c0c4cc" />
						</view>
					</view>
					<view class="row-detail" v-if="item.selected" v-for="(inner, index) in item.children" :key="inner.key" @click="selectDetailHandle(inner, item)">
						<text class="row-label">{{inner.title}}</text>
						<view class="btn-container">
							<uni-icons class="icon" v-if="inner.value" type="right" color="#c0c4cc" />
							<uni-icons class="icon" v-else type="down" color="#c0c4cc" />
						</view>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup lang="ts">
import { onMounted, computed, ref } from 'vue';
import { getQuestionList } from '@/api/user';

type RequestParams = Parameters<typeof getQuestionList>[0];
type Response = ReturnType<typeof getQuestionList> extends Promise<infer T> ? T : never;

interface SelectedItem {
	title: string;
	value: string;
	key: string
	selected: boolean;
	children?: SelectedItem[]
}

const keyword = ref<string>(undefined);
const paging = ref(null);
const dataList = ref<SelectedItem[]>([]);

function queryList(page: number, size: number) {
	const params: RequestParams = Object.assign({page,size}, keyword.value && {
		keyword: keyword.value
	});
	getQuestionList(params).then((res) => {
		const isSuccess = res.code === 20000;
		if (isSuccess) {
			const result: SelectedItem[] = res.data.results.map(item => {
				const children: SelectedItem[] = item.children.map(inner => ({title: inner.question, value: inner.answer, key: item.id, selected: false, children: []})) || [];
				return {title: item.question, value: item.answer, key: item.id, selected: false, children: children};
			});
			paging.value.complete(result);
		} else {
			paging.value.complete(false).catch(console.log);
			return uni.showToast({
				title: '搜索失败',
				icon: "error"
			})
		}
	});
}

const cancelSearch = () => {
	// 如果是删除按钮时keyword.value等于"",非空子串
	if (keyword.value === undefined) return;
	keyword.value = undefined;
	paging.value?.reload();
}
const searchConfirm = () => {
	paging.value?.reload();
};

const selectTitleHandle = (item: SelectedItem) => {
	const nextState = !item.selected;
	dataList.value.forEach(item => item.selected = false);
	item.selected = nextState;
};

const selectDetailHandle = (item: SelectedItem, paraentItem: SelectedItem) => {
	uni.navigateTo({
		url: "/pages/packageA/my-questions/detail?" + `title=${paraentItem.title}&value=${item.value}`
	})
};

</script>

<style scoped lang="scss">
	.container {
		height: 100%;
		width: 100%;
		.header {
			padding: 32rpx;
			:deep(.uni-searchbar) {
				padding: 0px;
			}
		}
		.main-container {
			padding: 0 32rpx;
			display: flex;
			flex-direction: column;
			row-gap: 24rpx;
		}
		
		.row {
			display: flex;
			flex-direction: column;
			gap: 34rpx;
			padding: 32rpx 28rpx;
			background-color: #F7F8FA ;
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			box-sizing: border-box;
			overflow: hidden;
			
			.row-title {
				display: flex;
				justify-content: space-between;
				align-items: center;
				color: #1F1F1F;
				
			}
			.row-detail {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding-left: 40rpx;
				color: #767676;
			}
			
			.row-label {
				flex: auto;
				font-size: 36rpx;
				line-height: 36rpx;
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
			}
			
			.btn-container {
				flex: none;
				.icon {
					width: 40rpx;
					height: 40rpx;
				}
			}
			
		}
		
	}

	.icon-search {
		width: 32rpx;
		height: 32rpx;
	}

</style>
