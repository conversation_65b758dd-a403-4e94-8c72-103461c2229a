import {
	ref,
	onMounted
} from 'vue'
import dayjs from 'dayjs'
import {
	updateDimensionService,
	getMineServiceDetail,
	createDimensionService
} from '@/api/mineService'

function debounce(func, wait) {
	let timeout = null;
	return function(...args) {
		if (timeout) {
			clearTimeout(timeout); // 每次触发都清除之前的计时器
		}
		timeout = setTimeout(() => {
			func.apply(this, args); // 等待时间到时执行
		}, wait);
	};
}


export const useExpo = ({
	from,
	expoId,
	serviceId,
	categoryId,
	expoDetail
}) => {
	const selectionDays = ref([])
	const serviceDetail = ref(null)
	const initServiceInfo = ref({
		cover: '',
		name: '',
		detail: '',
		parent_id: null
	})

	onMounted(() => {
		queryServiceDetail()
	})

	const isSameDay = (d) => {
		const d2 = new Date(d)
		return selectionDays.value.findIndex(item => {
			const d1 = new Date(item)
			return (
				d1.getFullYear() === d2.getFullYear() &&
				d1.getMonth() === d2.getMonth() &&
				d1.getDate() === d2.getDate()
			)
		})
	}

	const handleSelectDays = time => {
		if (isSameDay(time) !== -1) {
			selectionDays.value.splice(isSameDay(time), 1)
		} else {
			selectionDays.value.push(time)
		}
		updateExpoDays()
	}

	const updateExpoDays = debounce(async () => {
		console.log('updateExpoDays')
		if (from.value === 'add-combo' && !serviceId.value) {
			await createExpoService()
			return
		}

		const params = {
			...serviceDetail.value,
			date_slot: selectionDays.value,
		}
		try {
			if (!serviceId.value) return
			console.log('*****更新漫展服务信息params:', JSON.stringify(params))
			const res = await updateDimensionService(serviceId.value, params)
			if (res.code === 20000) {
				queryServiceDetail()
				uni.$emit('service-create-detail-expo:reload-data')
			}
		} catch (error) {
			console.log(error, 'createOrUpdateService')
		}
	}, 500)

	async function createExpoService() {
		try {
			const params = {
				clone_from_id: initServiceInfo.value.parent_id,
				cover: initServiceInfo.value.cover,
				name: initServiceInfo.value.name,
				editor: 'json',
				detail: initServiceInfo.value.detail,
				expo_id: +expoId.value,
				categories: [categoryId.value],
				type: 2,
				province_id: expoDetail.value.province.id,
				city_id: expoDetail.value.city.id,
				date_slot: selectionDays.value,
				// is_on_sale: true,
				locales: {
					[String(expoDetail.value.province.id)]: {
						[String(expoDetail.value.city.id)]: []
					}
				}
			}
			if (expoDetail.value?.district?.id) {
				params.district_id = expoDetail.value.district.id
			}
			console.log('******创建漫展服务信息params:', JSON.stringify(params))
			const res = await createDimensionService(params)
			if (res.code === 20000) {
				queryServiceDetail()
				uni.$emit('service-create-detail-expo:reload-data')
				uni.$emit('service-expo-add-combo:reload-hadSelectedExpo', +expoId.value)
				serviceId.value = res.data.id
			}
			return res.data.id
		} catch (error) {
			console.log(error, 'createDimensionService')
			return null
		}
	}

	async function queryServiceDetail() {
		if (!categoryId.value) return null
		try {
			const res = await getMineServiceDetail({
				categories: [categoryId.value],
			})
			if (res.code === 20000) {
				const list = res.data.results.filter(item => !item.is_daily)
				const initExpoServiceDetail = list.find(item => !item.expo)
				const detail = list.find(item => item.id === +serviceId.value)

				initServiceInfo.value = {
					cover: initExpoServiceDetail.cover,
					name: initExpoServiceDetail.name,
					detail: initExpoServiceDetail.detail,
					parent_id: initExpoServiceDetail.id
				}
				console.log('initServiceInfo.value:', initServiceInfo.value)
				if (detail) {
					selectionDays.value = detail.date_slot
					serviceDetail.value = detail
				}
			}
		} catch (error) {
			console.log(error, 'getMineServiceDetail service')
		}
	}


	return {
		isSameDay,
		handleSelectDays
	}
}

export function getDaysBetweenDates(startDate, endDate) {
	const start = dayjs(startDate);
	const end = dayjs(endDate);
	console.log('start:', start.format('YYYY.MM.DD'))
	console.log('end:', end.format('YYYY.MM.DD'))
	// 验证日期有效性
	if (!start.isValid() || !end.isValid()) return [];

	// 确保开始日期在结束日期之前
	if (start.isAfter(end)) {
		return getDaysBetweenDates(endDate, startDate);
	}

	// 获取今天的日期（用于过滤过去日期）
	const today = dayjs().startOf('day');

	// 计算所有日期
	const allDates = [];
	let currentDate = start.clone();

	// 收集所有日期（包括起止日期）
	while (currentDate.isBefore(end) || currentDate.isSame(end, 'day')) {
		// 只保留今天及以后的日期
		if (!currentDate.isBefore(today)) {
			allDates.push(currentDate.clone());
		}
		currentDate = currentDate.add(1, 'day');
	}

	// 最多取10个日期
	const maxDates = allDates.slice(0, 10);

	// 格式化结果
	const result = maxDates.map((date, index) => {
		const month = date.month() + 1; // 月份从1开始
		const day = date.date();

		return {
			label: `${month}.${day.toString().padStart(2, '0')}`,
			value: date.valueOf(), // 时间戳
			id: index
		};
	});
	console.log('*****result:', result)

	return result;
}