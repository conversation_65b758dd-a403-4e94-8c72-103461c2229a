<template>
	<view class="container">
		<editor id="editor" class="textarea"
			:placeholder="props.placeholder"
		    bindstatuschange="onStatusChange" 
			@ready="onEditorReady" 
			@input="updateContent"
			confirm-hold
		>
		</editor>
	</view>
</template>
<!-- 
富文本
1. 默认model有值时富文本在onReady后需要赋值
2. 外部插入特殊显示文本
3. 删除时，需要判断是否是特殊文本，是的话需要发送事件
model内容富文本显示是不一致的。model内容仅为富文本内容的text文本，非html文本
 -->
<script setup lang="ts">
import { computed, createTextVNode, ref, shallowRef, onMounted, toRaw, watchEffect, getCurrentInstance } from 'vue';
import { findChangeRange, 
	// WrappedCode, 
	WrappedBeginCode, WrappedEndCode,
	createTextNode, createTextStyleNode ,
	findDeleteWrappedRange,
	findAddIndex,
	getSpeciaMatchIndex
} from "./index";

// 需要特殊文本输入的字符集
const model = defineModel<string>();
const props = defineProps({
	placeholder: {
		type: String,
		default: "请输入"
	},
});

interface Emits {
	(e: "blureCursor", value: number): void; //光标位置移动
	(e: "deleteSpeciaText", value: string, deleteIndex: number): void; //删除特殊字符 时在特殊字符数组中的位置
}

const emits = defineEmits<Emits>();
const defaultStyleStr = "color: rgba(72, 218, 234);";
const editorCtx = ref(null) // 编辑器上下文
defineExpose({
	insertStyleText(text: string) {
		if (!text || !editorCtx.value) return;
		
		console.log("插入11");
		const endIndex = lastInputHtml.length;
		setSelection(endIndex).then(() => insertStyleText(text).then(span => {
			if (span) return setSelection(endIndex + span);
		}));
	}
});

let lastInputHtml = "";

// const onBlure = (e: {detail: {value: string, cursor: number}}) => {
// 	emits("blureCursor", e.detail.cursor);
// 	getSelection()
// };

// 编辑器初始化
const instance = getCurrentInstance();
const onEditorReady = () => {
	// 选择编辑器组件并获取上下文
    uni.createSelectorQuery().in(instance.proxy).select('#editor').context((res: any) => {
		editorCtx.value = res.context;
		handleDefaultInput(model.value);
    }).exec();
}

// 获取编辑器内容
// 内容insert时会触发updateContent 且返回的html参数不准确，需要在插入时延时10ms防止触发删除逻辑
let isInsertPending = false;
const updateContent = (e) => {
  if (isInsertPending) return;
  const content = e.detail // 获取编辑器内容
  // 新增
  if (content.html.length > lastInputHtml.length) {
	model.value = content.text;
	lastInputHtml = content.html;
	// 替换
  } else if (content.html.length == lastInputHtml.length) {
	  model.value = content.text;
	  lastInputHtml = content.html;
  } else { // 删除

	handleDeleteInput(content.html, content.text);
  }
}

// 默认显示
function handleDefaultInput(input: string) {
	if (!input || !editorCtx.value) return;
	// 把搜索 \n替换成 br标签
	input = input.replaceAll("\n", "<br/>");
	const regex = new RegExp(`${WrappedBeginCode}(.*?)${WrappedEndCode}`, "g");
	
	const displayText = input.replace(regex, (match: string, text: string) => {
		const isConverted = text.includes("<span");
		if (isConverted) return text;
		return `${WrappedBeginCode}<span style="${defaultStyleStr}">${text}</span>${WrappedEndCode} `;
	});

	editorCtx.value.setContents({html: displayText, success: (res) => {
		console.log("显示文本成功：", res);
		editorCtx.value.getContents({success: (detail) => {
			model.value = detail.text;
			lastInputHtml = detail.html;
			console.log("获取内容成功", detail);
		}});
	}});
}

// 删除文本
function handleDeleteInput(inputHtml: string, input: string) {
	const {start, oldEnd} = findChangeRange(lastInputHtml, inputHtml);
	// 特殊字符为 零宽字符+ 文本 + 零宽字符为一个整体，因此需要获取实际删除的长度
	const [startIndex, endIndex, isSpecialText] = findDeleteWrappedRange(lastInputHtml, start, oldEnd);
	console.log("删除--1----1", startIndex, endIndex);
	if (isSpecialText) {
		const deleteStr = lastInputHtml.slice(startIndex, endIndex);
		const deleteIndex = getSpeciaMatchIndex(lastInputHtml, deleteStr);
		console.log("删除的特殊子串位置：", deleteIndex);
		if (deleteIndex >= 0) {
			lastInputHtml = lastInputHtml.slice(0, startIndex) + lastInputHtml.slice(endIndex);
			// 去除span标签
			// const newDeleteStr = deleteStr.replace(`‏<span style="color: rgb(72, 218, 234);">`, "").replace("</span>", "");
			emits("deleteSpeciaText", deleteStr, deleteIndex);
			console.log("删除成功")
			editorCtx.value.setContents({ 
				html: lastInputHtml, 
				fail: (err) => {
					console.log("删除崩溃", err)
				},
				success: (res) => {
					console.log("删除文本成功：", res);
					editorCtx.value.getContents({success: (detail) => {
						model.value = detail.text;
						lastInputHtml = detail.html;
						console.log("获取内容成功", detail);
						setSelection(lastInputHtml.length);
					}});
				}
			});
		} else {
			model.value = input;
			lastInputHtml = inputHtml;
		}
	} else {
		model.value = input;
		lastInputHtml = inputHtml;
	}
}

function getContent() {
	return new Promise<{html: string, text: string}>(resolve => {
		if (!editorCtx.value) return resolve({html: "", text: ""});
		editorCtx.value.getContents({success: (res: any) => {
			resolve({html: res.html, text: res.text});
		}});
	});
}
function setContent(html: string) {
	return new Promise<boolean>(resolve => {
		if (!editorCtx.value) return resolve(false);
		editorCtx.value.setContents({html: html, success: () => {
			resolve(true);
		}});
	});
}

function insertStyleText(text: string) {
	return new Promise<number>(resolve => {
		let replaceSpan = 0;
		isInsertPending = true;
		insertText("\u200D").then(getContent).then((res: any) => {
			const lastHtml: string = res.html;
			const html = `${WrappedBeginCode}<span style="${defaultStyleStr}">${text}</span>${WrappedEndCode} `;
			replaceSpan = html.length - "\u200D".length;
			const newHtml = lastHtml.replace("\u200D", html);
			return newHtml;
		}).then(setContent).then(getContent).then((res: any) => {
			model.value = res.text;
			lastInputHtml = res.html;
			setTimeout(() => isInsertPending = false, 10);
			
			resolve(replaceSpan);
		});
	});
}

function insertText(text: string) {
	return new Promise<boolean>(resolve => {
		if (!editorCtx.value) return resolve(false);
		editorCtx.value.insertText({text: text, success: () => {
			resolve(true);
		}});
	});
}

// 小程序版本中getSelection和setSelection在3.7.11版本以下没有
function getSelection() {
	return new Promise<number>(resolve => {
		if (!editorCtx.value) return resolve(0);
		if (!editorCtx.value.getSelection) return resolve(lastInputHtml.length);
		editorCtx.value.getSelection({success: (cursor) => {
				console.log("鼠标位置", cursor.range);
				resolve(cursor.range.index);
			},
			fail: (err) => {
				console.log("调用失败", err);
				resolve(lastInputHtml.length);
			}
		});
	});
}

function setSelection(index: number) {
	return new Promise<boolean>(resolve => {
		if (!editorCtx.value) return resolve(false);
		if (!editorCtx.value.setSelection) return resolve(false);
		editorCtx.value.setSelection({
			index: index,
			length: 0,
			success: () => {
				console.log("设置鼠标位置", index);
				resolve(true);
			},
			fail: (err) => {
				console.log("调用选中文字失败", err)
				resolve(false);
			}
		});
	});
}
	
</script>

<style scoped lang="scss">
	.container {
		position: relative;
		font-size: 32rpx;
		line-height: 40rpx;
		border-radius: 40rpx;
		box-sizing: border-box;
		height: 100%;
		width: 100%;
		word-break: break-all;
		word-wrap: break-word;
		letter-spacing: 0px;
		overflow-wrap: break-word;
		
		.textarea {
			height: 100%;
			width: 100%;
			padding: 0px;
			margin: 0px;
			letter-spacing: 0px;
			min-height: auto;
		}
		.textarea::placeholder {
			color: green;
		}
	}
	.display-container {
		width: 100%;
		height: 100%;
		padding: inherit;
		overflow-y: auto;
		background-color: inherit;
		
		&::-webkit-scrollbar {
			display: none;
		}
	}
	
	.input-container {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		padding: inherit;
		width: 100%;
		height: 100%;
		box-sizing: border-box;
		background-color: transparent;
	
		&::-webkit-scrollbar {
			display: none;
		}
	
		.textarea {
			height: 100%;
			width: 100%;
			padding: 0px;
			margin: 0px;
			letter-spacing: 0px;
			min-height: auto;
			// background-color: transparent;
			color: transparent;
		}
	}
</style>