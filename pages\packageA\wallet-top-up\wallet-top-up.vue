<template>
  <view class="wrapper">
    <Navbar :type="1" leftText="充值"></Navbar>
    <view class="container">
      <view class="header">
        <view class="label">充值方式</view>
        <view class="method">
          <image class="icon" src="/static/my/wechat-pay.png"></image>
          <text>微信支付</text>
        </view>
      </view>
      <view class="divide"></view>
      <view class="content">
        <view class="label">充值金额</view>
        <view class="amount-list-wrapper">
          <view class="amount-item" v-for="(item, idx) in amountList" :key="idx"
            :class="{ active: selection === item.value }" @click="() => selectAmount(item.value)">
            <view class="item-container">
              <text class="prefix">￥</text>
              <text>{{ item.label }}</text>
            </view>
          </view>
        </view>
        <view class="custom-amount-item">
          <view class="label">其它金额</view>
          <input type="digit" placeholder-style="color: #c9cdd4;font-size:32rpx;" v-model="customAmount"
            placeholder="请输入自定义金额" />
        </view>
      </view>

      <view class="footer" @click="handleCommit">确认</view>
      <view class="quesion-documentation">常见问题</view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue'
import {
  reChargeWallet,
  cancelPayBill
} from '@/api/wallet'

const amountList = ref([
  { label: '10', value: 10 },
  { label: '50', value: 50 },
  { label: '100', value: 100 },
  { label: '200', value: 200 },
  { label: '500', value: 500 },
  { label: '648', value: 648 },
])
const selection = ref(0)
const customAmount = ref('')
const currentAmount = ref(0)
const paying = ref(false)

const selectAmount = (amount) => {
  selection.value = amount
  currentAmount.value = amount
}

watch(customAmount, (newValue, oldValue) => {
  currentAmount.value = newValue
})

const getLoginCode = () => {
  return new Promise((resolve, reject) => {
    uni.login({
      success: (res) => {
        if (!res.code) {
          reject(res)
        }
        resolve(res.code)
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

const cancelPayCallback = async (billId) => {
  try {
    const res = await cancelPayBill(billId)
    if (res.code === 20000) {
      uni.showToast({
        title: '取消支付',
        icon: 'none'
      })
      uni.$emit('wallet-page-reload-data')
    }
  } catch (error) {
    console.log('cancelPayBill', error)
  }
}

const handleCommit = async () => {
  const amount = parseFloat(currentAmount.value) * 100
  if (isNaN(amount)) return

  if (paying.value) return

  paying.value = true
  try {
    const code = await getLoginCode()
    const res = await reChargeWallet({
      channel: 0,
      bank_id: 0,
      amount,
      code
    })
    if (res.code !== 20000) {
      return
    }
    const wx = res.data.wx, billId = res.data.bill_id
    uni.requestPayment({
      provider: 'wxpay',
      orderInfo: wx,
      timeStamp: wx.timeStamp,
      nonceStr: wx.nonceStr,
      package: wx.package,
      signType: wx.signType,
      paySign: wx.paySign,
      success: async (res) => {
        if (res.errMsg === 'requestPayment:ok') {
          uni.$emit('wallet-page-reload-data')
          uni.navigateBack({
            delta: 1
          })
        }
        console.log(res, 'uni.requestPayment ')
      },

      fail: (err) => {
        console.log(err, 'fail')
        cancelPayCallback(billId)
      },
    })
    console.log(res, 'reChargeWallet')
    paying.value = false
  } catch (error) {
    console.log(error)
    paying.value = false
  }
}
</script>

<style scoped lang='scss'>
.wrapper {
  display: flex;
  flex-direction: column;
  gap: 58rpx;
  width: 100%;
  height: 100%;

  .container {
    flex: 1;
    width: 100%;
    box-sizing: border-box;
    padding: 0 24rpx;
    position: relative;

    .header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2129;

      .method {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .icon {
          width: 48rpx;
          height: 48rpx;
        }
      }
    }

    .divide {
      margin-top: 30rpx;
      width: 100%;
      height: 2rpx;
      background: #D8D8D8;
    }

    .content {
      margin-top: 30rpx;

      .label {
        font-weight: 500;
        font-size: 32rpx;
        color: #1D2129;
      }

      .amount-list-wrapper {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(2);
        gap: 24rpx;
        margin-top: 30rpx;

        .amount-item {
          width: 212rpx;
          height: 96rpx;
          background: #FFFFFF;
          border-radius: 16rpx;
          border: 2rpx solid #D8D8D8;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all .35s;

          .item-container {
            display: flex;
            align-items: flex-end;
            font-size: 40rpx;
            line-height: 40rpx;
            color: #3D3D3D;

            .prefix {
              font-size: 28rpx;
              line-height: 28rpx;
            }
          }

          &.active {
            background: #FE58B7;

            .item-container {
              color: #fff;
            }
          }
        }
      }

      .custom-amount-item {
        width: 100%;
        height: 96rpx;
        background: #FFFFFF;
        border-radius: 16rpx;
        border: 2rpx solid #D8D8D8;
        display: flex;
        align-items: center;
        gap: 56rpx;
        box-sizing: border-box;
        padding: 32rpx 28rpx;
        margin-top: 24rpx;

        .label {
          font-size: 32rpx;
          color: #1D2129;
        }
      }
    }

    .footer {
      margin-top: 56rpx;
      width: 100%;
      height: 96rpx;
      background: #FE58B7;
      border-radius: 48rpx;
      color: #fff;
      line-height: 96rpx;
      text-align: center;
    }

    .quesion-documentation {
      position: absolute;
      bottom: 68rpx;
      left: 0;
      width: 100%;
      text-align: center;
      color: #5E94CE;
      font-weight: 500;
      font-size: 32rpx;
    }
  }
}
</style>