<template>
	<template v-if="isShow">
		<view class="login-popup-layout">
			<view class="mask-layer"></view>
			<view class="login-popup">
				<view class="title">欢迎登录次元星尘</view>
				<view class="title-sub">次元星尘～</view>
				<custom-login @onLogin="onLogin"></custom-login>
				<view v-if="showClose" class="icon-close-bar" @click="closeDialog">
					<image class="icon-close" src="/static/icon/icon_close.png"></image>
				</view>
			</view>
		</view>
	</template>
</template>

<script setup>
	import {
		onBeforeUnmount,
		onMounted,
		ref,
		computed,
	} from 'vue';
	import {
		useStore
	} from 'vuex';
	import {
		getToken
	} from '@/common/auth'
	defineOptions({
		name: 'QuickLoginDialog',
	})
	const props = defineProps({
		showClose: {
			type: Boolean,
			default: true,
		}
	})
	const store = useStore();
	const userInfo = computed(() => store.state.userInfo)
	const isShow = ref(false)

	onMounted(() => {
		uni.$on('closeLogin', closeLogin)
		uni.$on('changeLogin', changeLogin);
	})

	onBeforeUnmount(() => {
		uni.$off('closeLogin', closeLogin);
		uni.$off('changeLogin', changeLogin);
	})

	function closeLogin() {
		showLogin(true)
	}

	function showLogin(value) {
		isShow.value = value
		uni.$emit('hideLoginDialog', value)
	}

	function changeLogin(value) {
		showLogin(value)
	}

	// 关闭弹窗
	function closeDialog() {
		// 发送关闭弹窗的通知
		uni.$emit('changeLogin', false)
		uni.$emit('hideLoginDialog', false)
	}
	// 登录
	async function onLogin(e) {
		console.log('onLogin e:', e)
		uni.showLoading()
		await store.dispatch('userInfo/login', e)
		await store.dispatch('userInfo/getUserInfo')
		uni.hideLoading()
		// 登录成功后关闭弹窗
		console.log('登录成功后关闭弹窗')
		uni.$emit('ReloadData', [])
		closeDialog();
	}
</script>

<style lang="scss">
	.login-popup-layout {
		position: fixed;
		z-index: 1991;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;

		.mask-layer {
			background: rgba(0, 0, 0, 0.5);
			position: fixed;
			z-index: 1992;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
		}

		.icon-close-bar {
			position: absolute;
			right: 20rpx;
			top: 20rpx;
			width: 50rpx;
			height: 50rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.icon-close {
				width: 28rpx;
				height: 28rpx;
			}
		}

		.login-popup {
			background-color: #fff;
			border-radius: 24rpx 24rpx 0rpx 0rpx;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			height: 447rpx;
			z-index: 1993;
			padding: 45rpx;
		}

		.title {
			font-size: 46rpx;
			font-weight: 500;
			color: #333333;
		}

		.title-sub {
			color: #999999;
			font-size: 28rpx;
			margin-top: 12rpx;
		}
	}
</style>