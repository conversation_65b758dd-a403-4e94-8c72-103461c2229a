import { requestApi } from './request';

/** 查询漫展列表 Params */
interface GetExhibitionParams {
	/** 分页页码（page、size 与 skip、limit 不可同时使用） */
	page: number;
	/** 分页大小（page、size 与 skip、limit 不可同时使用） */
	size: number;
	/** 跳过大小（page、size 与 skip、limit 不可同时使用） */
	skip: number;
	/** 限制大小（page、size 与 skip、limit 不可同时使用） */
	limit: number;
	/** 省份 ID */
	province: number;
	/** 城市 ID */
	city: number;
	/** 区县 ID */
	district: number;
	/** 开始时间戳 */
	start_time: number;
	/** 结束时间戳 */
	end_time: number;
	/** 关键字查询 */
	keyword: string;
	/** 推荐(recommend)、收藏(collection)、参加(joinin) */
	flag: 'recommend' | 'collection' | 'joinin';
}
/** 查询漫展列表 Result */
interface GetExhibitionResult {
	/** 符合查询条件的漫展分页列表 */
	results: {
		/** 漫展 ID */
		id: number;
		/** 漫展名称 */
		name: number;
		/** 漫展封面 */
		cover: number;
		/** 开始时间戳（utc） */
		start_datetime: number;
		/** 结束时间戳（utc） */
		end_datetime: number;
		/** 当前用户是否登录 */
		is_logined: boolean;
		/** 城市 ID */
		is_collected: boolean;
		/** 漫展浏览量 */
		views_count: number;
		/** 报名此漫展好友数量 */
		friends_count: number;
		/** 报名此漫展好友列表 (未登录为 null) */
		friends: {
			/** 报名此漫展好友 ID */
			id: number;
			/** 报名此漫展好友 头像 */
			avatar: string;
		}[];
	}[];
	/** 符合查询条件的漫展总数 */
	count: number;
}
/** 查询漫展列表 */
export const getExhibitions = (params: GetExhibitionParams) => {
	return requestApi<GetExhibitionResult>({
		url: `/api/v1/foreground/home/<USER>/expo`,
		method: 'GET',
		data: params,
		isToken: false,
		loading: false,
	});
};
/** 查询漫展列表 end */

/** 省份 城市 区县 */
interface LocationObjectResult {
	id: number;
	code: string; //响应代码
	full_code: string; //响应代码
	name: string;
	full_name: string;
}

/** 查询指定漫展 Result */
interface GetExhibitionDetailResult {
	/** 漫展 ID */
	id: number;
	/** 漫展名称 */
	name: number;
	/** 漫展封面 */
	cover: string;
	/** detail 编辑器 */
	editor: string;
	/** 漫展详情（后续版本可能为富文本） */
	detail: string;
	/** 开始时间戳（utc） */
	start_datetime: number;
	/** 结束时间戳（utc） */
	end_datetime: number;
	/** 省份 */
	province: LocationObjectResult;
	/** 城市 */
	city: LocationObjectResult;
	/** 区县 */
	district: LocationObjectResult;
	/** 具体地址 */
	location: number;
	/** 经度 */
	latitude: number;
	/** 纬度 */
	longitude: number;
	/** 主办单位 */
	organizer: number;
	/** 承办单位 */
	sponsor: number;
	is_verify: boolean;
	is_logined: boolean;
	is_collected: boolean;
	joinin_count: number;
	joinins: {
		id: number;
		avatar: string;
	}[];
}
/** 查询指定漫展 */
export const getExhibitionDetail = (expo_id: number) => {
	return requestApi<GetExhibitionDetailResult>({
		url: `/api/v1/foreground/home/<USER>/expo/${expo_id}`,
		method: 'GET',
		isToken: false
	});
};
/** 查询指定漫展 end */

/** 收藏/取消收藏漫展 */
export const collectExhibition = (expo_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/home/<USER>/expo/${expo_id}/collection`
	});
};
/** 收藏/取消收藏漫展 end */

/** 参加/取消参加漫展 Params */
interface JoinExhibitionParams {
	/** 角色, 1: 游客; 2: coser */
	role: number;
	/** 参加时间戳 */
	join_datetime: number;
	/** 角色来源 */
	source: string;
	/** 角色 */
	character: string;
}
/** 参加/取消参加漫展 */
export const joinExhibition = (expo_id: number, params: JoinExhibitionParams) => {
	return requestApi({
		url: `/api/v1/foreground/home/<USER>/expo/${expo_id}/joinin`,
		data: params
	});
};
/** 参加/取消参加漫展 end */

/** 获取漫展评论 Params */
interface JoinExhibitionParams {
	/** 父级评论 ID (0: 根评论) */
	parent: number;
	/** 页码 默认 0 */
	page: number;
	/** 分页大小 */
	size: number;
	/** 条数 (page ==0 && size == 0 生效) 默认 10  */
	limit: string;
	/** 跳过 (page ==0 && size == 0 生效) 默认 0 */
	skip: string;
}

/** 查询服务评论 Result */
interface JoinExhibitionResult {
	results: {
		/** 服务评论 ID */
		id: number;
		/** 评论发布者 ID */
		user_id: number;
		/** 评论发布者头像 */
		user_avatar: number;
		/** 服务评论用户昵称 */
		user_nickname: number;
		/** 服务评论内容编辑器 */
		editor: number;
		/** 服务评论内容(违规/删除 为空) */
		content: number;
		/** 是否违规 */
		is_violation: number;
		/** 是否被删除 */
		is_deleted: number;
		/** 父级评论 ID */
		parent_id: number;
		/** 该评论的发表者是不是当前用户 */
		is_current_user: number;
		/** 评论时间 */
		created_at: number;
	}[];
	/** 查询服务评论总数 */
	count: number;
}

/** 获取漫展评论 */
export const getExhibitionComments = (expo_id: number, params: JoinExhibitionParams) => {
	return requestApi<JoinExhibitionResult>({
		url: `/api/v1/foreground/home/<USER>/expo/${expo_id}/comment`,
		method: 'GET',
		data: params
	});
};
/** 获取漫展评论 end */

/** 创建漫展评论 Params */
interface CreateExhibitionCommentParams {
	editor: string;
	content: string;
	parent: number;
}

/** 创建漫展评论 */
export const createExhibitionComment = (expo_id: number, params: CreateExhibitionCommentParams) => {
	return requestApi({
		url: `/api/v1/foreground/home/<USER>/expo/${expo_id}/comment`,
		data: params
	});
};
/** 创建漫展评论 end */

/** 删除漫展评论 */
export const deleteExhibitionComment = (expo_id: number, comment_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/home/<USER>/expo/${expo_id}/comment/${comment_id}`,
		method: 'DELETE',
		data: params
	});
};
/** 删除漫展评论 end */
