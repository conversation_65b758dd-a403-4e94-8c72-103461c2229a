<template>
	<slot name="mentions-top">
		<view class="mention-wrapper" :class="{ active: showUserList }">
			<view class="mention-item" v-for="user in userList" :key="user.id" @click="() => onMentionClick(user)">
				<image :src="user.avatar"></image>
				<text class="name">{{ user.name }}</text>
			</view>
		</view>
		<view class="divider" v-if="showUserList"></view>
	</slot>
	<view class="wrapper">
		<scroll-view :scroll-top="top" scroll-y class="preview-comment-wrappper">
			<rich-text :nodes="comment" :key="JSON.stringify(comment)"></rich-text>
		</scroll-view>
		<scroll-view scroll-y :scroll-top="top" @scroll="onScroll" class="textarea-wrapper">
			<textarea class="textarea" hold-keyboard auto-height v-model="inputValue" :adjust-position="false"
				@input="onInput" @keyboardheightchange="onKeyboardHeightChange" @linechange="onLineChange"></textarea>
		</scroll-view>
		<view class="preview-image-wrapper" :class="{ active: imglist.length > 0 }">
			<image-block v-for="(preview, idx) in imglist" :key="preview" :url="preview"
				@onDelete="() => onDeleteImage(idx)"></image-block>
		</view>
	</view>

	<slot name="footer">
		<view class="footer-wrapper">
			<view class="footer-icon-wrapper">
				<image class="footer-icon" src="/static/icon/dimension/mention.png" @click="onInsertMention"></image>
				<image class="footer-icon" src="/static/icon/dimension/picture.png" @click="onChooseImage"></image>
				<image class="footer-icon" src="/static/icon/dimension/emoji.png" @click="() => showEmoji = !showEmoji"></image>
			</view>
			<view class="submit-btn" @click="onSendMessage">发送</view>
		</view>
	</slot>

	<slot name="emoji">
		<view class="emoji-wrapper" :class="{ active: showEmoji }">
			<view class="emoji" v-for="(emoji, index) in emojiList" :key="index" @click="() => onEmojiClick(emoji)">
				{{ emoji }}
			</view>
		</view>
	</slot>
</template>

<script setup>
import { ref } from 'vue'
import emojiList from '/static/emoji/emoji.json'
import { stringToNodes } from './transform.js'
import ImageBlock from '../video-comment/image-block.vue';

const props = defineProps({
	userList: {
		type: Array,
		default: []
	},

})

const emit = defineEmits(['onSubmit', 'keyboardHeightChange'])

const top = ref(0)
const inputValue = ref('')
const mentionText = ref('');
const selectedMention = ref([]); // 存储选中的用户
const showUserList = ref(false)
const imglist = ref([]) // 评论选择的图片

const comment = defineModel('comment', { type: Array, default: [] })
const showEmoji = defineModel('showEmoji', { type: Boolean, default: false })

const onLineChange = (e) => {
	const { heightRpx } = e.detail
	top.value = heightRpx
}

const onScroll = (e) => {
	const scrollTop = e.detail.scrollTop
	top.value = scrollTop
}

const onInput = (e) => {
	const text = e.detail.value

	const match = text.match(/@([a-zA-Z0-9_]+)$/)  // 匹配 @ 后的文本

	if (match) {
		mentionText.value = match[1];  // 获取 @ 后面的文本
		showUserList.value = true;  // 如果有输入 @ 后的文字，则显示相关内容
	} else {
		showUserList.value = false;  // 如果没有 @ 后面的文字，则隐藏
		mentionText.value = '';  // 清空 mentionText
	}

	const nodes = stringToNodes(text, selectedMention.value)
	comment.value = nodes
}

const onKeyboardHeightChange = (e) => {
	const height = e.detail.height
	emit('keyboardHeightChange', height)
}

const onEmojiClick = (emoji) => {
	inputValue.value += emoji
	const nodes = stringToNodes(inputValue.value, selectedMention.value)
	comment.value = nodes
	showEmoji.value = false
}

const onInsertMention = () => {
	inputValue.value += '@'
	const nodes = stringToNodes(inputValue.value, selectedMention.value)
	comment.value = nodes
}

const onMentionClick = (user) => {
	let text = inputValue.value
	text = text.replace(`@${mentionText.value}`, '')
	text += `@${user.name} `
	inputValue.value = text
	selectedMention.value.push(user.name)
	const nodes = stringToNodes(inputValue.value, selectedMention.value)
	comment.value = nodes
	mentionText.value = ''
	showUserList.value = false
}

const onChooseImage = () => {
	uni.chooseImage({
		sizeType: ['original', 'compressed'],
		success: (res) => {
			const { tempFilePaths, tempFiles } = res
			imglist.value = tempFilePaths
		}
	})
}

const onDeleteImage = (idx) => {
	imglist.value.splice(idx, 1)
}

const onSendMessage = () => {
	emit('onSubmit', { value: comment.value, images: imglist.value })
	comment.value = []
	imglist.value = []
}
</script>

<style lang="scss" scoped>
.wrapper {
	position: relative;
	width: 100%;

	.preview-comment-wrappper {
		font-size: 32rpx;
		line-height: 40rpx;
		width: 100%;
		height: 143rpx;
		background-color: #f3f3f3;
		border-radius: 40rpx;
		box-sizing: border-box;
		padding: 18rpx 24rpx;
		overflow-y: auto;

		&::-webkit-scrollbar {
			display: none;
		}
	}

	.textarea-wrapper {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 40rpx;
		box-sizing: border-box;
		padding: 18rpx 24rpx;
		width: 100%;
		height: 143rpx;

		background-color: transparent;

		&::-webkit-scrollbar {
			display: none;
		}

		.textarea {
			font-size: 32rpx;
			line-height: 40rpx;
			width: 100%;
			height: 100%;
			background-color: transparent;
			caret-color: black;
			color: transparent;
		}
	}

	.preview-image-wrapper {
		width: 100%;
		height: 0;
		display: flex;
		gap: 32rpx;
		margin-top: 32rpx;
		flex-wrap: wrap;
		transition: height .3s;

		&.active {
			height: 108rpx;
		}
	}
}

.footer-wrapper {
	box-sizing: border-box;
	padding: 28rpx 0;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;

	.footer-icon-wrapper {
		display: flex;
		align-items: center;
		gap: 56rpx;

		.footer-icon {
			width: 56rpx;
			height: 56rpx;
		}
	}

	.submit-btn {
		width: 136rpx;
		height: 64rpx;
		background: #FE58B7;
		border-radius: 32rpx 32rpx 32rpx 32rpx;
		text-align: center;
		line-height: 64rpx;
		color: #fff;
	}
}

.emoji-wrapper {
	padding-bottom: 24rpx;
	display: flex;
	flex-wrap: wrap;
	box-sizing: border-box;
	background-color: #f4f4f4;
	border-radius: 32rpx;
	font-size: 32rpx;
	height: 0;
	overflow-y: auto;
	transform: translateY(100%);
	transition: transform, height .3s;

	.emoji {
		padding-left: 24rpx;
		padding-top: 24rpx;
	}

	&.active {
		transform: translateY(0);
		height: 400rpx;
	}
}

.mention-wrapper {
	box-sizing: border-box;
	padding: 0 40rpx;
	width: calc(100vw - 80rpx);
	height: 0;
	display: flex;
	align-items: center;
	gap: 48rpx;
	padding: 0 40rpx;
	overflow-x: auto;
	transition: height .3s;

	&.active {
		height: 156rpx;
	}

	&::-webkit-scrollbar {
		display: none;
	}

	.mention-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 16rpx;
		width: 112rpx;

		image {
			width: 112rpx;
			height: 112rpx;
			border-radius: 50%;
			border: 2rpx solid #FFFFFF;
		}

		.name {
			font-weight: 400;
			font-size: 28rpx;
			color: #5E94CE;
			line-height: 28rpx;
		}
	}
}

.divider {
	width: 100%;
	height: 2rpx;
	background: #F4F4F4;
	margin-top: 32rpx;
	margin-bottom: 28rpx;
}
</style>