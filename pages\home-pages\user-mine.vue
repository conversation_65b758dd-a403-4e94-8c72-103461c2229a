<template>
	<Mine v-if="isMe" :isNoTabbarPages="true"/>
	<UserMine v-else/>
</template>

<script setup>
import { ref } from 'vue';
import { useStore } from 'vuex';
import { onLoad } from '@dcloudio/uni-app';
import UserMine from './components/UserMine.vue'
import Mine from '@/pages/tabbar/mine/components/Mine.vue'

const store = useStore()
const isMe = ref(false)

onLoad((e) => {
	console.log(e.id,'id是否相同',store.state.userInfo.id);
	isMe.value = e.id == store.state.userInfo.id;
})
</script>