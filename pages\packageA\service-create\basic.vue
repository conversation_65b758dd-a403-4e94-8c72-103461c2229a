<template>
  <Navbar :type="1" leftText="服务项"></Navbar>

  <view class="wrapper">
    <view class="create-service-item" v-for="item in menus" :key="item.value" @click="() => handleTo(item.title)">
      <image class="icon" :src="item.icon" />
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import yp from '../static/service/<EMAIL>'
import yz from '../static/service/<EMAIL>'

const menus = ref([
  { title: '约拍', value: 0, icon: yp },
  { title: '约妆', value: 1, icon: yz },
])

const handleTo = title => {
  let url = `/pages/packageA/service-create/type?type=${title}`
  url && uni.navigateTo({
    url
  })
}
</script>

<style scoped lang='scss'>
.wrapper {
  box-sizing: border-box;
  padding: 0 24rpx;
  margin-top: 32rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 32rpx;

  .create-service-item {
    width: 100%;
    height: 240rpx;

    .icon {
      width: 100%;
      height: 100%;
    }
  }
}
</style>