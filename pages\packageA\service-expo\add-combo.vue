<template>
  <z-paging class="app-container" ref="paging" v-model="dataList" @query="queryList" :fixed="false">
    <template #empty>
      <uv-empty />
    </template>

    <template #top>
      <Navbar :type="1" leftText="选择漫展"></Navbar>
    </template>

    <view class="wrapper">
      <view class="city-wrapper" @click="() => provicePicker.open()">
        <text>{{ customProvince?.name || '全国' }}</text>
        <image class="icon" src="/static/icon/<EMAIL>"></image>
      </view>
      <uni-list class="dust-uni-list" :border="false">
        <uni-list-item :border="false" class="dust-uni-list-item" v-for="(item, index) in dataList" :key="item.id">
          <template v-slot:body>
            <view class="article-item-wrapper" @click="() => handleTo(item.id)">
              <article-item :article="item" :showViewer="false">
                <view class="anime-item-wrap">
                  <view class="flex title-wrap">
                    <text class="title">{{ item.name }}</text>
                  </view>
                  <view class="time-range-container">
                    <image class="icon" src="/static/img/exhibition-details/icon_sj.png"></image>
                    <text>{{ formatTimeRange(item.start_datetime, item.end_datetime) }}</text>
                  </view>
                  <view class="people-container" v-if="item?.friends_count">
                    <view class="viewer-avatar-container">
                      <image v-for="(fri, index) in item.friends" :key="index" :src="prefixFilePath(fri.avatar)"
                        class="viewer-avatar" @tap="handleToUserMine(fri)">
                      </image>
                    </view>
                    <text>{{ item.friends_count + ' 好友参加' }}</text>
                  </view>
                </view>
              </article-item>
            </view>
          </template>
        </uni-list-item>
      </uni-list>
    </view>

    <uv-picker closeOnClickOverlay ref="provicePicker" :columns="provinceNames" keyName="name" round="32rpx"
      confirmColor="#FE58B7" @confirm="proviceConfirm"></uv-picker>
  </z-paging>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { onLoad } from '@dcloudio/uni-app'
import { useTitle } from '../service-create/hooks/useTitle';
import { getExhibitions } from '@/api/exhibitionExpo';
import dayjs from 'dayjs'

const store = useStore();
const provinceNames = computed(() => [store.state.location.provinces])
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl)

function prefixFilePath(path) {
  return path ? cloudFileUrl.value + path : ''
}

const { categoryId, categoryTitle, typeTitle } = useTitle()

const paging = ref(null)
const provicePicker = ref(null)
const dataList = ref([])
const customProvince = ref(store.state.location.province)
const hadSelectedExpo = ref([])

onLoad((option) => {
  const selectedExpo = option.hadSelectedExpo
  if (selectedExpo) {
    hadSelectedExpo.value = selectedExpo.split(',').map(item => Number(item))
  }
})

uni.$on('service-expo-add-combo:reload-hadSelectedExpo', (id) => {
  hadSelectedExpo.value.push(id)
})

async function queryList(pageNo, pageSize) {
  try {
    const params = {
      page: pageNo,
      size: 10,
      end_time: -dayjs().startOf('day').valueOf()
    }
    if (customProvince.value?.id) {
      params.province = customProvince.value.id
    }
    let res = await getExhibitions(params)
    if (res.code === 20000) {
      paging.value.complete(res.data.results);
    } else {
      paging.value.complete(false);
    }
  } catch (error) {
    console.log(error, 'getExhibitions')
  }
}

const proviceConfirm = (e) => {
  customProvince.value = e.value[0]
  paging.value.reload()
}

const handleTo = async id => {
  if (hadSelectedExpo.value.includes(id)) {
    uni.showToast({
      title: '请勿选择创建相同漫展服务',
      icon: 'none'
    })
    return
  }
  uni.navigateTo({
    url: `/pages/packageA/service-expo/service-expo?id=${id}&categoryTitle=${categoryTitle.value}&typeTitle=${typeTitle.value}&from=add-combo`
  })
}

function formatTimeRange(start, end) {
  function padZero(num) {
    return (num < 10 ? '0' : '') + num;
  }
  const startDate = new Date(start);
  const endDate = new Date(end);

  const year = endDate.getFullYear()
  const startMonth = startDate.getMonth() + 1;
  const startDay = startDate.getDate();
  const startHour = padZero(startDate.getHours());
  const startMinute = padZero(startDate.getMinutes());
  const endMonth = endDate.getMonth() + 1;
  const endDay = endDate.getDate();
  const endHour = padZero(endDate.getHours());
  const endMinute = padZero(endDate.getMinutes());

  return `${year}年${startMonth}月${startDay}日至${endMonth}月${endDay}日 ${startHour}:${startMinute}~${endHour}:${endMinute}`;
}
function handleToUserMine(item) {
	uni.navigateTo({
		url: '/pages/home-pages/user-mine?id=' + item.id,
	})
}
</script>

<style scoped lang='scss'>
.wrapper {
  box-sizing: border-box;
  padding: 0 24rpx;
  width: 100%;
  margin-top: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;

  .city-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    width: 164rpx;
    height: 60rpx;
    background: #F7F8FA;
    border-radius: 36rpx 36rpx 36rpx 36rpx;
    font-size: 28rpx;
    color: #1D2129;

    .icon {
      width: 24rpx;
      height: 24rpx;
    }
  }
}

.article-item-wrapper {
  width: 100%;

  .anime-item-wrap {
    padding: 0 24rpx;

    .title-wrap {
      width: 100%;
      align-items: center;
      justify-content: space-between;
      margin-top: 20rpx;

      .title {
        font-size: 36rpx;
        font-weight: 700;
        color: #3D3D3D;
      }

      .collect-wrap {
        gap: 8rpx;
        align-items: center;
        color: #1f1f1f;
        font-size: 28rpx;

        image {
          width: 40rpx;
          height: 40rpx;
        }
      }
    }

    .time-range-container {
      margin-top: 16rpx;
      display: flex;
      align-items: center;
      gap: 8rpx;
      font-weight: 400;
      font-size: 28rpx;
      line-height: 40rpx;

      .icon {
        width: 28rpx;
        height: 28rpx;
      }
    }

    .people-container {
      margin-top: 24rpx;
      display: flex;
      align-items: center;
      gap: 12rpx;
      font-size: 28rpx;
      line-height: 40rpx;

      .viewer-avatar-container {
        display: flex;

        .viewer-avatar {
          width: 54rpx;
          height: 54rpx;
          border-radius: 50%;
          margin-left: -20rpx;

          &:first-child {
            margin-left: 0;
          }
        }
      }
    }
  }
}
</style>