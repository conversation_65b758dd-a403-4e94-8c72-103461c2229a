<template>
	<view class="app-container">
		<z-paging ref="paging" :auto="false" :fixed="true" :refresher-enabled="false" :loading-more-enabled="false"
			:hide-empty-view="true">
			<template #top>
				<Navbar :type="1" leftText="搜索"></Navbar>
				<view class="allPidding" style="margin-top: 32rpx;">
					<NotificationNavbar placeholder="消息内容"></NotificationNavbar>
				</view>
			</template>
			<view class="content allPidding">
				<view class="messageBox" v-for="(item, index) in messageList" @click="toType(item.type)">
					<image :src="item.image" mode=""></image>
					<view class="messageBoxRight">
						<view>
							<text>{{ item.name }}</text>
							<text>{{ item.time }}</text>
						</view>
						<view>
							<text class="textHide">{{ item.body }}</text>
							<view>99</view>
						</view>
						<view class="type" v-if="item.type == 2">商家</view>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup>
import {
	ref
} from 'vue'
const messageList = ref([{
	name: '系统信息',
	time: '09:10',
	body: "消息内容 xxx消息内容 xxx",
	image: "../../../static/icon/<EMAIL>",
	type: 0
},
{
	name: '订单消息',
	time: '2023.09.18',
	body: "消息内容 xxx消息内容 xxx",
	image: "../../../static/icon/<EMAIL>",
	type: 1
},
{
	name: '订单消息',
	time: '2023.09.18',
	body: "消息内容 xxx消息内容 xxx",
	image: "../../../static/icon/<EMAIL>",
	type: 2
},
{
	name: '用户昵称 xx',
	time: '2023.09.18',
	body: "消息内容 xxx消息内容 xxx消息内消息内",
	image: "/static/Default_Avatar_boy3.png",
	type: 3
}
])

function toContacts() {
	uni.navigateTo({
		url: '/pages/tabbar/notification/contacts'
	})
}
function toType(type) {
	if (type == 0) {
		uni.navigateTo({
			url: `/pages/tabbar/notification/ordersNotification?type=${type}`
		})
	}
	if (type == 1 || type == 2) {
		uni.navigateTo({
			url: `/pages/tabbar/notification/ordersNotification?type=${type}`
		})
	}
}
</script>
<style lang="scss" scoped>
// :deep(.flex-1) {
// 	width: 592rpx !important;
// }

.allPidding {
	padding: 0 24rpx;
}

.textHide {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.search {
	margin-top: 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;

	image {
		width: 64rpx;
		height: 64rpx;
		z-index: 9999 !important;
		margin-right: 36rpx;
	}
}

.content {
	margin-top: 36rpx;

	.messageBox {
		height: 120rpx;
		display: flex;
		margin-bottom: 24rpx;

		image {
			width: 96rpx;
			height: 96rpx;
		}

		.messageBoxRight {
			position: relative;
			width: 582rpx;
			height: 100%;
			background: #F7F8FA;
			border-radius: 24rpx;
			margin-left: 24rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;

			.type {
				position: absolute;
				width: 64rpx;
				height: 40rpx;
				background: #FFE540;
				border-radius: 0rpx 24rpx 0rpx 16rpx;
				right: 0;
				top: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 24rpx;
				color: #3D3D3D;
			}

			view {
				display: flex;
				align-items: center;
				font-size: 28rpx;
				color: #85878D;
				justify-content: space-between;
			}

			view:nth-child(1) {
				margin-bottom: 20rpx;

				text:nth-child(1) {
					font-size: 32rpx;
					color: #1F1F1F;
					padding-left: 20rpx;
				}

				text:nth-child(2) {
					padding-right: 76rpx;
				}
			}

			view:nth-child(2) {
				text {
					padding-left: 20rpx;
					width: 476rpx;
				}

				view {
					display: flex;
					justify-content: center;
					align-items: center;
					width: fit-content;
					padding: 0 8rpx;
					height: 32rpx;
					background-color: #FE1E42;
					border-radius: 50%;
					font-size: 24rpx;
					color: #FFFFFF;
					margin-right: 20rpx;
				}
			}
		}
	}
}
</style>