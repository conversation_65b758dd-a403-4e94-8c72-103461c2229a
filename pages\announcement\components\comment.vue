<template>
	<view class="comment-container">
		<view class="header">
			<view class="title">
				<text>评论</text>
				<text>({{ commentsInfo.count}})</text>
			</view>
			<view class="action" @click="() => emites('checkAll')">
				<text>查看全部</text>
			</view>
		</view>
		<uni-list class="comment-list" :border="false">
			<uni-list-item class="dust-uni-list-item" v-for="comment in formatComments" :key="comment.id"
				:border="false">
				<template v-slot:body>
					<comment-block :item="comment" :show-footer="false" />
					<view class="children-comments" v-if="comment.children.length">
						<view v-for="child in comment.children" :key="child.id" class="child-comment">
							<comment-block :item="child" :show-footer="false" />
						</view>
					</view>
				</template>
			</uni-list-item>
		</uni-list>
	</view>
</template>
<!-- 参考 pages/packageA/exhibition-comment/sample-list.vue实现 有变动-->
<script setup lang="ts">
	import { computed } from 'vue';
	import type {
		CommentsItem,
	} from "@/api/announcement";
	
	interface CommentsInfo {
		count: number, 
		results: Array<CommentsItem>
	}
	interface Props {
		commentsInfo: CommentsInfo;
		id: string;
	}
	const props = withDefaults(defineProps<Props>(), {
		commentsInfo: () => ({count: 0, results: []})
	})
	interface Emits {
		(e: "checkAll"): void;
	}
	
	const emites = defineEmits<Emits>();

	interface FormattedComment {
		id: string
		avatar: string;
		name: string;
		comment: string;
		time: string;
		image?: string;
		children: Array<FormattedComment>
	}

	const formatComments = computed<Array<FormattedComment>>(() => {
		const formatCommentItem: (item: CommentsItem) => FormattedComment = (item) => {
			const isJson = item.editor === 'json'
			const content = (isJson && item.content) ? JSON.parse(item.content) : item.content;
			const comment = isJson ? content.text : content
			const image = isJson ? content.image : ''
			return {
				id: item.id,
				avatar: item.user_avatar,
				name: item.user_nickname,
				comment,
				time: formatTime(item.created_at * 1000),
				image,
				children: []
			}
		}

		const list: Array<FormattedComment> = props.commentsInfo.results.map(item => {
			// 先保存子评论
			const children = item.children || {results: []};
			const formattedItem = formatCommentItem(item);
			// 格式化子评论
			// 只能处理2层，超过2层未处理
			formattedItem.children = children.results.map(child => formatCommentItem(child));
			return formattedItem
		});
		return list;
	})

	/**
	 * 格式化时间戳
	 * @param {number} timestamp - 时间戳
	 * @returns {string} 格式化后的时间字符串
	 * 今天：hh:mm
	 * 今年非今天：MM月DD日 hh:mm
	 * 非今年：YYYY年MM月DD日 hh:mm
	 */
	function formatTime(timestamp: number): string {
		if (!timestamp) return '';

		const date = new Date(timestamp);
		const now = new Date();

		const year = date.getFullYear();
		const month = date.getMonth() + 1;
		const day = date.getDate();
		const hours = date.getHours().toString().padStart(2, '0');
		const minutes = date.getMinutes().toString().padStart(2, '0');

		const nowYear = now.getFullYear();
		const nowMonth = now.getMonth() + 1;
		const nowDay = now.getDate();

		// 判断是否是今天
		if (year === nowYear && month === nowMonth && day === nowDay) {
			return `${hours}:${minutes}`;
		}
		// 判断是否是今年
		else if (year === nowYear) {
			return `${month}月${day}日 ${hours}:${minutes}`;
		}
		// 非今年
		else {
			return `${year}年${month}月${day}日 ${hours}:${minutes}`;
		}
	}
</script>

<style lang="scss" scoped>
	.comment-container {
		margin-top: 32rpx;
		box-sizing: border-box;
		padding: 0 24rpx;

		.header {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.title {
				display: flex;
				align-items: center;
				gap: 12rpx;
				font-size: 28rpx;
				color: #3D3D3D;
			}

			.action {
				font-size: 28rpx;
				color: #48DAEA;
			}

		}

		.comment-list {
			margin-top: 24rpx;

			.dust-uni-list-item {
				margin: 24rpx 0;
				padding-bottom: 24rpx;
				border-bottom: 1px solid #F4F4F4;
			}

			.children-comments {
				box-sizing: border-box;
				padding-left: 88rpx;
				margin-top: 24rpx;
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.load-more {
					font-size: 28rpx;
					color: #48DAEA;
				}
			}
		}
	}

	.popup-wrapper {
		width: 100%;
		min-height: 220rpx;
		overflow-y: auto;
		box-sizing: border-box;
		padding: 24rpx;
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 0 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 24rpx;

		.status-item {
			width: 100%;
			text-align: center;
			padding-bottom: 24rpx;
			border-bottom: 2rpx solid #EEEEEE;
			font-size: 32rpx;
			color: #1F1F1F;

			&.red {
				color: $uni-color-error;
			}

			&:last-child {
				border: none;
			}
		}
	}
</style>