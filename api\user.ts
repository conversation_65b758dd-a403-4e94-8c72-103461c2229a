import { requestApi } from './request';

/** 微信手机号接口（无需token） Params */
interface LoginParams {
	/** 小程序用户登录凭证code */
	wx_code : string;
	/** 小程序获取手机号code */
	phone_code : string;
	/** 邀请码 */
	invite_code ?: string;
}
/** 登录接口 Result */
interface LoginResult {
	/** 登录凭证 */
	token : string;
}
/** 登录接口（无需token） */
export const login = (params : LoginParams) => {
	return requestApi<LoginResult>({
		url: `/api/v1/login/wxmp/phone`,
		data: params,
		isToken: false
	});
};
/** 登录接口（无需token） end */

/** 用户个人信息接口 Result */
interface GetUserInfoResult {
	/** 用户id */
	id : number;
	/** 微信open_id */
	openId : string;
	/** 用户昵称 */
	nickname : string;
}
/** 用户个人信息接口 */
export const getUserInfo = () => {
	return requestApi<GetUserInfoResult>({
		url: `/api/v1/foreground/mine/userinfo`,
		method: 'GET',
		loading: false
	});
};
/** 用户个人信息接口 end */

/** 手机注册发送验证码 Params */
interface GetRegisterCodeParams {
	/** 手机号 */
	phone : string;
}
/** 手机注册发送验证码 */
export const getRegisterCode = (params : GetRegisterCodeParams) => {
	return requestApi({
		url: `/api/v1/register/code`,
		data: params,
		isToken: false
	});
};
/** 手机注册发送验证码 end */

/** 手机注册发送验证码 Params */
interface RegisterParams {
	/** 手机号 */
	phone : string;
	/** 验证码 */
	code : string;
	/** 密码的 SHA256 签名 */
	password : string;
}
/** 手机注册发送验证码 */
export const register = (params : RegisterParams) => {
	return requestApi({
		url: `/api/v1/register`,
		data: params,
		isToken: false
	});
};
/** 手机注册发送验证码 end */

/** 用户手机号登录发送验证码 Params */
interface LoginCodeParams {
	/** 手机号 */
	phone : string;
}
/** 登录接口 Result */
interface LoginCodeResult {
	/** 登录凭证 */
	token : string;
}
/** 用户手机号登录发送验证码 */
export const loginCode = (params : LoginCodeParams) => {
	return requestApi<LoginCodeResult>({
		url: `/api/v1/login/code`,
		data: params,
		isToken: false,
		loading: false,
	});
};
/** 用户手机号登录发送验证码 end */

/** 用户手机号登录 Params */
interface LoginPhoneParams {
	/** 手机号 */
	phone : string;
	/** 验证码 */
	code : string;
}
/** 登录接口 Result */
interface LoginPhoneResult {
	/** 登录凭证 */
	token : string;
}
/** 用户手机号登录 */
export const loginPhone = (params : LoginPhoneParams) => {
	return requestApi<LoginPhoneResult>({
		url: `/api/v1/login/phone`,
		data: params,
		isToken: false
	});
};
/** 用户手机号登录 end */

/** 用户密码登录 Params */
interface loginPasswordParams {
	/** 手机号 */
	phone : string;
	/** 密码 */
	password : string;
	/** 邀请码 */
	invite_code : string;
}
/** 登录接口 Result */
interface loginPasswordResult {
	/** 登录凭证 */
	token : string;
}
/** 用户密码登录 */
export const loginPassword = (params : loginPasswordParams) => {
	return requestApi<loginPasswordResult>({
		url: `/api/v1/login/password`,
		data: params,
		isToken: false
	});
};
/** 用户密码登录 end */

/** 重置密码发送手机验证码 Params */
interface GetResetCodeParams {
	/** 手机号 */
	phone : string;
}
/** 重置密码发送手机验证码 */
export const getResetCode = (params : GetResetCodeParams) => {
	return requestApi({
		url: `/api/v1/reset/code`,
		data: params
	});
};
/** 重置密码发送手机验证码 end */

/** 重置密码 Params */
interface ResetPwdParams {
	/** 手机号 */
	phone : string;
}
/** 重置密码 */
export const resetPwd = (params : ResetPwdParams) => {
	return requestApi({
		url: `/api/v1/reset`,
		data: params
	});
};
/** 重置密码 end */

/** 检查是否登录 */
export const checklogin = () => {
	return requestApi({
		url: `/api/v1/checklogin`,
		method: 'GET'
	});
};
/** 检查是否登录 end */

/** 退出登录 */
export const logout = () => {
	return requestApi({
		url: `/api/v1/logout`,
		method: 'GET'
	});
};
/** 退出登录 end */

/** 更新用户信息 Params */
interface UpdateUserInfoParams {
	/** 用户昵称 */
	nickname : string;
	/** 用户头像 */
	avatar : string;
	/** 0: 未知 1: 男 2: 女 */
	gender : string;
	/** 生日时间戳 */
	birthday : string;
}
/** 更新用户信息 */
export const updateUserInfo = (params : UpdateUserInfoParams) => {
	return requestApi({
		url: `/api/v1/foreground/mine/userinfo`,
		method: 'PUT',
		data: params
	});
};
/** 更新用户信息 end */

/** 获取我的次元 Params */
interface GetDimenstarParams {
	page : number;
	size : number;
	skip : number;
	limit : number;
}
/** 获取我的次元 Result */
interface GetDimenstarResult {
	id : number;
	name : string;
	cover : string;
	thumbs_up_count : number;
}
/** 获取我的次元 */
export const getDimenstar = (params : GetDimenstarParams) => {
	return requestApi<GetDimenstarResult>({
		// url: `/api/v1/foreground/mine/dimenstar`,
		url: `/api/v1/foreground/mine/dimension`,
		method: 'GET',
		loading: false,
		data: params,
		showError: false
	});
};
/** 获取我的次元 end */

/** 获取我的相册 */
export const getAlbum = (params : GetDimenstarParams) => {
	return requestApi<GetDimenstarResult>({
		url: `/api/v1/foreground/mine/album`,
		method: 'GET',
		loading: false,
		data: params,
		showError: false
	});
};
/** 获取我的相册 end */

/** 获取我的点赞 */
export const getFavorite = (params : GetDimenstarParams) => {
	return requestApi<GetDimenstarResult>({
		url: `/api/v1/foreground/mine/favorite`,
		method: 'GET',
		loading: false,
		data: params,
		showError: false
	});
};
/** 获取我的点赞 end */

/** 获取我的收藏 */
export const getCollection = (params : GetDimenstarParams) => {
	return requestApi<GetDimenstarResult>({
		url: `/api/v1/foreground/mine/collection`,
		method: 'GET',
		loading: false,
		data: params,
		showError: false
	});
};
/** 获取我的收藏 end */

/** 微信登录接口（无需token） */
export const wxLogin = (params : {
	/** 小程序用户登录凭证code */
	wx_code : string;
	/** 邀请码 */
	invite_code ?: string;
}) => {
	return requestApi<LoginResult>({
		url: `/api/v1/login/wxmp/code`,
		data: params,
		isToken: false
	});
};
/** 登录接口（无需token） end */

/** 绑定手机号 */
export const bindPhone = (params : { phone : string; code_id : string; code : string }) => {
	return requestApi({
		url: '/api/v1/foreground/mine/phone',
		data: params
	});
};

/** 绑定邀请码 */
export const bindInviteCode = (code : string) => {
	return requestApi({
		url: '/api/v1/invite/bind',
		data: {
			invite_code: code
		}
	});
};
/** 获取邀请码 */
export const myInviteCode = () => {
	return requestApi({
		url: '/api/v1/foreground/message/invite',
		method: 'GET'
	});
};
/** 获取二维码邀请码 */
export const myQRInviteCode = (data : {
	page : string;
	scene : string;
	check_path : boolean;
	env_version : string;
}, regenerate = false) => {
	return requestApi({
		url: '/api/v1/foreground/message/invite?regenerate=' + regenerate,
		method: 'POST',
		data
	});
};
/** 获取二维码邀请码 */
export const myQRInviteFriendCode = (data : {
	page : string;
	scene : string;
	check_path : boolean;
	env_version : string;
}, regenerate = false) => {
	return requestApi({
		url: '/api/v1/foreground/mine/qrcode?regenerate=' + regenerate,
		method: 'POST',
		data
	});
};

interface BlackListParams {
	page : number;
	size : number;
	keyword ?: string;
}

interface BlackListResponse {
	count : number;
	results : Array<{
		id : string;
		"user_id" : string;
		target : {
			id : string;
			account : string;
			username : string;
			nickname : string;
			avatar : string;
			bio : string;
		}
		reason : string;
	}>
}
// 获取黑名单
export const getBlackList = (params : BlackListParams) => {
	return requestApi<BlackListResponse>({
		url: `/api/v1/foreground/message/blacklist`,
		method: 'GET',
		data: params,
	});
}
// 删除黑名单
export const deleteBlackItem = (id : string) => {
	return requestApi({
		url: `/api/v1/foreground/message/blacklist/${id}`,
		method: 'DELETE'
	});
}

// 添加黑名单
export const addBlackItem = (params : { "target_id" : number, reason : string }) => {
	return requestApi({
		url: `/api/v1/foreground/message/blacklist`,
		data: {
			...params,
			target_id: Number(params.target_id)
		},
		method: 'POST'
	});
}


// 关注用户
export const addFollow = (follow_id : number) => {
	return requestApi({
		url: `/api/v1/foreground/mine/follow`,
		method: 'POST',
		data: {
			follow_id: Number(follow_id)
		},
	});
}

// 取消关注用户
export const delFollow = (user_id : string) => {
	return requestApi({
		url: `/api/v1/foreground/mine/follow?user_id=${user_id}`,
		method: 'DELETE'
	});
}
type QuestionParams = BlackListParams;
interface QuestionItem {
	id : string;
	user_id : string;
	question : string;
	answer : string;
	editor : string;
	parent_id : string;
	children : QuestionItem[]
}
interface QuestionResponse {
	count : number;
	results : QuestionItem[]
}
// 获取问题列表
export const getQuestionList = (params : { keyword : string, parent_id ?: string }) => {
	return requestApi<QuestionResponse>({
		url: `/api/v1/foreground/mine/qa`,
		method: 'GET',
		data: params,
	});
}

/** 通过邀请码获取用户信息 Result */
interface Location {
	id : number;
	name : string;
}
interface GetInviteUserInfoResult {
	id : number;
	account : string;
	background_url : string;
	avatar : string;
	nickname : string;
	gender : number;
	bio : string;
	province : Location;
	city : Location;
	district : Location;
}

// 获取关注列表
export const getFollowList = (params : GetDimenstarParams) => {
	return requestApi<QuestionResponse>({
		url: `/api/v1/foreground/mine/follow`,
		method: 'GET',
		data: params,
		loading: false,
	});
}

// 获取粉丝列表
export const getFansList = (params : GetDimenstarParams) => {
	return requestApi<QuestionResponse>({
		url: `/api/v1/foreground/mine/fans`,
		method: 'GET',
		data: params,
		loading: false,
	});
}

// 回关粉丝
export const replyFansApi = (user_id: number) => {
	return requestApi<QuestionResponse>({
		url: `/api/v1/foreground/mine/fans`,
		method: 'POST',
		data: { user_id },
	});
}
/* 通过邀请码获取用户信息 */
export const getInviteUserInfo = (invite : string) => {
	return requestApi<GetInviteUserInfoResult>({
		url: `/api/v1/foreground/message/invite/${invite}`,
		method: 'GET',
	});
}

/** 获取我邀请的用户列表 */
interface GetInviteListParams {
	page : number;
	size : number;
	skip : number;
	limit : number;
	order_by: string;
	keyword: string;
}
/** 获取我邀请的用户列表 */
export const getInviteListApi = (params: GetInviteListParams) => {
	return requestApi({
		url: `/api/v1/foreground/mine/invite`,
		method: 'GET',
		data: params,
		loading: false,
	})
}