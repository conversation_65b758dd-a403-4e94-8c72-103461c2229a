<template>
	<z-paging class="app-container" :safe-area-inset-bottom="true" :use-safe-area-placeholder="true">
		<template #top>
			<Navbar :type="1" leftText="展会详情"></Navbar>
		</template>

		<view class="export-detail">
			<view class="image-container">
				<image class="img" :src="prefixFilePath(detail?.cover)" :show-menu-by-longpress="true" :show-loading="true" @tap="ckimg(prefixFilePath(detail.cover))">
				</image>
			</view>

			<view class="expo-title title-wrapper">
				<text class="title font-family-bold">{{ detail?.name || "" }}</text>
				<image class="item_icon" src="@/static/home/<USER>" @tap="reportHandle" />
			</view>

			<view class="expo-user" v-if="detail?.joinins?.length" @tap="toJoiner">
				<view class="people-container">
					<view class="viewer-avatar-container">
						<image v-for="(item, index) in detail?.joinins?.slice(0, 4)" :key="index"
							:src="prefixFilePath(detail?.joinins?.[index]?.avatar)" class="viewer-avatar"></image>
					</view>
					<text>{{ (detail?.joinin_count || 0) + ' 人想去' }}</text>
				</view>
				<!-- <view class="collection" @tap="handleCollect">
					<image :src="detail && detail.is_collected ? '/static/icon/star_selected.png' : '/static/img/exhibition-details/icon_sc.png'" />
					<text class="font-family-medium">收藏</text>
				</view> -->
			</view>

			<view class="expo-time mt-10">
				<image src="/static/img/exhibition-details/icon_sj.png" />
				<text class="font-size-small">{{ formatDateRange(detail?.start_datetime, detail?.end_datetime) }}</text>
			</view>

			<view class="expo-time">
				<image src="/static/img/exhibition-details/icon_sj.png" />
				<text class="font-size-small">{{ formatTimeRange(detail?.start_datetime, detail?.end_datetime) }}</text>
			</view>

			<view class="expo-location">
				<view class="expo-building" @tap="toMap">
					<image src="/static/icon/icon_wz.png" />
					<text class="font-size-small" style="color: #48daea">{{ detail?.location || '-' }}</text>
				</view>
				<view class="expo-address">
					<text>此活动由用户上传，请自行核实真实性</text>
				</view>
			</view>

			<!-- <view class="expo-company">
				<view class="flex items-center">
					<view class="flex items-center">
						<image src="/static/img/exhibition-details/icon_zbdw.png" />
					</view>
					<view class="flex items-center">
						<text class="font-size-small expo-company-name">{{ detail?.sponsor }}</text>
						<text class="font-size-small expo-company-check" :class="{ active: detail?.is_verify }">{{ detail?.is_verify ? '已核实' : '未核实' }}</text>
					</view>
				</view>
				<uni-icons class="ml-10 uni-icons" :size="12" color="#AAAAAA" type="arrowright" />
			</view> -->
		</view>

		<view class="dividers"></view>

		<view class="expo-service-container">
			<view class="expo-service">
				<view class="expo-header">
					<view class="line-gradient-border">
						<text class="expo-title font-family-bold">展会服务</text>
						<image class="icon-dise" src="/static/icon/dust_icon_tabs_active_star.svg" />
					</view>
					<image class="star" src="/static/img/exhibition-details/xing.png" />
				</view>
				<view class="expo-icons">
					<view :class="{disabled: !isYP, 'expo-icon-item': true}" @tap="handleTo(1)">
						<image src="/static/img/exhibition-details/icon_sy.png" />
						<text class="font-size-small">约拍</text>
					</view>
					<view :class="{disabled: !isYZ, 'expo-icon-item': true}" @tap="handleTo(2)">
						<image src="/static/img/exhibition-details/icon_hz.png" />
						<text class="font-size-small">约妆</text>
					</view>
				</view>
			</view>

			<view class="expo-header">
				<dust-tabs v-model="activeTab" :list="tabList" lineGradientBorder showActiveStar tabType="secondary"
					customStyle="gap: 56rpx;"></dust-tabs>
			</view>
		</view>

		<exhibition-tab-content :activeTab="activeTab"></exhibition-tab-content>

		<template #bottom>
			<view class="bottom-container">
				<view class="collection-wrapper" @tap="handleCollect">
					<image class="collect-icon"
						:src="detail?.is_collected ? '/static/icon/star_selected.png' : '/static/icon/star_noselected.png'">
					</image>
					<text class="font-size-small">收藏</text>
				</view>
				<view class="primary-btn" v-if="!isJoin" @tap="() => popup.open()">我想去</view>
				<view class="primary-btn" v-else>已参加</view>
			</view>
		</template>
	</z-paging>

	<uv-popup ref="popup" mode="bottom" bgColor="none">
		<view class="popup-container">
			<image class="close-icon" src="/static/icon/dimension/close.png" @tap="() => popup.close()"></image>
			<view class="primary-btn" @tap="() => handleTo('coser')">我是coser</view>
			<view class="secondary-btn" @tap="() => handleTo('tourists')">我是游客</view>
		</view>
	</uv-popup>
</template>

<script setup>
	import {
		computed,
		ref,
		toRaw
	} from 'vue';
	import {
		useStore
	} from 'vuex';
	import { formatDateRange, formatTimeRange } from '@/common/utils.js'
	import {
		useDetail
	} from './hooks/useDetail';
	import ExhibitionTabContent from './exhibition-tab-content.vue';
	const {
		detail,
		isJoin,
		handleCollect,
		handleRefresh
	} = useDetail();
	const store = useStore();
	const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

	const popup = ref(null);
	const activeTab = ref(0);
	const tabList = ref([{
			name: '漫展详情',
			value: 0
		},
		{
			name: '评论',
			value: 1
		},
		{
			name: '已参加',
			value: 2
		}
	]);

	const servicesMap = computed(() => {
		if (!detail.value?.services) return new Map()

		return new Map(detail.value.services.map(item => [item.category_id, item.service_count]))
	})
	// 约妆
	const isYZ = computed(() => (servicesMap.value.get(2) || 0) > 0)
	// 约拍
	const isYP = computed(() => (servicesMap.value.get(1) || 0) > 0)

	uni.$on('exhibition-detail-refresh', () => {
		handleRefresh();
		popup.value?.close();
	});

	function ckimg(img) {
		uni.previewImage({
			urls: [img],
			longPressActions: {
				itemList: ['发送给朋友', '保存图片', '收藏'],
				success: function(data) {},
				fail: function(err) {}
			}
		});
	}

	function prefixFilePath(url) {
		if (!url) return '';
		return cloudFileUrl.value + url;
	}

	function handleTo(id) {
		let url = '';
		switch (id) {
			case 'coser':
			case 'tourists':
				url =
					`/pages/packageA/exhibition-join-date/exhibition-join-date?type=${id}&start=${detail.value?.start_datetime}&end=${detail.value?.end_datetime}&expo_id=${detail.value.id}`;
				break;
			default:
				url =
					`/pages/packageA/photography/photography?type=${id}&is_daily=false&expo_id=${detail.value.id}&start_datetime=${detail.value.start_datetime}&end_datetime=${detail.value.end_datetime}`;
		}
		url &&
			uni.navigateTo({
				url
			});
	}

	function handleToUserMine(item) {
		uni.navigateTo({
			url: '/pages/home-pages/user-mine?id=' + item.id
		});
	}
	// 当前展示举报在空标签时 不美观。
	function reportHandle() {
		// 需要传递举报是服务举报(type为1) 还是 漫展举报（type为2）且需要传递对应漫展或者服务id
		uni.navigateTo({
			url: '/pages/packageA/report/first-type?' + `id=${detail.value.id}&type=${2}`
		});
	}

	function toMap() {
		uni.navigateTo({
			url: `/pages/packageA/exhibition-details/exhibition-map?latitude=${detail.value.latitude}&longitude=${detail.value.longitude}&location=${encodeURIComponent(detail.value.location)}`,
		})
	}

	function toJoiner() {
		uni.navigateTo({
			url: `/pages/packageA/exhibition-joiner/exhibition-joiner`,
		})
	}
</script>

<style lang="scss" scoped>
	uni-nav-bar {
		font-size: 24px;
		font-weight: 500;
	}

	text {
		&.font-size-small {
			font-size: 28rpx;
			font-weight: 400;
			line-height: 40rpx;
		}

		&.font-family-medium {
			font-family: Alibaba PuHuiTi 3-65 Medium;
		}

		&.font-family-bold {
			font-family: Alibaba PuHuiTi 3-75 SemiBold;
		}

		font-family: Alibaba PuHuiTi 3-55 Regular;
		color: #3d3d3d;
	}

	image {
		width: 28rpx;
		height: 28rpx;
	}

	.export-detail {
		margin-bottom: 24rpx;
		padding: 0 30rpx;
	}

	.line-gradient-border {
		position: relative;

		&::after {
			background-image: linear-gradient(to right, #48daea, #fff);
			content: '';
			position: absolute;
			bottom: 0;
			z-index: -1;
			display: block;
			width: 80%;
			height: 16rpx;
		}
	}

	.icon-dise {
		position: absolute;
		top: 0;
		right: -20rpx;
		width: 20rpx;
		height: 20rpx;
	}

	.star {
		position: relative;
		width: 14rpx;
		height: 18rpx;
		left: -92rpx;
		z-index: -1;
		margin-bottom: 32rpx;
	}

	.expo-title {
		padding-top: 28rpx;

		text {
			font-weight: 700;
			font-size: 36rpx;
			line-height: 50rpx;
		}
	}

	.title-wrapper {
		display: flex;
		justify-content: space-between;
		align-items: center;
		overflow: hidden;

		.title {
			flex: auto;
			// overflow: hidden;
			// text-overflow: ellipsis;
			// white-space: nowrap;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			overflow: hidden;
		}

		.item_icon {
			flex: none;
			width: 50rpx;
			height: 50rpx;
		}
	}

	.expo-user {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 10rpx;

		.people-container {
			display: flex;
			align-items: center;
			gap: 12rpx;
			font-size: 28rpx;
			line-height: 40rpx;

			.viewer-avatar-container {
				display: flex;

				.viewer-avatar {
					width: 54rpx;
					height: 54rpx;
					border-radius: 50%;
					margin-left: -20rpx;
					border: 2rpx solid #fff;

					&:first-child {
						margin-left: 0;
					}
				}
			}
		}

		.users {
			display: flex;
			align-items: center;

			image {
				width: 50rpx;
				height: 50rpx;
			}

			text {
				margin: 14rpx;
			}
		}

		.collection {
			display: flex;
			align-items: center;
			background-color: rgba(254, 88, 183, 0.12);
			height: 64rpx;
			border-radius: 36rpx;

			image {
				width: 40rpx;
				height: 40rpx;
				margin: 0 16rpx 0 24rpx;
			}

			text {
				color: #fe58b7;
				font-size: 500;
				padding-right: 24rpx;
			}
		}
	}

	.mt-10 {
		margin-top: 10rpx;
	}

	.expo-time {
		display: flex;
		align-items: center;

		image {
			flex-shrink: 0;
		}

		text {
			margin-left: 8rpx;
		}
	}

	.expo-location {
		display: flex;
		flex-direction: column;

		.expo-building {
			display: flex;
			align-items: center;

			image {
				margin-right: 8rpx;
			}
		}

		.expo-address {
			margin-left: 36rpx;

			text {
				font-size: 24rpx;
				color: red;
			}
		}
	}

	.expo-company {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin: 12rpx 0 0 0;

		.expo-company-name {
			color: #1f1f1f;
			font-size: 28rpx;
			margin: 0 12rpx 0 8rpx;
		}

		.expo-company-check {
			font-size: 20rpx;
			background-color: #bef000;
			padding: 4rpx 12rpx;
			border-radius: 12rpx;
			filter: grayscale(1);

			&.active {
				background-color: #bef000;
				filter: grayscale(0);
			}
		}
	}

	.dividers {
		border-bottom: 2rpx solid #d8d8d8;
	}

	.expo-service-container {
		padding: 14rpx;

		.expo-header {
			display: flex;
			align-items: center;
			padding: 20rpx 16rpx;
		}

		.expo-title {
			color: #1f1f1f;
			font-weight: 600;
			font-size: 32rpx;
			height: 72rpx;
		}

		.expo-icons {
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 24rpx 0;

			.expo-icon-item {
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 72rpx;

				&.disabled {
					filter: grayscale(1);
				}

				image {
					width: 56rpx;
					height: 56rpx;
				}
			}

			.expo-icon-item:last-child {
				margin-right: 0;
			}
		}
	}

	.uni-icons {
		line-height: 24rpx;
	}

	.expo-tab-container {
		padding-bottom: 118rpx;
	}

	.bottom-container {
		height: 168rpx;
		box-sizing: border-box;
		padding: 0 32rpx 50rpx;
		background-color: #fff;
		border-radius: 32rpx 32rpx 0rpx 0rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0rpx -8rpx 32rpx 0rpx rgba(0, 0, 0, 0.12);

		.collection-wrapper {
			display: flex;
			align-items: center;
			gap: 8rpx;
			font-size: 32rpx;
			color: #1f1f1f;
			line-height: 44rpx;

			.collect-icon {
				width: 48rpx;
				height: 48rpx;
			}
		}
	}

	.primary-btn {
		width: 356rpx;
		height: 80rpx;
		background: #fe58b7;
		border-radius: 40rpx 40rpx 40rpx 40rpx;
		color: #fff;
		text-align: center;
		line-height: 80rpx;
		font-weight: 500;
		font-size: 36rpx;
	}

	.secondary-btn {
		width: 356rpx;
		height: 80rpx;
		background: #48daea;
		border-radius: 40rpx 40rpx 40rpx 40rpx;
		color: #fff;
		text-align: center;
		line-height: 80rpx;
		font-weight: 500;
		font-size: 36rpx;
	}

	.popup-container {
		width: 100%;
		height: 312rpx;
		background: #ffffff;
		box-shadow: 0rpx -8rpx 32rpx 0rpx rgba(0, 0, 0, 0.12);
		border-radius: 32rpx 32rpx 0rpx 0rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 36rpx;
		box-sizing: border-box;
		padding: 0 96rpx;
		position: relative;

		.close-icon {
			position: absolute;
			width: 48rpx;
			height: 48rpx;
			top: 24rpx;
			right: 24rpx;
		}

		.primary-btn,
		.secondary-btn {
			width: 100%;
		}
	}
</style>