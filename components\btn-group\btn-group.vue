<template>
	<view :style="paddingStyle">
		<slot name="header" />
		<view class="btn-group" :style="gapStyle">
			<slot />
		</view>
		<uv-safe-bottom></uv-safe-bottom>
	</view>
</template>

<script setup>
	import {
		computed
	} from 'vue';
	import { isIPhoneX } from '@/common/utils'

	defineOptions({
		name: 'BtnGroup',
	})

	const props = defineProps({
		top: {
			type: String,
			default: '',
		},
		padding: {
			type: String,
			default: '0 24rpx 24rpx', // '0 24rpx'
		},
		gap: {
			type: String,
			default: '24rpx',
		},
		styleCusm: {
			type: String,
			default: '',
		},
	})

	const paddingStyle = computed(() => {
		let top = ''
		if (props.padding) {
			if (props.top) {
				top = `padding-top:${props.top};`
			}
			return `padding:${props.padding};${top}${isIPhoneX() ? 'padding-bottom:0;' : ''}` + props.styleCusm
		}
	})

	const gapStyle = computed(() => {
		if (props.gap) {
			return `gap:${props.gap};`
		}
	})
</script>

<style lang="scss">
	.btn-group {
		display: flex;
	}
</style>