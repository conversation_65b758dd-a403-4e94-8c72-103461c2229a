<template>
	<z-paging :auto="false" :refresher-enabled="false">
		<template #top>
			<Navbar :type="1" leftText="地图"></Navbar>
		</template>
		<map class="map" :style="{ height: contentHeight }" :latitude="latitude" :longitude="longitude"
			:markers="markers">
		</map>
	</z-paging>
</template>

<script setup>
import { computed, ref, toRaw } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import useContentHeight from '@/composables/useContentHeight.ts'
const { contentHeight } = useContentHeight({
  baseSpacing: '0rpx', // 可调整的值
  subtractSafeArea: false,
});
const latitude = ref(0)
const longitude = ref(0)
const location = ref('')
const markers = ref([])

onLoad((options) => {
	latitude.value = options.latitude
	longitude.value = options.longitude
	location.value = decodeURIComponent(options.location)
	markers.value = [{
		id: 1,
		latitude: options.latitude,
		longitude: options.longitude,
		width: 20,
		height: 30,
		callout: {
			content: decodeURIComponent(location.value),
			color: '#000',
			fontSize: 12,
			borderRadius: 8,
			bgColor: '#ffffff',
			padding: 10,
			display: 'ALWAYS' // 始终显示callout
		},
	}]
})
</script>

<style lang="scss">
.map {
	width: 100vw;
}
</style>
