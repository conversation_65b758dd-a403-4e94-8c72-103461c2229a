import {
	ref,
	computed
} from 'vue'
import { useStore } from 'vuex';
import {
	onLoad
} from '@dcloudio/uni-app';
import {
	collectExhibition
} from '@/api/exhibitionExpo'
import {
	toLog
} from '@/common/utils.js';
import * as auth from '@/common/auth';

export const useDetail = () => {
	const store = useStore()
	const expoDetailState = computed(() => store.state.expoDetail);
	const detail = computed(() => expoDetailState.value.expoDetail)
	const userInfo = computed(() => store.state.userInfo)
	const detailId = ref(null)

	const isJoin = computed(() => {
		return detail.value?.joinins?.some(item => item.id === userInfo.value.id)
	})

	onLoad(option => {
		const id = option.id
		const token = auth.getToken();
		if (id) {
			store.dispatch('expoDetail/fetchExpoDetail', id)
			if (!!token) {
				store.dispatch('expoDetail/fetchExpoComments', { expoId: id })
			}
			detailId.value = id
		}
	})

	function handleRefresh() {
		const token = auth.getToken();
		store.dispatch('expoDetail/fetchExpoDetail', detailId.value)
		if (!!token) {
			store.dispatch('expoDetail/fetchExpoComments', { expoId: detailId.value })
		}
	}

	async function handleCollect() {
		toLog()
		try {
			const res = await collectExhibition(detailId.value)
			if (res.code === 20000) {
				detail.value.is_collected = !detail.value.is_collected
				store.dispatch('expoDetail/updateExpoDetail', detail.value)
			}
		} catch (error) {
			console.log(error, '  handleCollect')
		}
	}

	return {
		detail,
		isJoin,
		handleCollect,
		handleRefresh
	}
}