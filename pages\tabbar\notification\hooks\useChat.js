import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { onLoad, onUnload, onHide } from '@dcloudio/uni-app'
import { getConsultDetailMessage, getConsultMessageReport } from '@/api/notification'

export const useChat = ({
  orderInfo = null,
  type = null,
  richtextPopup = null
}) => {
  const store = useStore();
  const userInfo = computed(() => store.state.userInfo);
  const cacheMessage = computed(() => store.state.message.message)

  const consultId = ref(null);
  const from = ref(null);
  const safeAreaBottom = uni.getWindowInfo().safeAreaInsets.bottom;
  const messageList = computed(() => {
    if (consultId.value) {
      return cacheMessage.value[consultId.value]?.list || []
    } else {
      return []
    }
  })

  const queryConsultMessage = async (id) => {
    try {
      const res = await getConsultDetailMessage(id)
      if (res.code === 20000) {
        const newList = res.data.results?.reverse().filter(item => item.msg_type === 'text' || item.msg_type === 'file')
        if (!!cacheMessage.value[id]) {
          cacheMessage.value[id].list = newList
        } else {
          cacheMessage.value[id] = {
            list: newList
          }
        }
        // store.commit('message/updateMessage', cacheMessage)
      }
    } catch (error) {
      console.log('getConsultDetailMessage Error: ', error)
    }
  }

  const reportMessageTime = async (id) => {
    try {
      const res = await getConsultMessageReport(id)
      if (res.code === 20000) {
        uni.$emit('ordersNotification:refresh-data')
      }
    } catch (error) {
      console.log('getConsultMessageReport Error:', error)
    }
  }

  onLoad(async option => {
    consultId.value = option.consultid;
    messageList.value = cacheMessage.value[consultId.value]?.list || [];
    from.value = option.from

    store.commit('message/updateCurrentPageInfo', {
      consultId: option.consultid,
      isNotificationDetail: true
    })

    if (option.from) {
      await store.dispatch('message/initClient')
      store.dispatch('message/commentSend', {
        consultId: option.consultid,
        content: '您好！想问问你们家套餐情况',
      })
    }

    if (option.message) {
      await store.dispatch('message/initClient')
      store.dispatch('message/commentSend', {
        consultId: option.consultid,
        content: decodeURIComponent(option.message),
      })
    }

    store.dispatch('message/loginSend', option.consultid)

    queryConsultMessage(option.consultid)
    reportMessageTime(option.consultid)
  })

  onUnload(() => {
    store.commit('message/updateCurrentPageInfo', {
      consultId: null,
      isNotificationDetail: false
    })
    reportMessageTime(consultId.value)
  })

  const keyboardHeight = ref(0);
  const onKeyboardHeightChange = (height) => {
    // 因为iOS中底部有安全区，所以需要调整实际高度
    const spanHeight = (safeAreaBottom && height > 0) ? (height - safeAreaBottom / 2) : height;
    keyboardHeight.value = spanHeight;
  }

  const isMyself = (id) => {
    return id === userInfo.value.id;
  }

  const formatAvatarAndName = (id = '') => {
    if (!id && !orderInfo.value.user) {
      return {
        avatar: '',
        nickname: ''
      }
    }
    if (id === orderInfo.value.user?.id) {
      return orderInfo.value.user
    } else {
      return orderInfo.value.vendor
    }
  }

  const onSubmit = ({ text, image }) => {
    const toId = orderInfo.value[`${type.value === '2' ? 'user' : 'vendor'}`].id

    if (text) {
      const data = {
        consultId: consultId.value,
        to: toId,
        content: text,
        type: 'text'
      }
      store.dispatch('message/commentSend', data)
    }

    if (image) {
      const data = {
        consultId: consultId.value,
        to: toId,
        content: image,
        type: 'file'
      }
      store.dispatch('message/commentSend', data)
    }
  }

  const previewImage = (url) => {
    uni.previewImage({
      urls: [url],
      current: url,
    })
  }

  return {
    messageList,
    keyboardHeight,
    previewImage,
    onKeyboardHeightChange,
    isMyself,
    formatAvatarAndName,
    onSubmit
  }
}