<template>
	<z-paging class="app-container" ref="paging" v-model="dataList" @query="queryList" :fixed="false">
		<template #empty>暂无评价</template>
		<template #top>
			<Navbar :type="1" leftText="服务评价"></Navbar>
		</template>

		<view class="wrapper">
			<view class="header">
				<view>评价 ({{ review_count }})</view>
				<view class="rating">总评分: {{ dataList.length > 0 ? '5.0' : 0 }}</view>
			</view>
			<uni-list class="dust-uni-list" :border="false">
				<uni-list-item :border="false" class="dust-uni-list-item" v-for="(item, index) in dataList" :key="item.id">
					<template v-slot:body>
						<view class="comment-item-wrapper" @click="toDelite(item.order_id)">
							<view class="order-info">约拍 · 订单编号 {{ item.order_number }}</view>
							<view class="header">
								<image class="avatar" :src="prefixFilePath(item.user_avatar)"></image>
								<view class="info-wrapper">
									<view class="name">{{ item.user_nickname }}</view>
									<view class="rating-wrapper">
										<view class="rating-list">
											<image
												class="rating-item"
												:src="`${
													(item.rating / 20).toFixed(1) >= star ? '../../static/rating-comment/<EMAIL>' : '../../static/rating-comment/<EMAIL>'
												}`"
												v-for="star in 5"
											></image>
										</view>
										<text>{{ (item.rating / 20).toFixed(1) }}分</text>
									</view>
								</view>
								<view class="time">{{ timeToString(item.created_at) }}</view>
							</view>
							<text class="userAppraise" v-if="item.editor == 'orderComment'">{{ item.content.txt }}</text>
							<text class="userAppraise" v-else>{{ item.content }}</text>
							<image :src="prefixFilePath(item.content.url)" v-if="item.editor == 'orderComment' && item.content.url" style="width: 100%; margin-top: 10rpx"></image>
							<view class="footer">
								<view class="receive" v-if="!item.reply" @click.stop="openRichtextPopup(index, 0)">立即回复</view>
								<view class="receive-child-wrapper" v-else>
									<view class="recerve-child" @click.stop="openRichtextPopup(index, 1)">
										<view class="comment">
											<text>
												<text style="color: #48daea">回复：</text>
												{{ item.reply.content }}
											</text>
										</view>
										<view class="time" style="margin-top: 20rpx">{{ timeToString(item.reply.created_at) }}</view>
									</view>
								</view>
							</view>
						</view>
					</template>
				</uni-list-item>
			</uni-list>
		</view>
	</z-paging>
	<uv-popup ref="richtextPopup" mode="bottom" bgColor="none">
		<view class="mention-container">
			<textareaComment @keyboardHeightChange="onKeyboardHeightChange" @onSubmit="onSubmit"></textareaComment>
		</view>
		<view v-if="keyboardHeight" :style="{ height: `${keyboardHeight}px` }"></view>
	</uv-popup>
</template>

<script setup>
import { uptip } from '@/common/utils.js';
import { ref, computed } from 'vue';
import { getMineServiceReview } from '@/api/mineService';
import { useStore } from 'vuex';
import { timeToString } from '@/common/utils.js';
import textareaComment from '@/components/textareaComment/textareaComment.vue';
import { createServiceReview, replyServiceReview } from '@/api/order';
const store = useStore();
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);
function prefixFilePath(url) {
	if (!url) return '';
	return cloudFileUrl.value + url;
}
const paging = ref(null);
function comment() {}
const dataList = ref([]);
const review_count = ref(0);
const richtextPopup = ref(null);
const keyboardHeight = ref(0);
const ckindex = ref(null);
const ckType = ref(0);
const openRichtextPopup = (index, type) => {
	ckindex.value = index;
	ckType.value = type;
	richtextPopup.value.open();
};

const onKeyboardHeightChange = (height) => {
	keyboardHeight.value = height;
};
function toDelite(id) {
	uni.navigateTo({
		url: `/pages/packageA/orderDetails/orderDetails?id=${id}&type=sell`
	});
}
function onSubmit(text, commentType = 'text') {
	richtextPopup.value.close();
	if (ckType.value == 0) {
		let obj = {
			service_item_id: Number(dataList.value[ckindex.value].service_item_id),
			editor: 'md',
			content: text,
			parent: Number(dataList.value[ckindex.value].id)
		};
		createServiceReview(dataList.value[ckindex.value].order_id, obj).then((res) => {
			if (res.code === 20000) {
				uptip('评论已成功提交', 'success');
				dataList.value[ckindex.value].reply = {
					id: res.data.id,
					content: text,
					created_at: Date.parse(new Date())
				};
			}
		});
	} else {
		let data = {
			content: text
		};
		replyServiceReview(dataList.value[ckindex.value].order_id, dataList.value[ckindex.value].reply.id, data).then((res) => {
			if (res.code === 20000) {
				uptip('评论修改成功', 'success');
				dataList.value[ckindex.value].reply.content = text;

				console.log(dataList.value[ckindex.value].reply);
			}
		});
	}
}
async function queryList(pageNo, pageSize) {
	const params = {
		page: pageNo,
		page: 10
	};
	const res = await getMineServiceReview(params);
	if (res.code === 20000) {
		review_count.value = res.data.count;
		for (let item of res.data.results) {
			if (item.editor == 'orderComment') {
				item.content = JSON.parse(item.content);
			}
		}
		paging.value.complete(res.data.results);
	} else {
		review_count.value = 0;
		paging.value.complete(false);
	}
}
</script>

<style scoped lang="scss">
.userAppraise {
	font-size: 28rpx;
}
.wrapper {
	box-sizing: border-box;
	width: 100%;
	padding: 0 24rpx;

	.header {
		width: 100%;
		margin: 32rpx 0 10rpx 0;
		font-weight: 500;
		font-size: 32rpx;
		color: #1f1f1f;
		line-height: 32rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.rating {
			font-size: 28rpx;
			color: #fe58b7;
			line-height: 40rpx;
		}
	}
}

.comment-item-wrapper {
	width: 100%;
	background: #f7f8fa;
	border-radius: 16rpx;
	box-sizing: border-box;
	padding: 24rpx;
	.order-info {
		color: #767676;
		font-size: 28rpx;
	}

	.header {
		display: flex;
		gap: 28rpx;
		align-items: flex-start;

		.avatar {
			width: 84rpx;
			height: 84rpx;
			border-radius: 50%;
		}

		.info-wrapper {
			flex: 1;
			display: flex;
			flex-direction: column;
			gap: 20rpx;

			.name {
				font-weight: 500;
				font-size: 32rpx;
				color: #1f1f1f;
			}

			.time {
				font-size: 24rpx;
				color: #85878d;
				line-height: 24rpx;
			}
		}

		.rating-wrapper {
			font-size: 28rpx;
			color: #fe58b7;
			line-height: 40rpx;
			display: flex;
			align-items: center;
			gap: 8rpx;

			.rating-list {
				display: flex;

				.rating-item {
					width: 32rpx;
					height: 32rpx;
				}
			}
		}
	}

	.main {
		width: 100%;
		margin: 24rpx 0 20rpx;
		font-size: 28rpx;
		color: #1f1f1f;
		line-height: 28rpx;
	}

	.footer {
		width: 100%;
		min-height: 68rpx;
		background: #ffffff;
		border-radius: 16rpx;
		box-sizing: border-box;
		padding: 20rpx;
		margin-top: 20rpx;

		.receive {
			text-align: center;
			font-size: 28rpx;
			color: #fe58b7;
			line-height: 28rpx;
		}

		.receive-child-wrapper {
			width: 100%;
			display: flex;
			flex-direction: column;

			.recerve-child {
				width: 100%;
				.comment {
					font-size: 28rpx;
					color: #1f1f1f;
					line-height: 28rpx;

					.label {
						color: #48daea;
					}
				}
			}
		}
	}
}
.time {
	font-size: 24rpx !important;
	color: #85878d;
	line-height: 24rpx !important;
}
.mention-container {
	height: fit-content;
	background: #ffffff;
	border-radius: 32rpx 32rpx 0rpx 0rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	box-sizing: border-box;
	padding: 32rpx 40rpx 0;
}
</style>
