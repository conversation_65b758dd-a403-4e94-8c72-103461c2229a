<template>
  <Navbar :type="1" leftWidth="422rpx" :leftText="`${typeTitle || ''}${categoryTitle || ''}服务描述`"></Navbar>

  <view class="wrapper">
    <view class="card-wrapper">
      <dust-crop-image :width="540" :height="342" @crop="(e) => chooseCover(e)">
        <template #trigger>
          <view class="upload-cover" v-if="!form.cover">
            <view class="img no-cover">
              <image class="bg-image-default" src="/static/img/upload-cover-default.png"></image>
              <image class="icon" src="/static/icon/<EMAIL>"></image>
              <view>上传封面图</view>
            </view>
          </view>
          <view class=" upload-cover" v-else>
            <image class="img" mode="aspectFit" :src="form.previewCover"></image>
          </view>
        </template>
      </dust-crop-image>
      <div class="input-wrapper">
        <input v-model="form.title" placeholder=" 给漫展活动添加一个标题吧！" placeholder-style="color: #979797; font-size: 32rpx;"
          @blur="textareaBlur" />
      </div>
      <textarea class="description-wrapper" :maxlength="-1" v-model="form.content" placeholder="请输入详情的活动描述"
        placeholder-style="color: #979797; font-size: 28rpx;" @blur="textareaBlur" />

    </view>

    <view class="feature-wrapper">
      <view class="feature-item-wrapper" @tap="() => handleTo(1)">
        <view class="feature-content">
          <view class="feature-header">
            <view class="label">作品集</view>
            <view class="action-text" v-if="artworksList.length !== 0">上传</view>
            <image class="header-icon" src="/static/icon/<EMAIL>" v-if="artworksList.length === 0"></image>
          </view>
          <view class="artworks-preview" v-if="artworksList.length > 0">
            <view class="artwork-item"
              v-for="(item, index) in artworksList.slice(0, artworksTotal > 3 ? 4 : artworksList.length)"
              :key="item.id">
              <image class="artwork-image" mode="aspectFill" :src="prefixImageUrl(item.url)"></image>
            </view>
            <view class="shadow-overlay" v-if="artworksList.length >= 4">
              <image class="arrow-icon" src="/static/icon/<EMAIL>"></image>
            </view>
          </view>
        </view>
      </view>
      <view v-if="isDaliy" class="feature-item-wrapper" @tap="() => handlePopupOpen('area')">
        <view class="label">服务区域</view>
        <image class="icon" src="/static/icon/<EMAIL>"></image>
      </view>
      <view v-if="isDaliy" class="feature-item-wrapper" @tap="() => handlePopupOpen('date-range')">
        <view class="label">服务时间/时段</view>
        <image class="icon" src="/static/icon/<EMAIL>"></image>
      </view>
    </view>
  </view>


  <uv-popup ref="popup" mode="bottom" bgColor="none" :closeOnClickOverlay="true">
    <view class="popup-wrapper">
      <view class="header">{{ popupType === 'date-range' ? '服务时间/时段' : '服务区域' }}</view>
      <dust-date-picker :showTimeSlot="true" :timeSlot="detail.time_slot" :dateSlot="detail.date_slot"
        v-if="popupType === 'date-range'" @confirm="popupConfirm" filterDirtyDates="2025"></dust-date-picker>
      <!-- <service-area :areas="detail.areas" :provinceId="detail.province_id" :cityId="detail.city_id" v-if="popupType === 'area'"
        @confirm="popupConfirm"></service-area> -->
      <view class="city-select-wrapper" v-if="popupType === 'area'">
        <city-select
          :showHotCity="false"
          :value="currentLocationValue"
          :customNavHeight="88"
          @confirm="handleLocationConfirm"
          @change="handleLocationChange"
        ></city-select>
      </view>
    </view>
  </uv-popup>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import dayjs from 'dayjs'
import {
  updateDimensionService,
  getMineServiceDetail,
  createDimensionService
} from '@/api/mineService'
import { getArtworks } from '@/api/dimensionService'
import { uploadFile } from '@/api/request'
import { useTitle } from '../service-create/hooks/useTitle'
import { useDaliy } from './hooks/useDaliy'
import CitySelect from '@/components/city-select/city-select.vue'

const store = useStore();
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);
const detail = ref(null)
const location = computed(() => store.state.location)
const artworksList = ref([])
const artworksTotal = ref(0)
const currentLocationValue = ref({})

const { isDaliy, typeTitle, categoryId, categoryTitle } = useTitle()

const { popup, popupType, openPopup, popupConfirm } = useDaliy({
  detail,
  queryServiceDetail
})

const form = ref({
  cover: '',
  title: '',
  content: '',
  originContent: '',
  previewCover: ''
})
const createServiceId = ref(null)

const prefixImageUrl = (filepath) => {
  if (filepath) {
    return cloudFileUrl.value + filepath
  }
  return ''
}

const queryArtworksList = async () => {
  if (!!createServiceId.value) {
    try {
      const params = {
        page: 1,
        size: 4, // 只获取前4张用于预览
      }
      const res = await getArtworks(createServiceId.value, params)
      if (res.code === 20000) {
        artworksList.value = res.data.results || []
        artworksTotal.value = res.data.count || 0
      }
    } catch (error) {
      console.log('获取作品集失败:', error)
    }
  }
}

onMounted(() => {
  queryServiceDetail()
})

async function queryServiceDetail() {
  if (!categoryId.value) return
  let res = await getMineServiceDetail({ categories: [categoryId.value], })
  console.log('*********getMineServiceDetail:', res)
  if (res.code === 20000) {
    const resdetail = !isDaliy.value ? findLastItem(res.data.results.filter(item => !item.is_daily)) : res.data.results.find(item => item.is_daily)
    console.log('***********resdetail:', resdetail)
    if (!!resdetail) {
      detail.value = resdetail

      createServiceId.value = resdetail.id
      form.value.cover = resdetail.cover
      form.value.previewCover = `${cloudFileUrl.value}${resdetail.cover}`

      form.value.content = resdetail.detail ? JSON.parse(resdetail.detail).instructions : ''
      form.value.originContent = resdetail.detail ? JSON.parse(resdetail.detail).instructions : ''

      // 获取作品集数据
      queryArtworksList()
      form.value.title = resdetail.name
    }
  }
  return res
}

function findLastItem(arr) {
  return arr[arr.length - 1]
}

async function createServiceDetail() {
  const params = {
    expo_id: null,
    name: form.value.title,
    cover: form.value.cover,
    editor: 'json',
    detail: form.value.content,
    start_date: dayjs().add(1, 'day').valueOf(),
    end_date: dayjs().add(1, 'month').valueOf(),
    categories: [categoryId.value],
    is_daily: isDaliy.value,
    // is_on_sale: false,
    province_id: location.value.province.id || 1,
    city_id: location.value.city.id || 1,
    locales: {},
    // district_id: location.value.district.id || 1
  }
  try {
    const res = await createDimensionService(params)
    if (res.code === 20000) {
      await queryServiceDetail()
      isDaliy.value && uni.$emit('service-create-detail-daliy:reload-data', 1)
      !isDaliy.value && uni.$emit('service-create-detail-expo:reload-data')
    }
  } catch (error) {
    console.log(error, 'createDimensionService service')
  }
}

const createOrUpdateService = async () => {
  const params = {
    ...detail.value,
    name: form.value.title,
    cover: form.value.cover,
    detail: JSON.stringify({
      instructions: form.value.content
    }),
    // is_on_sale: (detail.value.date_slot?.length > 0) && 
    //            !!detail.value.cover?.trim() && 
    //            !!form.value.title?.trim() && 
    //            !!form.value.content?.trim(),
  }
  try {
    if (createServiceId.value === null) return
    const res = await updateDimensionService(createServiceId.value, params)
    if (res.code === 20000) {
      await queryServiceDetail()
      isDaliy.value && uni.$emit('service-create-detail-daliy:reload-data', 1)
      !isDaliy.value && uni.$emit('service-create-detail-expo:reload-data')
    }
  } catch (error) {
    console.log(error, 'createOrUpdateService')
  }
}

const textareaBlur = () => {
  if (form.value.title.length > 20) {
    uni.showToast({
      title: '请输入20字以内的标题',
      icon: 'none'
    })
    return
  }
  if (detail.value) {
    createOrUpdateService()
  } else {
    createServiceDetail()
  }
}

const chooseCover = async (e) => {
  const imagePath = e.tempFilePath
  if (imagePath) {
    form.value.previewCover = imagePath
    const res = await uploadFile(imagePath)
    if (res.code === 20000) {
      form.value.cover = res.data.url
      form.value.previewCover = `${cloudFileUrl.value}${res.data.url}`
      if (detail.value) {
        createOrUpdateService()
      } else {
        createServiceDetail()
      }
    }
  }
}

const handlePopupOpen = async (type) => {
  if (!detail.value) {
    await queryServiceDetail()
    if (!detail.value) {
      console.log('handlePopupOpen 请先编写服务信息:', detail.value)
      uni.showToast({
        title: '请先编写服务信息',
        icon: 'none'
      })
      return
    }
  }
  openPopup(type)
}

const handleLocationConfirm = (locationData) => {
  currentLocationValue.value = locationData
  // 这里可以保存选中的地区信息到服务详情
  console.log('选中的地区:', locationData)
  popup.value.close()
}

const handleLocationChange = (locationData) => {
  // 地区选择变化时的回调
  console.log('地区选择变化:', locationData)
}

const handleTo = async id => {
  let url = ``

  if (id === 1) {
    if (!detail.value) {
      await queryServiceDetail()
      if (!detail.value) {
        console.log('handleTo 请先编写服务信息:', detail.value)
        uni.showToast({
          title: '请先编写服务信息',
          icon: 'none'
        })
        return
      }
    }
    url = `/pages/packageA/service-portfolio/service-portfolio?id=${createServiceId.value}`
  }
  url && uni.navigateTo({
    url
  })
}

</script>

<style scoped lang='scss'>
.wrapper {
  box-sizing: border-box;
  padding: 0 24rpx 24rpx;
  width: 100%;
  height: 100vh;
  margin-top: 32rpx;
  background-color: #FFFAFC;
  display: flex;
  flex-direction: column;
  gap: 32rpx;

  .card-wrapper {
    box-sizing: border-box;
    padding: 24rpx;
    width: 100%;
    height: fit-content;
    background-color: #FFFFFF;
    border-radius: 24rpx;
  }

  .input-wrapper {
    width: 100%;
    height: 96rpx;
    border-bottom: 2rpx solid #F5ECF1;
    display: flex;
    align-items: center;

    input {
      width: 100%;
    }
  }

  .upload-cover {
    width: 360rpx;
    height: 228rpx;
    border-radius: 24rpx;
    overflow: hidden;
    position: relative;

    .no-cover {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 20rpx;
      font-size: 28rpx;
      color: #FFFFFF;
      position: relative;

      .bg-image-default {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
      }

      // 蒙层
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(59, 52, 56, 0.4);
        z-index: 2;
      }
    }

    .icon {
      width: 64rpx;
      height: 64rpx;
      filter: brightness(0) invert(1);
      z-index: 3;
      position: relative;
    }

    // 文字也需要设置z-index
    .no-cover>view:last-child {
      z-index: 3;
      position: relative;
    }

    .img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .description-wrapper {
    width: 100%;
    height: 368rpx;
    box-sizing: border-box;
    padding: 24rpx;
    padding-left: 0;
    display: flex;
    flex-direction: column;
    gap: 24rpx;

    &.h-auto {
      height: fit-content;
    }

    textarea {
      width: 100%;
    }
  }

  .feature-wrapper {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
  }

  .feature-item-wrapper {
    width: 100%;
    min-height: 80rpx;
    box-sizing: border-box;
    padding: 24rpx;
    background: #fff;
    border-radius: 16rpx;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .feature-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 24rpx;

      .feature-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8rpx;

        .label {
          font-size: 32rpx;
          color: #3D3D3D;
          line-height: 32rpx;
        }

        .action-text {
          font-size: 28rpx;
          color: #FE58B7;
          line-height: 32rpx;
        }

        .header-icon {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .artworks-preview {
        display: flex;
        align-items: center;
        gap: 24rpx;
        overflow: hidden;
        position: relative;

        .artwork-item {
          width: 176rpx;
          height: 176rpx;
          border-radius: 12rpx;
          overflow: hidden;
          background: #F5F5F5;
          position: relative;
          flex-shrink: 0;

          .artwork-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .shadow-overlay {
          position: absolute;
          top: 0;
          right: 0;
          width: 60rpx;
          height: 176rpx;
          background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.5) 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          pointer-events: none;
          border-radius: 12rpx;

          .arrow-icon {
            width: 32rpx;
            height: 32rpx;
            filter: brightness(0) invert(1);
          }
        }
      }

      .empty-text {
        font-size: 28rpx;
        color: #FE58B7;
        line-height: 32rpx;
      }
    }

    .icon {
      width: 40rpx;
      height: 40rpx;
    }
  }
}

.popup-wrapper {
  width: 100%;
  height: 90vh;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  display: flex;
  flex-direction: column;

  .header {
    width: 100%;
    padding: 28rpx 0;
    text-align: center;
    font-weight: 500;
    font-size: 32rpx;
    color: #3D3D3D;
    border-bottom: 1rpx solid #F5F5F5;
    flex-shrink: 0;
  }

  .city-select-wrapper {
    flex: 1;
    height: 0;
    display: flex;
    flex-direction: column;
  }
}
</style>