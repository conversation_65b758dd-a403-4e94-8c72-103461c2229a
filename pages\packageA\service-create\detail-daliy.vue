<template>
  <z-paging ref="paging" class="app-container" :fixed="false" :safe-area-inset-bottom="true" :use-safe-area-placeholder="true">
    <template #empty>
      <uv-empty />
    </template>
    <template #top>
      <Navbar :type="1" :leftText="`${typeTitle || ''}${categoryTitle || ''}`"></Navbar>
    </template>

    <view class="wrapper">
      <view class="header" @click="() => handleTo('description')">
        <view class="empty-info" v-if="isEmptyDetail">编辑我的服务信息</view>
        <dust-image-base wrapperRadius="20rpx" :src="serivceDetail.cover" :local="false" aspect-ratio="16:9"
          v-if="!isEmptyDetail"></dust-image-base>

        <view v-if="!isEmptyDetail" class="description">
          <view class="title">服务标题</view>
          <view class="description-text">{{ serivceDetail.name }}</view>
        </view>
        <view v-if="!isEmptyDetail" class="description">
          <view class="title">服务描述</view>
          <view class="description-text">{{ formatDescription(serivceDetail) }}</view>
        </view>
      </view>

      <view class="content">
        <view class="flex items-center justify-between">
          <text class="title">{{ comboTitleMap.title }}</text>
          <view class="flex items-center add-combo-btn" @click="() => handleTo('add-combo')">
            <image class="icon" src="../static/service/add-circle.svg"></image>
            <text>{{ comboTitleMap.addCombo }}</text>
          </view>
        </view>
        <view class="combolist-container">
			<empty-list v-if="dataList.length === 0">
				<text>{{ comboTitleMap.emptyCombo }}</text>
			</empty-list>
          <uni-list v-else class="dust-uni-list" :border="false">
            <uni-list-item :border="false" class="dust-uni-list-item" v-for="(item, index) in dataList" :key="item.id">
              <template v-slot:body>
                <view class="combo-item-wrapper" @click="() => handleTo(item.id)">
                  <daliy-combo-item :item="item" />
                </view>
              </template>
            </uni-list-item>
          </uni-list>
        </view>
      </view>
    </view>
	
	<template #bottom>
		<btn-group>
			<button type="primary" v-if="!serivceDetail?.is_on_sale" class="btn-submit" @tap="onPublish">确认发布</button>
			<button type="primary" v-else class="btn-submit" @tap="onDesale">下架服务</button>
		</btn-group>
	</template>
  </z-paging>
  <publish-notice :cutdown="cutdown" v-if="showNotice" @close="onCloseNotice" @confirm="onConfirmNotice"/>
</template>

<script setup>
import { ref, computed, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { onLoad } from '@dcloudio/uni-app'
import {
  getMineServiceDetail,
  getMineServiceDetailItems,
  publishService,
  desaleService,
} from '@/api/mineService'
import { useTitle } from './hooks/useTitle'
import DaliyComboItem from './components/daliy-combo-item.vue'
import useNoticeManager from "@/components/publish-notice/useNoticeManager.ts";

const store = useStore()
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

function prefixFilePath(path) {
  if (!path) return ''
  return cloudFileUrl.value + path
}

const paging = ref(null)
const serivceDetail = ref(null)
const dataList = ref([])

const { typeTitle, categoryId, categoryTitle, comboTitleMap } = useTitle()
const {cutdown, showNotice, onConfirmNotice, onCloseNotice} = useNoticeManager("daliy", categoryTitle, 10);

const isEmptyDetail = computed(() => {
  return !serivceDetail.value?.cover && !serivceDetail.value?.name
})

uni.$on('service-create-detail-daliy:reload-data', async (type) => {
  if (type === 1) {
    const detail = await queryServiceDetail()
    serivceDetail.value = detail
  } else if (type === 2) {
    const list = await queryCombos()
    dataList.value = list
  } else {
    const detail = await queryServiceDetail()
    serivceDetail.value = detail

    const list = await queryCombos()
    dataList.value = list
  }
})

onLoad(async () => {
  const detail = await queryServiceDetail()
  serivceDetail.value = detail
	console.log('**********:', serivceDetail.value)
  const list = await queryCombos()
  dataList.value = list
})

function formatDescription(item) {
  try {
    return JSON.parse(item.detail).instructions
  } catch (error) {
    return ''
  }
}

async function queryServiceDetail() {
  if (!categoryId.value) return null
  try {
    let res = await getMineServiceDetail({
      categories: [categoryId.value]
    })

    if (res.code === 20000) {
      const resdetail = res.data.results.find(item => !!item.is_daily)
      return resdetail
    }
    return null
  } catch (error) {
    console.log(error, 'getMineServiceDetail service')
    return null
  }
}

async function queryCombos() {
  if (!serivceDetail.value) return []
  try {
    const res = await getMineServiceDetailItems(serivceDetail.value.id)

    if (res.code === 20000) {
      return res.data.sort((a, b) => b.is_on_sale - a.is_on_sale)
    }
    return []
  } catch (error) {
    console.log(error, 'getMineServiceDetailItems service')
    return []
  }
}

const handleTo = (id) => {
  let url = ''
  switch (id) {
    case 'description':
      url = `/pages/packageA/service-create-description/basic?categoryTitle=${categoryTitle.value}&typeTitle=${typeTitle.value}`
      break
    case 'add-combo':
      if (!serivceDetail.value?.id) {
        uni.showToast({ title: '请先编辑服务内容', icon: 'none' })
        return
      }
      url = `/pages/packageA/service-create-edit/service-create-edit?serviceId=${serivceDetail.value.id}&categoryTitle=${categoryTitle.value}&typeTitle=${typeTitle.value}`
      break
    default:
      url = `/pages/packageA/service-create/combo-list?serviceId=${serivceDetail.value.id}&categoryTitle=${categoryTitle.value}&typeTitle=${typeTitle.value}`
  }
  url && uni.navigateTo({
    url
  })
}
// 发布服务
async function onPublish() {
	console.log('onPublish')
	if (!serivceDetail.value?.id) {
	  uni.showToast({ title: '请先编辑服务内容', icon: 'none' })
	  return
	}
	const res = await publishService(serivceDetail.value.id)
	if (res.code === 20000) {
		uni.showToast({
			title: '发布成功'
		})
		const detail = await queryServiceDetail()
		serivceDetail.value = detail
	} else {}
}
// 下架服务
async function onDesale() {
	if (!serivceDetail.value?.id) {
	  uni.showToast({ title: '请先编辑服务内容', icon: 'none' })
	  return
	}
	const res = await desaleService(serivceDetail.value.id)
	if (res.code === 20000) {
		uni.showToast({
			title: '服务下架成功'
		})
		const detail = await queryServiceDetail()
		serivceDetail.value = detail
	} else {}
}
</script>

<style scoped lang='scss'>
.cover-wrapper {
  border-radius: 20rpx;
}

.wrapper {
  box-sizing: border-box;
  margin-top: 24rpx;
  padding: 0 24rpx;
  flex: 1;
  display: flex;
  flex-direction: column;

  .header {
    width: 100%;

    .empty-info {
      width: 100%;
      height: 368rpx;
      background: #F7F8FA;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #FE58B7;
    }

    .description {
      margin-top: 32rpx;

      .title {
        font-weight: 500;
        font-size: 32rpx;
        color: #3D3D3D;
      }

      .description-text {
        margin-top: 16rpx;
        font-size: 28rpx;
        color: #85878D;
      }
    }
  }

  .content {
    margin-top: 40rpx;
    flex: 1;
    display: flex;
    flex-direction: column;

    .title {
      font-size: 32rpx;
      color: #3D3D3D;
    }

    .add-combo-btn {
      font-size: 28rpx;
      color: #FE58B7;
      gap: 4rpx;

      .icon {
        width: 28rpx;
        height: 28rpx;
      }
    }

    .combolist-container {
      margin-top: 24rpx;
      flex: 1;

      .empty-list {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        color: #85878D;

        .icon {
          width: 380rpx;
          height: 380rpx;
        }
      }

      .combo-item-wrapper {
        width: 100%;
      }
    }
  }
}
</style>