<!-- 活动类型-->
<template>
  <z-paging ref="paging" class="app-container" :fixed="false">
    <template #top>
      <Navbar :type="1" leftText="活动类型"></Navbar>
    </template>
    <view class="container">
		<view class="item" v-for="(item, index) in dataList" @click="itemClick(index)" :key="index">
			<slot :data="item">
				<text :class="{'selcted': item.select}">{{item.name}}</text>
				<image class="image" v-if="item.select" src="@/pages/packages-publish/static/<EMAIL>" mode="aspectFit" />
			</slot>
		</view>
	  <view class="upload">
	  	<publish-input-row label="活动类型" textAlign="left" v-model="newType"/>
		<!-- <input class="input" v-model="newType" placeholder="请输入" placeholder-class="title-placeholder" /> -->
		<button class="btn" @tap="commitType">新建</button>
	  </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useStore } from 'vuex';
import type { ServiceInfo } from "@/store/modules/services.js";
import { PUBLISH_ACTIVITY_TYPES_SELECTE } from "./const"
import { onLoad } from "@dcloudio/uni-app";
import {
	getPublishTypes,
	createPublishType
} from "@/api/publish";

interface Option {
	name: string,
	id: number,
	select: boolean,
}
const selectType = ref<number>(undefined);
// 可选择的列表
const dataList = ref<Array<Option>>([]);
// 提交数据
const newType = ref<string>(null);
// 上个页面有传递选择时，页面需要显示选中
onLoad((query) => {
	console.log("传递的参数：", query);
	query.type && (selectType.value = query.type);
	loadData()
});

function loadData() {
	uni.showLoading();
	getPublishTypes().then(res => {
		const isSucessed = res.code === 20000;
		uni.hideLoading();
		if (!isSucessed) return uni.showToast({
			icon: "error",
			title: "活动类型获取失败"
		});
		const data = res.data || [];
		dataList.value = data.map(item => ({name: item.name, id: item.id, select: selectType.value == item.id}));
	}).finally(() => uni.hideLoading());
}

function commitType() {
	if (!newType.value) return uni.showToast({
		icon: "none",
		title: "请输入活动类型"
	});
	const isExistedType = dataList.value.find(item => item.name === newType.value);
	if (isExistedType) return uni.showToast({
		icon: "none",
		title: "已有相同的活动类型"
	});
	uni.showLoading();
	createPublishType(newType.value.trim()).then(res => {
		const isSucessed = res.code === 20000;
		uni.hideLoading();
		if (!isSucessed) return uni.showToast({
			icon: "error",
			title: "活动类型创建失败"
		});
		loadData();
	}).finally(() => uni.hideLoading());
}

const itemClick = function(index: number) {
	dataList.value.forEach((item, loc) => {
		item.select = loc === index ? !item.select : false;
	});
	const selectItem = dataList.value[index];
	console.log("选中的类型项", selectItem)
	// 对外抛出点击事件
	uni.$emit(PUBLISH_ACTIVITY_TYPES_SELECTE, {...selectItem});
}

</script>

<style scoped lang='scss'>

.container {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  width: 100%;
  padding: 0 24rpx;
  box-sizing: border-box;
  
  .item {
  	display: flex;
  	justify-content: space-between;
  	align-items: center;
  	height: 80rpx;
  	width: 100%;
  	
  	font-size: 32rpx;
  	color: rgba(31, 31, 31, 1);
  	border-bottom: 1px solid rgba(238, 238, 238);
  	.selcted {
  		color: rgba(254, 88, 183, 1);
  	}
  	.image {
  		height: 40rpx;
  		width: 40rpx;
  	}
  }
}

.upload {
	margin-top: 30rpx;
	display: flex;
	flex-direction: column;
	gap: 15px;
}

.input {
	padding: 20rpx;
	width: 100%;
	flex: 0 0 auto;
	height: 56rpx;
	font-size: 32rpx;
	line-height: 56rpx;
	background-color: rgba(247, 248, 250);
}

.title-placeholder {
	color: rgba(133, 135, 141);
}

.btn {
	height: 78rpx;
	width: 100%;
	padding: 0 44rpx;
	background-color: rgba(238, 238, 238);
	border-radius: 40rpx;
	font-size: 32rpx;
	font-weight: 700;
	line-height: 78rpx;
	border: none;
}


</style>