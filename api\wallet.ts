import { requestApi } from './request'

/** 查询我的余额 Result */
interface GetMineBalanceResult {
  id: number,
  /** 用户 ID */
  user_id: number,
  /** 余额 (分) */
  balance: number,
  /** 更新时间 */
  updated_at: number,
}

/** 查询我的余额 */
export const getMineBalance = () => {
  return requestApi<GetMineBalanceResult>({
    url: `/api/v1/foreground/mine/wallet/balance`,
    method: 'GET',
  })
}
/** 查询我的余额 end */

/** 查询我的账单 Params */
interface GetMineRecordParams {
  /** 分页页码（page、size 与 skip、limit 不可同时使用） */
  page: number,
  /** 分页大小（page、size 与 skip、limit 不可同时使用） */
  size: number,
  /** 跳过大小（page、size 与 skip、limit 不可同时使用） */
  skip: number,
  /** 限制大小（page、size 与 skip、limit 不可同时使用） */
  limit: number,
  /** 类型 0: 全部; 1: 充值; 2: 提现 */
  flag: 0 | 1 | 2
}

/** 查询我的账单 Result */
interface GetMineRecordResult {
  count: number,
  results: {

  }[]
}

/** 查询我的账单 */
export const getMineRecord = (params: GetMineRecordParams) => {
  return requestApi<GetMineRecordResult>({
    url: `/api/v1/foreground/mine/wallet/bill`,
    method: 'GET',
    data: params
  })
}
/** 查询我的账单 end */

/** 充值 / 提现 Params */
interface WalletReChangeParams {
  /** 充值 / 提现金额 (分) */
  amount: number,
  /** wx.login 返回的 code */
  code: string,
}

/** 充值 Result */
interface WalletReChangeResult {
  code: number,
  data: {
    appId: string,
    nonceStr: string,
    package: string,
    paySign: string,
    signType: string,
    timeStamp: string
  }
}

/** 充值 */
export const reChargeWallet = (params: WalletReChangeParams) => {
  return requestApi<WalletReChangeResult>({
    url: `/api/v1/foreground/mine/wallet/recharge`,
    data: params
  })
}
/** 充值 end */

/** 提现 */
export const withdrawalWallet = (params: WalletReChangeParams) => {
  return requestApi<WalletReChangeResult>({
    url: `/api/v1/foreground/mine/wallet/withdrawal`,
    data: params
  })
}
/** 提现 end */


/** 查询银行卡 */
export const getBankCard = () => {
  return requestApi({
    url: `/api/v1/foreground/mine/wallet/bank`,
    method: 'GET'
  })
}

interface BindBankCardParams {
  /** 银行卡号 */
  card_no: string
  /** 卡开户行所在省编码 */
  prov_id: string
  /** 卡开户行所在市编码 */
  area_id: string
  /** 默认卡 */
  is_settle_default: boolean
  phone: string
  bank: string
  name: string
  cert_no: string
  type: string
}
/** 绑定银行卡 */
export const bindBankCard = (params: BindBankCardParams) => {
  return requestApi({
    url: `/api/v1/foreground/mine/wallet/bank`,
    data: params
  })
}

/** 解绑银行卡 */
export const unbindBankCard = (id: number) => {
  return requestApi({
    url: `/api/v1/foreground/mine/wallet/bank/${id}`,
    method: 'DELETE'
  })
}

/** 提现 */
export const withdrawalToBank = (params: WalletReChangeParams) => {
  return requestApi({
    url: `/api/v1/foreground/mine/wallet/cashout`,
    data: params
  })
}

interface VerifyBankCardParams {
  "name": string, //真实姓名
  "cert_no": string, //身份证号
  "cert_validity_type": string, //0: 非长期有效 1: 长期有效
  "cert_begin_date": string, //个人证件有效期开始日期
  "cert_end_date": string //个人证件有效期截止日期
}

export const verifyBankCard = (params: VerifyBankCardParams) => {
  return requestApi({
    url: `/api/v1/foreground/mine/verification/3`,
    data: params,
  })
}

export const cancelPayBill = (id: number) => {
  return requestApi({
    url: `/api/v1/foreground/mine/wallet/recharge/${id}`,
    method: 'DELETE'
  })
}