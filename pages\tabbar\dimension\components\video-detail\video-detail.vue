<template>
  <view class="popup-content">
    <view class="top-action-line"></view>
    <view class="row-wrapper">
      <view class="user-info" @tap="toUserInfoPage">
        <image class="avatar" :src="prefixFilePath(info?.user?.avatar)">
        </image>
        <text>{{ info?.user?.nickname || info?.user?.username || '用户' + (info?.user?.account || info?.user?.uid || info?.user?.id) }}</text>
      </view>
      <!-- <view class="focus-btn">关注</view> -->
    </view>
    <view class="row-wrapper">
      <text class="description">{{ info?.description || '' }}</text>
    </view>
    <view class="row-wrapper">
      <text class="tag-mention" v-for="item in info?.subjects">
        #{{ item.name }}
      </text>
      <text class="tag-mention" v-for="item in info?.mentioned">
        @{{ item.nickname }}
      </text>
    </view>
    <!-- <view class="row-wrapper">
      <text class="time">
        15:00
      </text>
    </view> -->
    <!-- <view class="server-wrapper">
      <image src="https://i0.hdslb.com/bfs/mall/mall/b4/ae/b4aebb4dc49a59c9b1e0cdb04fe8b0c4.png"></image>
      <view class="info">
        <view>cos.Lolita 人像拍摄 | 一对一 | 外拍跟拍</view>
        <view class="price-wrapper">
          <view class="price">¥123.99</view>
          <view class="provide-price">商家保证金10%</view>
          <view class="count">已售 6</view>
        </view>
      </view>
    </view> -->
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'

const store = useStore()
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

function prefixFilePath(url) {
  if (!url) return '';
  return cloudFileUrl.value + url;
}

const props = defineProps({
  info: {
    type: Object,
    default: () => { },
  },
})

function toUserInfoPage() {
	uni.navigateTo({
		url: '/pages/home-pages/user-mine?id=' + props.info.user.id,
	})
}

</script>

<style scoped lang='scss'>
.popup-content {
  height: 550rpx;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 40rpx;

  .top-action-line {
    width: 80rpx;
    height: 8rpx;
    background: #D8D8D8;
    border-radius: 4rpx;
    margin-top: 24rpx;
  }

  .row-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 16rpx;

    .user-info {
      display: flex;
      align-items: center;
      gap: 16rpx;
      font-weight: 500;
      font-size: 36rpx;
      color: #1F1F1F;
      margin-top: 40rpx;

      .avatar {
        width: 84rpx;
        height: 84rpx;
        border: 2rpx solid #FFFFFF;
        border-radius: 50%;
      }
    }

    .focus-btn {
      margin-top: 40rpx;
      width: 144rpx;
      height: 64rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(254, 88, 183, 0.2);
      border-radius: 32rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #FE58B7;
    }

    .description {
      font-weight: 400;
      font-size: 32rpx;
      color: #1F1F1F;
      line-height: 48rpx;
      margin-top: 24rpx;
    }

    .tag-mention {
      font-weight: 500;
      font-size: 28rpx;
      color: #5E94CE;
      line-height: 39rpx;
      margin-top: 8rpx;
    }

    .time {
      font-weight: 400;
      font-size: 24rpx;
      color: #85878D;
      line-height: 24rpx;
      margin-top: 12rpx;
    }
  }

  .server-wrapper {
    margin-top: 22rpx;

    width: 100%;
    height: 124rpx;
    background: #F2F4F7;
    border-radius: 16rpx;
    display: flex;
    align-items: center;

    image {
      width: 124rpx;
      height: 124rpx;
      border-radius: 20rpx;
    }

    .info {
      font-weight: 400;
      font-size: 24rpx;
      color: #1F1F1F;
      line-height: 34rpx;

      .price-wrapper {
        display: flex;
        align-items: center;
        gap: 16rpx;
        margin-top: 16rpx;

        .price {
          font-weight: 600;
          font-size: 32rpx;
          color: #FE58B7;
          line-height: 44rpx;
        }

        .provide-price {
          color: #FE58B7;
        }

        .count {
          color: #86909C;
          margin-left: 20rpx;
        }
      }
    }
  }
}
</style>