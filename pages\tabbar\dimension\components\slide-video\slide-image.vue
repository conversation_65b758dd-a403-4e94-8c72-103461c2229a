<template>
  <view class="slide-image-container">
    <swiper class="swiper-wrapper" :indicator-dots="displayList.length > 1" :circular="true" autoplay
      indicator-color="rgba(255, 255, 255, 0.5)" indicator-active-color="#FE58B7">
      <swiper-item v-for="(item, index) in displayList" :key="index">
				<view class="image-wrapper">
					<image 
						:src="item" 
						class="slide-image" 
						mode="widthFix"
						:lazy-load="true"
					/>
				</view>
			</swiper-item>
    </swiper>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'

const props = defineProps({
  // 封面图片
  cover: {
    type: String,
    default: ''
  },
  // 图片资源数组
  resources: {
    type: Array,
    default: () => []
  }
})
const store = useStore()
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

const displayList = computed(() => {
  const list = props.resources.map(item => (prefixFilePath(item.url)))
  if (props.cover) {
    list.unshift(prefixFilePath(props.cover))
  }
  return list
})

function prefixFilePath(path) {
  if (path) {
    return cloudFileUrl.value + path
  }
  return ''
}
</script>

<style lang="scss" scoped>
.slide-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.swiper-wrapper {
  width: 100%;
  height: calc(100% - 70rpx);
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide-image {
  width: 100%;
  max-height: 100%;
  object-fit: contain;
}
</style>
