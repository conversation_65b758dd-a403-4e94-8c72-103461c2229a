import * as locationApi from '@/api/common'

// 地址状态模块
const locationStore = {
	namespaced: true, // 添加命名空间
	state: {
		provinces: [],
		cities: [],
		districts: [],
		province: {},
		city: {},
		district: {},
		geoLocation: null, // 当前用户位置
	},
	getters: {
		provinceNames(state) {
			return state.provinces.map(item => item.abbreviation)
		},
	},
	mutations: {
		updateProvinces(state, payload) {
			state.provinces = payload
		},
		updateCities(state, payload) {
			state.cities = payload
		},
		updateDistricts(state, payload) {
			state.districts = payload
		},
		updateProvince(state, payload) {
			state.province = payload
		},
		updateCity(state, payload) {
			state.city = payload
		},

		updateGeoLocation(state, payload) {
			state.geoLocation = payload;
		}
	},
	actions: {
		initProvinces({
			state,
			commit,
			dispatch,
			rootState
		}) {
			dispatch('fetchProvinces').then(() => {
				const provinceCache = uni.getStorageSync('location_code')
				if (provinceCache) {
					dispatch('setLocationByCode', provinceCache)
				} else {
					// 获取地理位置
					uni.getLocation({
						type: 'gcj02',
						success: function(res) {
							commit("updateGeoLocation", {
								longitude: res.longitude,
								latitude: res.latitude
							});
							uni.request({
								method: 'GET',
								// url: `https://restapi.amap.com/v3/geocode/regeo?output=json&location=${res.longitude},${res.latitude}&key=${rootState.common.amapKey}&extensions=base`, // 高德
								url: `https://apis.map.qq.com/ws/geocoder/v1/?location=${res.latitude},${res.longitude}&key=${rootState.common.tencentKey}`, // 腾讯
								success: function(res) {
									try {
										// const code = res.data.regeocode.addressComponent.adcode // 高德
										const code = res.data.result.ad_info
											.adcode // 腾讯
										dispatch('setLocationByCode', code)

										// 首次获取位置后重新更新首页数据
										// 漫展数据重新获取
										uni.$emit('location-change-refresh-expos')
										// 日常区数据重新获取
										uni.$emit(
											'location-change-refresh-dimension')
									} catch (e) {
										console.log(e)
										dispatch('setLocationByCode', '1101')
									}
								},
								fail: function(err) {
									dispatch('setLocationByCode', '1101')
								}
							})
						},
						fail: function(err) {
							console.log('获取位置失败', err)
						}
					})
				}
			})
		},
		setLocationByCode({
			state,
			commit,
			dispatch
		}, code) {
			uni.setStorageSync('location_code', code)
			const p = state.provinces.find(item => item.code === code.substring(0, 2))
			if (p) {
				commit('updateProvince', p)
				return dispatch('fetchCities', p.id).then(() => {
					const c = state.cities.find(item => item.code === code.substring(2, 4))
					if (c) {
						commit('updateCity', c)
					}
					return Promise.resolve() // 确保返回Promise
				})
			}
			return Promise.resolve() // 如果没有找到province，也返回一个resolved的Promise
		},
		fetchProvinces({
			commit
		}) {
			return locationApi.getSystemProvince().then(res => {
				if (res.code === 20000) {
					commit('updateProvinces', res.data)
				}
				return res
			}).catch(e => {
				console.log('getSystemProvince err:', e)
			})
		},
		fetchCities({
			state,
			commit
		}, province_id) {
			const p = state.provinces.find(item => item.id === province_id)
			if (p) {
				commit('updateProvince', p)
			}
			return locationApi.getSystemCity(province_id).then(res => {
				if (res.code === 20000) {
					commit('updateCities', res.data)
				}
				return res
			}).catch(e => {
				console.log('getSystemCity err:', e)
			})
		},
		fetchDistricts({
			commit
		}, city_id) {
			return locationApi.getSystemDistrict(city_id).then(res => {
				if (res.code === 20000) {
					commit('updateDistricts', res.data)
				}
				return res
			}).catch(e => {
				console.log('getSystemDistrict err:', e)
			})
		},
		getGeoLocation({
			commit,
			dispatch
		}) {
			return uni.getLocation({
				type: "gcj02",
				isHighAccuracy: true,
				highAccuracyExpireTime: 10 * 1000,
				success: res => {
					console.log("当前经纬度:", res);
					commit("updateGeoLocation", {
						longitude: res.longitude,
						latitude: res.latitude
					});
				},
				fail: (err) => {
					const errMsg = err.errMsg;
					if (errMsg && !errMsg.includes("auth deny")) return;
					console.log("获取经纬度失败：", err)
					wx.showModal({
						title: "提示",
						content: "获取地址需要打开位置权限",
						confirmText: "前往设置",
						showCancel: false,
						success: (res) => {
							if (!res.confirm) return;
							wx.openSetting({
								success: (res1) => {
									if (!res1.authSetting['scope.userLocation']) return;
									dispatch("getGeoLocation");
								}
							});
						}
					})
				}
			});
		}
	}
}

export default locationStore