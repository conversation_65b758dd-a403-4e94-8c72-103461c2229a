<template>
  <view class="app-container">
    <Navbar :type="1" leftText="我的钱包"></Navbar>

    <view class="wrapper">
      <view class="wallet-card">
        <image class="card-bg" src="/static/img/walllet/<EMAIL>"></image>
        <view class="main">
          <view class="header" @click="verifyIdCard">
            <text>我的余额</text>
            <image class="icon" src="/static/my/<EMAIL>"></image>
          </view>
          <view class="price">
            <text class="price-prefix">￥</text>
            <text>{{ balanceRef.balance }}</text>
          </view>
          <view class="extra-price">
            <view class="extra-item">
              <image class="icon" src="/static/my/<EMAIL>"></image>
              <text class="label">可用余额</text>
              <text class="value">{{ balanceRef.enableBalance }}</text>
            </view>
            <view class="extra-item">
              <image class="icon" src="/static/my/<EMAIL>"></image>
              <text class="label">冻结余额</text>
              <text class="value">{{ balanceRef.freeze }}</text>
            </view>
          </view>
          <view class="divide"></view>
          <view class="footer">
            <view class="footer-action" @click="() => handleTo('top-up')">
              <image class="left-icon" src="/static/my/<EMAIL>"></image>
              <text>充值</text>
              <image class="right-icon" src="/static/my/<EMAIL>"></image>
            </view>
            <view class="divide-y"></view>
            <view class="footer-action" @click="() => handleTo('withdrawal')">
              <image class="left-icon" src="/static/my/<EMAIL>"></image>
              <text>提现</text>
              <image class="right-icon" src="/static/my/<EMAIL>"></image>
            </view>
          </view>
        </view>
      </view>

      <view class="bill">
        <view class="header">
          <view class="header-item" @click="() => openPopup('all')">
            <text>全部账单</text>
            <image src="/static/icon/<EMAIL>" class="triangle-icon"></image>
          </view>
          <view class="header-item" @click="openDatetimePicker">
            <text>{{ datetime.label }}</text>
            <image src="/static/icon/<EMAIL>" class="triangle-icon"></image>
          </view>
        </view>
        <view class="divide"></view>
        <view class="main">
          <z-paging ref="paging" v-model="dataList" @query="queryList" :fixed="false">
            <template #empty>
              <uv-empty />
            </template>

            <uni-list class="dust-uni-list" :border="false">
              <uni-list-item :border="false" class="dust-uni-list-item" v-for="(item, index) in dataList"
                :key="item.id">
                <template v-slot:body>
                  <view class="bill-item-wrapper">
                    <view class="title">
                      <text class="label">{{ item.description }}</text>
                      <text class="bill-price">{{ destinationMap[item.destination] || ''}}{{ (item.amount /
                        100)
                        }}</text>
                    </view>
                    <view class="time">{{ timeToString(item.created_at, 2, '-') }}</view>
                  </view>
                </template>
              </uni-list-item>
            </uni-list>
          </z-paging>
        </view>
      </view>
    </view>
  </view>

  <uv-popup ref="popup" mode="bottom" bgColor="none">
    <choose-type @onClose="closePopup" @onSelect="chooseBillType"></choose-type>
  </uv-popup>

  <uv-datetime-picker ref="datetimePicker" v-model="datetime.value" mode="year-month" confirmColor="#FE58B7" round="16"
    @confirm="datetimeConfirm"></uv-datetime-picker>
</template>

<script setup>
import {
  ref,
  computed,
  onMounted,
} from "vue";
import {
  useStore
} from 'vuex';
import { timeToString } from '@/common/utils'
import { getMineBalance, getMineRecord, getBankCard } from '@/api/wallet';
import ChooseType from "./components/choose-type.vue";

const store = useStore();
const userInfo = computed(() => store.state.userInfo)
const selection = ref([])

const paging = ref(null)
const dataList = ref([])
const hadBankcard = ref(false)
const firstBankcard = ref(null)
const popup = ref(null)
const balanceRef = ref({
  balance: 0,
  freeze: 0,
  enableBalance: 0
})

const destinationMap = ref({
  1: '+',
  2: '-'
})

const datetimePicker = ref(null)
const datetime = ref({
  label: timeToString(new Date(), 6),
  value: Number(new Date()),
})

const openDatetimePicker = () => {
  datetimePicker.value.open()
}
const datetimeConfirm = ({ value }) => {
  datetimePicker.value.close()
  datetime.value.label = timeToString(new Date(value), 6)
  datetime.value.value = value
  paging.value.reload()
}

onMounted(() => {
  queryBalance()
  queryBankCard()
})

async function reloadData() {
  queryBalance()
  queryBankCard()
  paging.value.reload()
}

uni.$on('wallet-page-reload-data', reloadData)

function getMonthFirstAndLastDay(timestamp) {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = date.getMonth();
  const firstDay = new Date(year, month, 1).getTime();
  const lastDay = new Date(year, month + 1, 0).getTime();
  return { firstDay, lastDay };
}

async function queryBalance() {
  try {
    const res = await getMineBalance()
    if (res.code === 20000) {
      balanceRef.value.balance = res.data.balance / 100
      balanceRef.value.freeze = res.data.freeze / 100
      balanceRef.value.enableBalance = (res.data.balance - res.data.freeze) / 100
    }
  } catch (error) {
    console.log(error)
  }
}

async function queryBankCard() {
  if (userInfo.value.cert_level <= 0) return
  try {
    const res = await getBankCard()
    if (res.code === 20000) {
      hadBankcard.value = res.data.length > 0
      firstBankcard.value = res.data?.[0] || null
    }
  } catch (error) {
    console.log(error, 'getBankCard')
  }
}

async function queryList(pageNo, pageSize) {
  const { firstDay, lastDay } = getMonthFirstAndLastDay(datetime.value.value)
  const params = {
    page: pageNo,
    size: 10,
    type: selection.value,
    start_datetime: firstDay,
    end_datetime: lastDay,
    status: 0
  }
  let res = await getMineRecord(params)
  if (res.code === 20000) {
    paging.value.complete(res.data.results);
  } else {
    paging.value.complete(false);
  }
}

function openPopup(type) {
  popup.value.open()
}

function closePopup() {
  popup.value.close()
}

function chooseBillType(value) {
  const apiValue = value.reduce((pre, cur) => pre + cur, 0)
  selection.value = apiValue
  closePopup()
  paging.value.reload()
}

function handleTo(type) {
  let url = ''
  if (userInfo.value.cert_level <= 0) {
    uni.navigateTo({
      url: '/pages/packageA/verify-idcard/verify-idcard'
    })
    return
  }
  if (type === 'top-up') {
    url = '/pages/packageA/wallet-top-up/wallet-top-up'
  }
  if (type === 'withdrawal') {
    url = hadBankcard.value ?
      `/pages/packageA/wallet-withdrawal/wallet-withdrawal?balance=${balanceRef.value.enableBalance}&bankcardName=${firstBankcard.value?.bank_name}&bankcardId=${firstBankcard.value?.id}` :
      `/pages/packageA/wallet-bankcard/wallet-bankcard?notBind=${!hadBankcard}`
  }

  url && uni.navigateTo({
    url
  })
}

</script>

<style lang="scss" scoped>
.wrapper {
  flex: 1;
  width: 100%;
  margin-top: 32rpx;
  box-sizing: border-box;
  padding: 0 24rpx;
  display: flex;
  flex-direction: column;
  gap: 48rpx;

  .wallet-card {
    width: 100%;
    height: 360rpx;
    position: relative;
    color: #fff;
    border-radius: 24rpx;
    box-shadow: 0 10rpx 10rpx rgba($color: #FFBE0A, $alpha: 0.15);

    .card-bg {
      width: 100%;
      height: 100%;
      position: absolute;
      z-index: -1;
      top: 0;
      left: 0;
    }

    .main {
      width: 100%;
      height: 100%;

      .header {
        display: flex;
        gap: 16rpx;
        align-items: center;
        padding-left: 40rpx;
        padding-top: 34rpx;

        .icon {
          width: 32rpx;
          height: 32rpx;
        }
      }

      .price {
        width: 100%;
        height: 64rpx;
        text-align: center;
        font-weight: 500;
        font-size: 40rpx;
        line-height: 40rpx;
        margin-top: 32rpx;

        .price-prefix {
          font-size: 20rpx;
          line-height: 40rpx;
        }
      }

      .extra-price {
        width: 100%;
        box-sizing: border-box;
        padding: 0 76rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .extra-item {
          display: flex;
          align-items: center;

          .icon {
            width: 32rpx;
            height: 32rpx;
          }

          .label {
            margin-left: 12rpx;
            margin-right: 8rpx;
            font-size: 28rpx;
          }

          .value {
            font-size: 32rpx;
            font-weight: 500;
          }
        }
      }

      .divide {
        width: 100%;
        height: 2rpx;
        margin-top: 32rpx;
        margin-bottom: 22rpx;
        background-color: rgba($color: #fff, $alpha: 0.3);
      }

      .footer {
        display: flex;
        gap: 72rpx;
        align-items: center;
        justify-content: center;

        .divide-y {
          width: 2rpx;
          height: 40rpx;
          background-color: rgba($color: #fff, $alpha: 0.3);
        }

        .footer-action {
          font-weight: 500;
          font-size: 36rpx;
          display: flex;
          align-items: center;

          .left-icon {
            width: 48rpx;
            height: 48rpx;
            margin-right: 8rpx;
          }

          .right-icon {
            width: 32rpx;
            height: 32rpx;
            margin-left: 4rpx;
          }
        }
      }
    }


  }

  .bill {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;

    .header {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-item {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .triangle-icon {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }

    .divide {
      width: 100vw;
      height: 2rpx;
      background: #EEEEEE;
      margin-top: 28rpx;
      margin-bottom: 32rpx;
      transform: translateX(-24rpx);
    }

    .main {
      width: 100%;
      flex: 1;

      .bill-item-wrapper {
        width: 100%;
        height: 100rpx;
        box-sizing: border-box;
        padding: 0 8rpx;
        border-bottom: 2rpx solid #eee;

        .title {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .label {
            font-size: 28rpx;
            color: #1F1F1F;
          }

          .bill-price {
            font-size: 32rpx;
            color: #1F1F1F;
          }
        }

        .time {
          font-size: 28rpx;
          color: #85878D;
        }
      }
    }
  }
}
</style>