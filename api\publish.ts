import { requestApi } from './request';

export interface PublishActivityRequest {
	name: string, // 活动标题
	cover: string, // 活动封面
	contact?: string, // 联系方式
	type?: number, 	// 活动类型
	introduction: string, // 活动描述
	detail: string; // 详情图片
	max_people: number; // 限制人数
	editor: "json",
	organizer: "个人主办",
	start_time: number; // 活动开始时间
	end_time: number; // 活动结束时间
	categories: string[]; // 服务类型
	sponsor: string; // 服务类型
	location: string;
	
	province: number, //广东省
	city: number, //广州市
	district: number, //天河区
	longitude?: number,
	latitude?: number;
}
export interface Response {
	code: number; // 2000000为成功
	msg: string; // 失败信息
}

/** 创建发布活动 */
export const createPublishAvtivity = (params: PublishActivityRequest) => {
	return requestApi<Response>({
		url: "/api/v1/foreground/mine/expo",
		data: params,
		loading: false,
	})
}


interface UserInfo {
	"id": string; // 关注的id
	"account": string; // 用户的id
	"username": string; // 用户昵称
	"nickname": string; 
	"avatar": string; // // 用户头像
}

interface UserListResponse {
	count: number; // 返回的个数
	results?: UserInfo[]	
}
/**
 * 获取alt用户列表
 * {{keyword：string}} 搜索参数
 */
export const getAltList = (params?: {keyword: string}) => {
	return requestApi<UserListResponse>({
		url: `/api/v1/foreground/mine/dimension/mentioned`,
		data: params ? params : undefined,
		method: "GET",
		loading: false,
	});
}

interface TopicResponse {
	count: number; // 返回的个数
	results?: Array<{
		id: string;
		name: string;
	}>	
}
/**
 * 获取话题
 * {{keyword: string}} 搜索参数
 */
export const getTopicList = (params?: {keyword: string}) => {
	return requestApi<TopicResponse>({
		url: `/api/v1/foreground/mine/dimension/subject`,
		data: params ? params : undefined,
		method: "GET",
		loading: false,
	});
}

interface ResourceItem {
	"url": string,
	"mime": `image/${string}` | `media/${string}`,
	"type": number, //1：任意 2：视频 3：图文 4：音频
	"sequence": number //顺序
}

interface AddressInfo {
	"province_id"?: number, //省份 ID，可为 null
	"city_id"?: number, //城市 ID，可为 null
	"district_id"?: number, //区县 ID，可为 null
	"address"?: string,
	"longitude"?: number,
	"latitude"?: number,
}

interface PublishParams extends AddressInfo {
	"name": string, //话题
	"cover": string, //封面
	"description": string, //描述
	"publish_at": number, //定时发布时间戳
	"mentioned": string[]; // @用户
	"subjects": string[],  // 主题
	"resources": ResourceItem[]
}

interface  SendPublishParams extends PublishParams {
	"type": 1;
	"visibility": 2, //可见性 0-仅自己 1-所有人 2-好友 3-密友 4-指定用户
	"allow_download": 1, //允许下载 0-仅自己 1-所有人 2-好友 3-密友 4-指定用户
	"allow_forward": 1, //允许转发 0-仅自己 1-所有人 2-好友 3-密友 4-指定用户
	"allow_comment": 0, //允许评论 0-仅自己 1-所有人 2-好友 3-密友 4-指定用户
	"tags": [],
}

/** 创建发布次元 */
export const createPublishDimension = (params: PublishParams) => {
	const sendParams: SendPublishParams = {
		"type": 1,
		"visibility": 2,
		"allow_download": 1,
		"allow_forward": 1,
		"allow_comment": 0,
		"tags": [],
		...params,
	}
	return requestApi<Response>({
		url: "/api/v1/foreground/mine/dimension",
		data: sendParams,
		loading: false,
	})
}

// 发布-活动类型-列表
export const getPublishTypes = () => {
	return requestApi<Array<{name: string, id: number}>>({
		url: "/api/v1/foreground/mine/expo/type",
		loading: false,
		method: "GET",
	});
}

// 发布-活动类型-创建类型
export const createPublishType = (name: string) => {
	return requestApi<Response>({
		url: "/api/v1/foreground/mine/expo/type",
		data: {name: name},
		loading: false,
	});
}