<!-- 活动服务界面 类似于packageA/pages/service-create/type-->
<template>
  <z-paging ref="paging" class="app-container" :fixed="false">
    <template #top>
      <Navbar :type="1" leftText="活动服务"></Navbar>
    </template>
    <view class="container mt-32">
	  <view class="mt-24" v-for="item in selectOptions">
		<publish-service-select  :label="item.text" v-model="item.selected" @change="changeHandle($event, item)" />
	  </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useStore } from 'vuex';
import type { ServiceInfo } from "@/store/modules/services.js";
import { PUBLISH_ACTIVITY_SERVICE_SELECT } from "./const"
import { onLoad } from "@dcloudio/uni-app";

const store: any = useStore();
const storeServiceOption: ServiceInfo[]  = store.getters["service/serviceOption"];

interface Option {
	text: string, 
	value: number,
	selected: boolean,
}

const selectOptions = ref<Option[]>(storeServiceOption.map(item => ({text: item.name, value: item.value, selected: false})));
// 上个页面有传递选择时，页面需要显示选中
onLoad((query) => {
	console.log("传递的参数：", query);
	const selectedStr: string = query.selected;
	if (!selectedStr) return;
	const selectedTypes: Array<number | string> = selectedStr.split(",");
	selectOptions.value.forEach(item => {
		item.selected = selectedTypes.findIndex(value => value == item.value) >= 0;
	});
})
const changeHandle = function(selected: boolean, item: Option) {
	const params = {selected, ...item};
	console.log("选中项：", params);
	uni.$emit(PUBLISH_ACTIVITY_SERVICE_SELECT, params);
}

</script>

<style scoped lang='scss'>
.mt-24 {
  margin-top: 24rpx;
}

.mt-32 {
  margin-top: 32rpx;
}

.container {
  width: 100%;
  box-sizing: border-box;
  padding: 0 24rpx;
}

.feature-item-wrapper {
  width: 100%;
  height: 80rpx;
  box-sizing: border-box;
  padding: 24rpx;
  background: #F7F8FA;
  border-radius: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .label {
    font-size: 32rpx;
    color: #3D3D3D;
    line-height: 32rpx;
  }

  .icon {
    width: 40rpx;
    height: 40rpx;
  }
}
</style>