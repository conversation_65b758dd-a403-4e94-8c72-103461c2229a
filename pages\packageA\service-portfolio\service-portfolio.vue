<template>
  <z-paging class="app-container" ref="paging" v-model="dataList" @query="queryList">
    <template #top>
      <Navbar :type="1" leftText="作品集"></Navbar>
    </template>
    <view class="wrapper">
      <view class="container">
        <view class="header">
          <view class="">全部 ({{ total }})</view>
          <view class="action-btn" @click="handleEdit">
            {{ isEditing ? '取消' : '编辑' }}
          </view>
        </view>
        <scroll-view scroll-y="true" class="content">
          <view class="content">
            <view class="item-wrapper" v-for="item in dataList" :key="item.id"
              @click="() => handleCheckboxClick(item.id)">
              <view class="item-image-wrapper">
                <image class="item-image" mode="aspectFit" :src="prefixImageUrl(item.url)"></image>
                <!-- 选中蒙层 -->
                <view v-show="isEditing && handleChecked(item.id)" class="item-mask"></view>
              </view>
              <image v-show="isEditing" class="item-checkbox"
                :src="handleChecked(item.id) ? '../static/service/<EMAIL>' : '../static/service/<EMAIL>'">
              </image>
            </view>
          </view>
        </scroll-view>

      </view>
    </view>
    <template #bottom>
      <view class="footer">
        <view class="primary-btn" :class="{ 'delete-btn': isEditing }" @click="handleFooterClick">
          {{ isEditing ? '删除' : '上传作品' }}
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import { onLoad } from '@dcloudio/uni-app'
import { getArtworks, addArtworks, deleteArtworks } from '@/api/dimensionService';
import { uploadFile } from '@/api/request';

const store = useStore();
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

const paging = ref(null)
const serviceId = ref(null)
const isEditing = ref(false)
const checklistValue = ref([])
const dataList = ref([])
const total = ref(0)

const prefixImageUrl = (filepath) => {
  if (filepath) {
    return cloudFileUrl.value + filepath
  }
  return ''
}

const queryList = async (pageNo) => {
  if (!!serviceId.value) {
    const params = {
      page: pageNo,
      size: 10,
    }
    const res = await getArtworks(serviceId.value, params)
    if (res.code === 20000) {
      total.value = res.data.count
      paging.value.complete(res.data.results);
    }
  }
}

onLoad((option) => {
  serviceId.value = option.id
  // queryList()
})

const handleEdit = () => {
  if (isEditing.value) {
    checklistValue.value = []
  }
  isEditing.value = !isEditing.value
}

const handleChecked = (id) => {
  return checklistValue.value.includes(id)
}

const handleCheckboxClick = id => {
  if (isEditing.value) {
    const checklist = checklistValue.value
    if (checklist.includes(id)) {
      // 如果已选中，移除该项
      checklistValue.value = checklist.filter(item => item !== id)
    } else {
      // 如果未选中，添加该项
      checklistValue.value = [...checklist, id]
    }
  } else {
    // 使用uni.previewImage预览图片
    const urls = dataList.value.map(item => prefixImageUrl(item.url))
    const currentIndex = dataList.value.findIndex(item => item.id === id)
    
    uni.previewImage({
      urls: urls,
      current: currentIndex,
    })
  }
}

const uploadArtwork = async (params) => {
  try {
    const res = await addArtworks(serviceId.value, params)
    if (res.code === 20000) {
      paging.value.reload()
    }
  } catch (error) {
    console.log(error)
  }
}

const deleteArtwork = async (id) => {
  try {
    const res = await deleteArtworks(serviceId.value, id)
    if (res.code === 20000) {
      paging.value.reload()
    }
  } catch (error) {
    console.log(error)
  }
}

const handleFooterClick = () => {
  if (isEditing.value) {
    // 删除
    checklistValue.value.forEach(id => {
      deleteArtwork(id)
    })
    checklistValue.value = []
    isEditing.value = false
  } else {
    uni.chooseImage({
      count: 9,
      success: ({ tempFilePaths, tempFiles }) => {
        tempFilePaths.forEach(async imagePath => {
          const mimeTypes = {
            jpg: 'image/jpeg',
            jpeg: 'image/jpeg',
            png: 'image/png',
            gif: 'image/gif',
            bmp: 'image/bmp',
            webp: 'image/webp',
          }
          const extension = imagePath.split('.').pop().toLowerCase()

          if (imagePath) {
            const res = await uploadFile(imagePath)
            if (res.code === 20000) {
              const params = {
                type: 1,
                url: res.data.url,
                mime_type: mimeTypes[extension] || 'application/octet-stream'
              }
              uploadArtwork(params)
            }
          }
        })
      }
    })
  }
}



</script>

<style scoped lang='scss'>
.primary-btn {
  width: 100%;
  height: 72rpx;
  background: #FE58B7;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 36rpx;
  color: #FFFFFF;
  line-height: 36rpx;

  &.delete-btn {
    background: #FE585B;
  }
}

.wrapper {
  width: 100%;
  height: 80%;

  .container {
    box-sizing: border-box;
    padding: 0 24rpx;
    margin-top: 38rpx;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .header {
      margin-bottom: 52rpx;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 28rpx;
      color: #979797;
      line-height: 36rpx;

      .action-btn {
        font-size: 28rpx;
        color: #FE58B7;
        text-align: center;
      }
    }

    .content {
      height: 100%;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      gap: 24rpx;

      .item-wrapper {
        position: relative;
        width: 218rpx;
        height: 218rpx;
        border-radius: 24rpx;
        overflow: hidden;

                  .item-checkbox {
            position: absolute;
            top: 16rpx;
            right: 16rpx;
            width: 42rpx;
            height: 42rpx;
            z-index: 2;
          }

        .item-image-wrapper {
          width: 100%;
          height: 100%;
          background-color: #000;
          position: relative;

          .item-image {
            width: 100%;
            height: 100%;
          }

          .item-mask {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1;
          }
        }
      }
    }
  }
}



.footer {
  width: 100%;
  box-sizing: border-box;
  padding: 48rpx 24rpx;
  background: #fff;
  box-shadow: 0 0 16rpx 0 rgba(35, 36, 37, 0.06);
}
</style>