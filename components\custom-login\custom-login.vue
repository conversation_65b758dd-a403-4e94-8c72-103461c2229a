<template>
	<view>
		<view class="btn-bar">
			<button v-if="!isConsent" @tap="showTip = true" class="login-btn">
				<image v-if="iconLeft" class="wx" src="/static/icon/wx.png"></image>
				<text>{{ title }}</text>
			</button>
			<button v-else class="login-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">
				<image v-if="iconLeft" class="wx" src="/static/icon/wx.png"></image>
				<text>{{ title }}</text>
			</button>
		</view>
		<view class="consent" @tap="changeConsent">
			<image v-if="showTip" class="tip-icon" src="/static/icon/xieyi_tip.png"></image>
			<image class="consent-icon" v-bind:src="isConsent ? '/static/icon/check-on.png' : '/static/icon/check.png'">
			</image>
			<view class="text">
				登录代表您已阅读并同意<text class="code" @tap.stop="navAgree('1')">《用户协议》</text>和<text class="code"
					@tap.stop="navAgree('2')">《隐私政策》</text>
			</view>
		</view>
	</view>
</template>

<script setup name="custom-login">
import { ref } from 'vue'

defineProps({
	title: {
		type: String,
		default: '手机号快捷登录'
	},
	iconLeft: {
		type: Boolean,
		default: false,
	},
});
const emit = defineEmits(['onLogin'])
let isConsent = ref(false)
let showTip = ref(false)

// 修改协议状态
const changeConsent = () => { // 打开弹出
	if (isConsent.value) {
		isConsent.value = false
	} else {
		isConsent.value = true
		showTip.value = false
	}
}
// 判断是否同意协议
const getPhoneNumber = (e) => {
	console.log('getPhoneNumber:', e)
	uni.getUserInfo({
		provider: 'weixin',
		success: (res0) => {
			console.log('res0:', res0)
			emit('onLogin', {
				phone_code: e.detail.code,
			})
		},
		fail: (e0) => {
			console.log('登录失败:', e0)
		}
	})
}
// 是否勾选协议
const verify = (e) => {
	isConsent.value = e
	// this.$refs.popup.close()
}
// 跳转协议
const navAgree = (type) => {
	// uni.navigateTo({
	// 	url: `/pagesB/agreement/agreement?type=${type}`
	// })
}

</script>

<style lang="scss" scoped>
.btn-bar {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 84rpx;

	.wx {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
		vertical-align: middle;
	}

	.login-btn {
		background-color: $uni-color-primary;
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 16rpx;
		color: #fff;
		width: 100%;
		font-size: 32rpx;
		font-weight: 500;
	}
}

.consent {
	align-items: center;
	justify-content: center;
	display: flex;
	font-size: 24rpx;
	margin-top: 88rpx;
	position: relative;

	.consent-icon {
		width: 26rpx;
		height: 26rpx;
	}

	.text {
		display: inline-block;
		line-height: 35rpx;
		color: #666666;
		padding-left: 10rpx;
	}

	.code {
		display: inline-block;
		line-height: 35rpx;
		color: $uni-color-primary;
		vertical-align: top;
		padding-left: 10rpx;
	}
}

.tip-icon {
	position: absolute;
	bottom: 40rpx;
	left: 4rpx;
	width: 290rpx;
	height: 61.5rpx;
}
</style>