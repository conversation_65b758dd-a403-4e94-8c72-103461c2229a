<template>
	<z-paging class="app-container" ref="paging" v-model="dataList" @query="queryList" :safe-area-inset-bottom="true"
		:use-safe-area-placeholder="true">
		<template #top>
			<Navbar :type="1" leftWidth="20px" :showSearch="true" placeholder="搜索关键字" @confirm="onSearch" @clear="onClear"></Navbar>
		</template>
		<view class="tabs-container">
			<dust-tabs v-model="activeTab" :list="TAB_LIST" tabType="secondary" lineGradientBorder
				showActiveStar></dust-tabs>
		</view>
		<view class="data-view">
			<template v-if="activeTab === 0">
				<user-item v-for="(item, index) in dataList" :key="item.id" :data="item" />
			</template>
			<template v-if="activeTab === 1">
				<expos-items v-if="dataList.length > 0" :dataList="dataList"/>
			</template>
			<template v-if="activeTab === 2">
				<dimension-item :dataList="dataList" />
			</template>
			<template v-if="activeTab === 3 || activeTab === 4">
				<daily-life :dataList="dataList" />
			</template>
			<template v-if="activeTab === 5">
				<topic-item :dataList="dataList" />
			</template>
		</view>
	</z-paging>
</template>

<script setup>
	import {
		ref,
		computed,
		watch
	} from 'vue';
	import {
		useStore
	} from 'vuex';
	import {
		getSearchData
	} from '@/api/common'
	import UserItem from './components/userItem.vue'
	import DimensionItem from './components/dimension-item.vue'
	import DailyLife from './components/daily-life.vue'
	import TopicItem from './components/topic-item.vue'

	const TAB_LIST = [{
			name: '用户',
			value: 0
		},
		{
			name: '漫展',
			value: 1
		},
		{
			name: '次元',
			value: 2
		},
		{
			name: '约拍',
			value: 3
		},
		{
			name: '约妆',
			value: 4
		},
		{
			name: '话题',
			value: 5
		}
	];

	const store = useStore();
	// 获取当前用户ID
	const currentUserId = computed(() => store.state.userInfo?.id);

	const paging = ref(null);
	const dataList = ref([]);
	const activeTab = ref(0);
	const keyword = ref('')

	const apiType = computed(() => {
		switch (activeTab.value) {
			case 0:
				return {
					type: 'user'
				};
			case 1:
				return {
					type: 'expo'
				};
			case 2:
				return {
					type: 'dimension'
				};
			case 3:
				return {
					type: 'service', categories: 1
				};
			case 4:
				return {
					type: 'service', categories: 2
				};
			case 5:
				return {
					type: 'subject'
				};
			default:
				return {
					type: 'user'
				};
		}
	});
	
	watch(activeTab, () => {
		paging.value.reload();
	})

	function onSearch(e) {
		console.log('onSearch:', e)
		keyword.value = e.value
		if (keyword.value) {
			paging.value.reload();
		} else {
			uni.showToast({
				title: '请输入关键字',
				icon: 'none'
			})
			paging.value.complete([]);
		}
	}
	
	function onClear() {
		keyword.value = ''
		paging.value.reload();
	}

	async function queryList(pageNo, pageSize) {
		if (!keyword.value) {
			paging.value.complete([]);
			return
		}
		try {
			const res = await getSearchData({
				...apiType.value,
				keyword: keyword.value,
				page: pageNo,
			});

			if (res.code === 20000) {
				let results = res.data.results;

				// 对用户类型进行过滤：排除当前用户自身
				if (apiType.value.type === 'user' && currentUserId.value) {
					results = results.filter(item => item.id !== currentUserId.value);
				}
				paging.value.complete(results);
			} else {
				paging.value.complete([]);
			}
		} catch (error) {
			console.error('搜索请求失败:', error);
			paging.value.complete(false);
		}
	}
</script>

<style lang="scss">
	.tabs-container {
		margin-top: 28rpx;
		margin-bottom: 16rpx;
		width: 100%;
		height: 72rpx;
		display: flex;
		align-items: center;

		:deep(.dust-tabs-container) {
			gap: 20px !important;
			padding: 0 38rpx !important;
		}
	}

	.data-view {
		padding: 0 32rpx;
	}
</style>