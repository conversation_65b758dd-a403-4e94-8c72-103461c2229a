<template>
  <view class="wrapper">
    <view class="title">筛选类型</view>
    <view class="types-wrapper">
      <view class="type-item" :class="{ active: selection.includes(item.value) }" v-for="(item, idx) in billTypes"
        :key="idx" @click="() => selectType(item.value)">
        {{ item.label }}
      </view>
    </view>
    <view class="divide-line"></view>
    <view class="footer">
      <view class="btn" @click="handleCancel">取消</view>
      <view class="btn primary" @click="handleCommit">确认</view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
const emit = defineEmits(['onClose', 'onSelect'])

const billTypes = ref([
  { label: '充值', value: 512 },
  { label: '提现', value: 1024 },
  { label: '返佣', value: 0 },
  { label: '服务消费', value: 1 },
  { label: '服务收入', value: 2 },
  { label: '解冻/退款', value: 508 }
])
const selection = ref([])

const selectType = (type) => {
  const selected = selection.value
  if (selected.includes(type)) {
    const idx = selected.findIndex(item => item === type)
    selection.value.splice(idx, 1)
  } else {
    selection.value.push(type)
  }
}

const handleCommit = () => {
  emit('onSelect', selection.value)
}

const handleCancel = () => {
  emit('onClose')
}
</script>

<style scoped lang='scss'>
.wrapper {
  width: 100%;
  height: 564rpx;
  background: #FFFFFF;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  .title {
    font-size: 36rpx;
    color: #3D3D3D;
    width: 100%;
    text-align: center;
    padding-top: 28rpx;
  }

  .types-wrapper {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2);
    gap: 24rpx;
    margin-top: 54rpx;

    .type-item {
      width: 214rpx;
      height: 80rpx;
      background: rgba($color: #EEEEEE, $alpha: 0.5);
      border-radius: 16rpx;
      font-size: 32rpx;
      color: #1F1F1F;
      transition: all .35s;
      text-align: center;
      line-height: 80rpx;

      &.active {
        background: #FE58B7;
        color: #fff;
      }
    }
  }

  .divide-line {
    width: 100%;
    height: 2rpx;
    background: #F4F4F4;
    margin-top: 70rpx;
    margin-bottom: 40rpx;
  }

  .footer {
    display: flex;
    align-items: center;
    gap: 30rpx;
    width: 100%;
    box-sizing: border-box;
    padding: 0 32rpx;

    .btn {
      flex: 1;
      height: 80rpx;
      background: #EEEEEE;
      border-radius: 48rpx;
      font-size: 36rpx;
      color: #1F1F1F;
      text-align: center;
      line-height: 80rpx;

      &.primary {
        background: #FE58B7;
        color: #fff;
      }
    }
  }
}
</style>