export const errorCode = [
    {
        "code": 0,
        "msg": "Unauthorized",
        "data": null,
        "description": {
            "en": "Unauthorized",
            "zh": "未登录"
        }
    },
    {
        "code": 1,
        "msg": "user is disabled",
        "data": null,
        "description": {
            "en": "User is disabled",
            "zh": "用户被禁用"
        }
    },
    {
        "code": 40000,
        "msg": "invalid form data",
        "data": {
            "field": "xxx"
        },
        "description": {
            "en": "Invalid request body",
            "zh": "请求体错误"
        }
    },
    {
        "code": 40001,
        "msg": "invalid param",
        "data": {
            "param": "xxx"
        },
        "description": {
            "en": "Invalid request path parameter",
            "zh": "路径参数错误"
        }
    },
    {
        "code": 40002,
        "msg": "invalid query",
        "data": {
            "query": "xxx"
        },
        "description": {
            "en": "Invalid request query parameter",
            "zh": "查询参数错误"
        }
    },
    {
        "code": 40010,
        "msg": "verification code not found",
        "data": null,
        "description": {
            "en": "Verification code does not exist",
            "zh": "验证码不存在"
        }
    },
    {
        "code": 40011,
        "msg": "verification code is invalid",
        "data": null,
        "description": {
            "en": "Verification code is invalid",
            "zh": "验证码无效"
        }
    },
    {
        "code": 40012,
        "msg": "record not found",
        "data": {
            "data": "xxx"
        },
        "description": {
            "en": "Data does not exist",
            "zh": "数据不存在"
        }
    },
    {
        "code": 40013,
        "msg": "user not found",
        "data": null,
        "description": {
            "en": "User does not exist",
            "zh": "用户不存在"
        }
    },
    {
        "code": 40014,
        "msg": "incorrect password",
        "data": null,
        "description": {
            "en": "Incorrect password",
            "zh": "密码错误"
        }
    },
    {
        "code": 40015,
        "msg": "email not found",
        "data": null,
        "description": {
            "en": "Email does not exist",
            "zh": "邮箱不存在"
        }
    },
    {
        "code": 40016,
        "msg": "phone not found",
        "data": null,
        "description": {
            "en": "Phone number does not exist",
            "zh": "手机号不存在"
        }
    },
    {
        "code": 40017,
        "msg": "not real name",
        "data": null,
        "description": {
            "en": "User is not real name",
            "zh": "用户未实名"
        }
    },
    {
        "code": 40018,
        "msg": "real name expired",
        "data": null,
        "description": {
            "en": "User real name is expired",
            "zh": "用户实名失效"
        }
    },
    {
        "code": 40019,
        "msg": "real name level",
        "data": null,
        "description": {
            "en": "User real name level is not enough",
            "zh": "用户实名等级不足"
        }
    }
]

export const CODE_MAPPING = {
  // 认证类错误
  0: { 
    msg: '未登录', 
    handleType: 'redirect', 
    redirectPath: '/login' 
  },
  1: { 
    msg: '用户被禁用', 
    handleType: 'redirect', 
    redirectPath: '/login' 
  },

  // 用户信息类错误
  40015: {
    msg: '邮箱不存在',
    handleType: 'redirect',
    redirectPath: '/set-email'
  },
  40016: {
    msg: '手机号不存在',
    handleType: 'redirect',
    redirectPath: '/set-phone'
  },
  
  // 实名认证类错误
  40017: {
    msg: '用户未实名',
    handleType: 'redirect',
    redirectPath: '/real-name-auth'
  },
  40018: {
    msg: '用户实名失效',
    handleType: 'redirect',
    redirectPath: '/real-name-auth'
  }
}
