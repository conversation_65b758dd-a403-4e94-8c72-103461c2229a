<script setup>
	import {
		ref,
		computed
	} from 'vue';
	import {
		useStore
	} from 'vuex';
	import {
		onLaunch,
		onShow
	} from '@dcloudio/uni-app';
	import * as auth from '@/common/auth';
	import {
		routingIntercept
	} from '@/common/permission';

	const store = useStore();
	const commonInfo = computed(() => store.state.common);

	onLaunch((options) => {
		uni.hideTabBar();
		console.log('App onLaunch：', options);
		store.dispatch('common/getAdminSetting')
		const sys = uni.getSystemInfoSync();
		console.log('sys:', sys);
		const token = auth.getToken();
		console.log('token:', token);
		store.dispatch('common/setCommonInfo', {
			curPage: options.path,
			sysInfo: sys
		});
		const inviteCode = auth.getInviteCode();
		if (inviteCode) {
			store.dispatch('common/setInviteCode', inviteCode);
		}

		store.dispatch('location/initProvinces');

		// 将token存储到状态管理中
		console.log('app onLaunch token:', token);
		if (!!token) {
			store.commit('userInfo/SET_TOKEN', token);
			store.dispatch('userInfo/getUserInfo');
		} else {
			store.commit('userInfo/SET_TOKEN', '');
		}
		// 对路由进行统一拦截，实现路由导航守卫 router.beforeEach 功能
		routingIntercept();

		uni.$on('resetLogin', resetLogin);
	});

	onShow(options => {
		uni.hideTabBar().catch(console.error);
		console.log('App onShow:', options)
		if (options.query && options.query.inviteCode) {
			store.dispatch('common/setInviteCode', options.query.inviteCode);
			auth.updateInviteCode(options.query.inviteCode)
		} else if (options.query && options.query.scene) {
			try {
				const scene = decodeURIComponent(options.query.scene)
				const [key, value] = scene.split('=');
				console.log('scene:', scene, key, value)
				if (key === 'inviteCode') {
					store.dispatch('common/setInviteCode', value);
					auth.updateInviteCode(value)
				}
			} catch (e) {
				console.log('参数解析失败:', e)
			}
		}
	})

	function resetLogin() {
		store.dispatch('userInfo/cleanUseInfo');
	}
</script>

<style lang="scss">
	page {
		height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.app-container {
		display: flex;
		flex-direction: column;
		flex: 1;
	}

	.ml-10 {
		margin-left: 10rpx;
	}

	.mt-20 {
		margin-top: 20rpx;
	}

	.mt-30 {
		margin-top: 30rpx;
	}

	.mx-30 {
		margin-left: 30rpx;
		margin-right: 30rpx;
	}

	.mt-60 {
		margin-top: 60rpx;
	}

	.pb-24 {
		padding-bottom: 24rpx;
	}

	.pt-20 {
		padding-top: 20rpx;
	}

	.px-24 {
		padding-left: 24rpx;
		padding-right: 24rpx;
	}

	.px-30 {
		padding-left: 30rpx;
		padding-right: 30rpx;
	}

	.mx-40 {
		margin-left: 40rpx;
		margin-right: 40rpx;
	}

	.px-40 {
		padding-right: 40rpx;
		padding-left: 40rpx;
	}

	.w-full {
		width: 100%;
	}

	.gap-26 {
		gap: 26rpx;
	}

	.gap-32 {
		gap: 32rpx;
	}

	.flex {
		display: flex;
	}

	.flex-1 {
		flex: 1;
	}

	.flex-col {
		flex-direction: column;
	}

	.justify-end {
		justify-content: flex-end;
	}

	.justify-between {
		justify-content: space-between;
	}

	.justify-center {
		justify-content: center;
	}

	.items-center {
		align-items: center;
	}

	.relative {
		position: relative;
	}

	image {
		vertical-align: middle;
	}

	::v-deep .dust-uni-list-item .uni-list-item__container {
		padding: 12rpx 0 !important;
	}

	::v-deep .uni-nav-bar {
		.uni-navbar__header-container {
			align-items: center;
			padding: 0 70rpx 0 36rpx !important;
		}

		.uni-searchbar {
			padding: 0 !important;

			.uni-searchbar__box {
				border: 4rpx solid #000;
				height: 64rpx !important;
				padding: 0 10rpx 0 0 !important;
			}

			.uni-searchbar__box-icon-search {
				padding: 0 0 0 16rpx !important;
			}

			.uni-searchbar__text-placeholder,
			.uni-searchbar__box-search-input {
				font-size: 26rpx !important;
			}

			.uni-searchbar__box-icon-clear {
				line-height: inherit !important;
			}
		}
	}

	.txtellipsis {
		overflow: hidden;
		text-overflow: ellipsis;
		-webkit-line-clamp: 2;
		display: -webkit-box;
		-webkit-box-orient: vertical;
	}

	// 16:9 图片
	.image-container {
		position: relative;
		width: 100%;
		/* 图片宽度为父容器宽度 */
		padding-top: 56.25%;
		/* 16:9 比例的高度占比，即 (9 / 16) * 100% */
		overflow: hidden;
	}

	.image-container .img {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		/* 图片宽度为父容器宽度 */
		height: 100%;
		/* 图片高度为父容器高度 */
		object-fit: cover;
		/* 图片填充整个容器，保持比例不变 */
	}

	.textHide {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.btn-submit {
		background-color: #FE58B7 !important;
		border-radius: 96rpx !important;
		color: #fff !important;
		font-size: 36rpx !important;
		height: 96rpx !important;
		line-height: 96rpx !important;
		align-items: center !important;
		width: 100%;
	}
	
	.eli,
	.eli2 {
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		/*! autoprefixer: off */
		-webkit-box-orient: vertical;
	}
	
	.eli {
		-webkit-line-clamp: 1;
	}
	
	.eli2 {
		-webkit-line-clamp: 2;
	}
	// 高斯模糊样式
	.matter-view {
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		width: 100%;
		background: rgba(255,255,255,0.3); 
		//设置半透明背景
		backdrop-filter: blur(2px); 
	}
</style>