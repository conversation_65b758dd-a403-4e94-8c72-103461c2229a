<template>
  <view class="city-select-container">
    <!-- 面包屑导航 -->
    <view class="breadcrumb">
      <view
        class="breadcrumb-item"
        :class="{ active: currentLevel === 'province' }"
        @click="goToLevel('province')"
      >
        {{ selectedProvince.name || '请选择省份' }}
      </view>
      <view
        v-if="selectedProvince.id"
        class="breadcrumb-item"
        :class="{ active: currentLevel === 'city' }"
        @click="goToLevel('city')"
      >
        {{ selectedCity.name || '请选择城市' }}
      </view>
      <view
        v-if="selectedCity.id && mode === 'province-city-district'"
        class="breadcrumb-item"
        :class="{ active: currentLevel === 'district' }"
        @click="goToLevel('district')"
      >
        {{ selectedDistrict.name || '请选择区县' }}
      </view>
    </view>

    <!-- 省份选择 -->
    <view v-if="currentLevel === 'province'" class="level-container">
      <uv-index-list :index-list="provinceIndexList" :custom-nav-height="customNavHeight">
        <template v-for="(item, index) in provinceItemArr" :key="index">
          <!-- #ifdef APP-NVUE -->
          <uv-index-anchor :text="provinceIndexList[index]"></uv-index-anchor>
          <!-- #endif -->
          <uv-index-item>
            <!-- #ifndef APP-NVUE -->
            <uv-index-anchor :text="provinceIndexList[index]"></uv-index-anchor>
            <!-- #endif -->
            <view
              class="list-cell"
              v-for="province in item"
              :key="province.id"
              @click="selectProvince(province)"
            >
              {{ province.name }}
            </view>
          </uv-index-item>
        </template>
      </uv-index-list>
    </view>

    <!-- 城市选择 -->
    <view v-if="currentLevel === 'city'" class="level-container">
      <view v-if="cityLoading" class="loading">
        <uv-loading-icon></uv-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>
      <uv-index-list v-else :index-list="cityIndexList" :custom-nav-height="customNavHeight">
        <template v-for="(item, index) in cityItemArr" :key="index">
          <!-- #ifdef APP-NVUE -->
          <uv-index-anchor :text="cityIndexList[index]"></uv-index-anchor>
          <!-- #endif -->
          <uv-index-item>
            <!-- #ifndef APP-NVUE -->
            <uv-index-anchor :text="cityIndexList[index]"></uv-index-anchor>
            <!-- #endif -->
            <view
              class="list-cell"
              v-for="city in item"
              :key="city.id"
              @click="selectCity(city)"
            >
              {{ city.name }}
            </view>
          </uv-index-item>
        </template>
      </uv-index-list>
    </view>

    <!-- 区县选择 -->
    <view v-if="currentLevel === 'district'" class="level-container">
      <view v-if="districtLoading" class="loading">
        <uv-loading-icon></uv-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>
      <uv-index-list v-else :index-list="districtIndexList" :custom-nav-height="customNavHeight">
        <template v-for="(item, index) in districtItemArr" :key="index">
          <!-- #ifdef APP-NVUE -->
          <uv-index-anchor :text="districtIndexList[index]"></uv-index-anchor>
          <!-- #endif -->
          <uv-index-item>
            <!-- #ifndef APP-NVUE -->
            <uv-index-anchor :text="districtIndexList[index]"></uv-index-anchor>
            <!-- #endif -->
            <view
              class="list-cell"
              v-for="district in item"
              :key="district.id"
              @click="selectDistrict(district)"
            >
              {{ district.name }}
            </view>
          </uv-index-item>
        </template>
      </uv-index-list>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import { getSystemCity, getSystemDistrict } from '@/api/common'
import pinyin from 'js-pinyin'

// Props
const props = defineProps({
  // 选择模式：province(省份), province-city(省市), province-city-district(省市区)
  mode: {
    type: String,
    default: 'province-city-district'
  },
  // 自定义导航栏高度
  customNavHeight: {
    type: [String, Number],
    default: 0
  },
  // 默认选中的值
  value: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['change', 'confirm'])

const store = useStore()
const provinces = computed(() => store.state.location.provinces)

// 当前选择层级
const currentLevel = ref('province')

// 选中的数据
const selectedProvince = ref({})
const selectedCity = ref({})
const selectedDistrict = ref({})

// 数据列表
const cities = ref([])
const districts = ref([])

// 加载状态
const cityLoading = ref(false)
const districtLoading = ref(false)

// 省份索引列表和数据
const provinceIndexList = computed(() => {
  const indexSet = new Set()
  provinces.value.forEach(item => {
    const firstLetter = getFirstLetter(item.name)
    indexSet.add(firstLetter)
  })
  return Array.from(indexSet).sort()
})

const provinceItemArr = computed(() => {
  const grouped = {}
  provinces.value.forEach(item => {
    const firstLetter = getFirstLetter(item.name)
    if (!grouped[firstLetter]) {
      grouped[firstLetter] = []
    }
    grouped[firstLetter].push(item)
  })

  return provinceIndexList.value.map(letter => grouped[letter] || [])
})

// 城市索引列表和数据
const cityIndexList = computed(() => {
  const indexSet = new Set()
  cities.value.forEach(item => {
    const firstLetter = getFirstLetter(item.name)
    indexSet.add(firstLetter)
  })
  return Array.from(indexSet).sort()
})

const cityItemArr = computed(() => {
  const grouped = {}
  cities.value.forEach(item => {
    const firstLetter = getFirstLetter(item.name)
    if (!grouped[firstLetter]) {
      grouped[firstLetter] = []
    }
    grouped[firstLetter].push(item)
  })

  return cityIndexList.value.map(letter => grouped[letter] || [])
})

// 区县索引列表和数据
const districtIndexList = computed(() => {
  const indexSet = new Set()
  districts.value.forEach(item => {
    const firstLetter = getFirstLetter(item.name)
    indexSet.add(firstLetter)
  })
  return Array.from(indexSet).sort()
})

const districtItemArr = computed(() => {
  const grouped = {}
  districts.value.forEach(item => {
    const firstLetter = getFirstLetter(item.name)
    if (!grouped[firstLetter]) {
      grouped[firstLetter] = []
    }
    grouped[firstLetter].push(item)
  })

  return districtIndexList.value.map(letter => grouped[letter] || [])
})

// 获取首字母
function getFirstLetter(name) {
  if (!name) return '#'

  try {
    // 使用 js-pinyin 库获取拼音
    const pinyinArray = pinyin.getFullChars(name)
    if (pinyinArray && pinyinArray.length > 0) {
      const firstChar = pinyinArray[0].charAt(0).toUpperCase()
      // 检查是否为有效的英文字母
      return /^[A-Z]$/.test(firstChar) ? firstChar : '#'
    }
  } catch {
    // 如果拼音库出错，尝试直接获取首字符
    const firstChar = name.charAt(0).toUpperCase()
    return /^[A-Z]$/.test(firstChar) ? firstChar : '#'
  }

  // 如果都不是，返回 #
  return '#'
}

// 选择省份
async function selectProvince(province) {
  selectedProvince.value = province
  selectedCity.value = {}
  selectedDistrict.value = {}

  if (props.mode === 'province') {
    // 只选择省份
    emitChange()
    return
  }

  // 需要选择城市
  currentLevel.value = 'city'
  await fetchCities(province.id)
}

// 选择城市
async function selectCity(city) {
  selectedCity.value = city
  selectedDistrict.value = {}

  if (props.mode === 'province-city') {
    // 只选择到城市
    emitChange()
    return
  }

  // 需要选择区县
  currentLevel.value = 'district'
  await fetchDistricts(city.id)
}

// 选择区县
function selectDistrict(district) {
  selectedDistrict.value = district
  emitChange()
}

// 获取城市列表
async function fetchCities(provinceId) {
  cityLoading.value = true
  try {
    const res = await getSystemCity(provinceId)
    if (res.code === 20000) {
      cities.value = res.data || []
    }
  } catch (error) {
    console.error('获取城市列表失败:', error)
    cities.value = []
  } finally {
    cityLoading.value = false
  }
}

// 获取区县列表
async function fetchDistricts(cityId) {
  districtLoading.value = true
  try {
    const res = await getSystemDistrict(cityId)
    if (res.code === 20000) {
      districts.value = res.data || []
    }
  } catch (error) {
    console.error('获取区县列表失败:', error)
    districts.value = []
  } finally {
    districtLoading.value = false
  }
}

// 跳转到指定层级
function goToLevel(level) {
  if (level === 'province') {
    currentLevel.value = 'province'
  } else if (level === 'city' && selectedProvince.value.id) {
    currentLevel.value = 'city'
  } else if (level === 'district' && selectedCity.value.id) {
    currentLevel.value = 'district'
  }
}

// 发送变化事件
function emitChange() {
  const result = {
    province: selectedProvince.value,
    city: selectedCity.value,
    district: selectedDistrict.value
  }

  emit('change', result)
  emit('confirm', result)
}

// 初始化拼音库
function initPinyin() {
  try {
    pinyin.setOptions({
      checkPolyphone: false,
      charCase: 0,
      type: 'array'
    })
  } catch (error) {
    console.error('拼音库初始化失败:', error)
  }
}

// 初始化默认值
function initDefaultValue() {
  if (props.value.province) {
    selectedProvince.value = props.value.province
  }
  if (props.value.city) {
    selectedCity.value = props.value.city
  }
  if (props.value.district) {
    selectedDistrict.value = props.value.district
  }

  // 根据已选择的数据确定当前层级
  if (selectedDistrict.value.id) {
    currentLevel.value = 'district'
  } else if (selectedCity.value.id) {
    currentLevel.value = 'city'
  } else if (selectedProvince.value.id) {
    currentLevel.value = 'city'
  } else {
    currentLevel.value = 'province'
  }
}

// 监听props.value变化
watch(() => props.value, () => {
  initDefaultValue()
}, { deep: true, immediate: true })

// 初始化
onMounted(() => {
  initPinyin()
})
</script>

<style lang="scss" scoped>
.city-select-container {
  height: 100vh;
  background-color: #f5f5f5;
}

.breadcrumb {
  display: flex;
  align-items: center;
  padding: 20rpx 32rpx;
  background-color: #fff;
  border-bottom: 1px solid #ebeef5;

  .breadcrumb-item {
    color: #909399;
    font-size: 28rpx;
    cursor: pointer;

    &.active {
      color: #409eff;
      font-weight: 500;
    }

    &:not(:last-child)::after {
      content: '>';
      margin: 0 16rpx;
      color: #c0c4cc;
    }
  }
}

.level-container {
  flex: 1;
  height: calc(100vh - 120rpx);
}

.list-cell {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  padding: 24rpx 32rpx;
  overflow: hidden;
  color: #323233;
  font-size: 28rpx;
  line-height: 40rpx;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;

  &:active {
    background-color: #f5f5f5;
  }

  &:last-child {
    border-bottom: none;
  }
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;

  .loading-text {
    margin-top: 20rpx;
    color: #909399;
    font-size: 24rpx;
  }
}
</style>
