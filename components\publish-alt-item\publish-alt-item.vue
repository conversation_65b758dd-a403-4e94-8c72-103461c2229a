<template>
	<view class="btn-item" @click="emits('click')">
		<slot name="icon">
			<image v-if="props.icon" class="btn-item-img" :src="props.icon" mode="aspectFill" />
		</slot>
		<slot>
			<text class="btn-item-lable">{{props.label}}</text>
		</slot>
	</view>
</template>

<script setup lang="ts">
	const props = defineProps({
		label: {
			type: String,
		},
		icon: {
			type: String
		}
	});
	const emits = defineEmits(["click"]);
</script>

<style lang="scss">
	.btn-item {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 16rpx;
		border-radius: 36rpx;
		padding: 16rpx 24rpx;
		font-size: 32rpx;
		color: rgba(72, 218, 234);
		background-color: rgba(247, 248, 250);
		.btn-item-img {
			height: 40rpx;
			width: 40rpx;
		}
	}
</style>