<template>
  <view class="wrapper">
    <view class="feature-item-wrapper">
      <view class="label">服务区域设置说明</view>
      <image class="icon" src="/static/icon/<EMAIL>"></image>
    </view>
    <dust-location-picker :value="{ cid: selectedCity.id, pid: selectedProvince.id }" @confirm="locationConfirm">
      <template #trigger>
        <view class="city-wrapper">
          <text>{{ selectedCity?.name || '北京市' }}</text>
          <image class="icon" src="/static/icon/<EMAIL>"></image>
        </view>
      </template>
    </dust-location-picker>
    <view class="area-wrapper">
      <view class="area-item" :class="{ active: selection.includes(item.id) }" v-for="item in areaList" :key="item.id"
        @click="() => handleSelection(item)">
        <text>{{ item.name }}</text>
        <image v-if="selection.includes(item.id)" class="checked-icon" src="/static/icon/<EMAIL>"></image>
      </view>
    </view>
  </view>
  <view class="footer flex">
    <view class="primary btn" @click="handleConfirm">保存</view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { getSystemDistrict, getSystemCity } from '@/api/common';

const props = defineProps({
  cityId: {
    type: Number,
  },
  provinceId: {
    type: Number,
  },
  areas: {
    type: Array,
    default: () => []
  }
})
const store = useStore()
const location = computed(() => store.state.location)

const emit = defineEmits(['cancel', 'confirm'])
const areaList = ref([])
const selection = ref(props.areas || [])
const selectedCity = ref(location.value?.city || {})
const selectedProvince = ref(location.value?.province || {})

onMounted(() => {
  let cid = location.value?.city?.id || 1
  let pid = location.value?.province?.id || 1
  if (props.cityId) {
    cid = props.cityId
    selectedCity.value = { id: cid }
  }
  if (props.provinceId) {
    pid = props.provinceId
    selectedProvince.value = { id: pid }
  }
  getCity(pid, cid)
  getDistrict(cid)
})

async function getCity(pid, cid) {
  try {
    const res = await getSystemCity(pid)
    if (res.code === 20000) {
      selectedCity.value = res.data.find(item => item.id === cid)
    }
  } catch (error) {
    console.log(error, 'getSystemCity')
  }
}

async function getDistrict(cid) {
  try {
    const res = await getSystemDistrict(cid)
    if (res.code === 20000) {
      areaList.value = res.data
    }
  } catch (error) {
    console.log(error, 'getSystemDistrict')
  }
}

const locationConfirm = (e) => {
  const [province, city] = e
  selectedCity.value = city
  selectedProvince.value = province
  getDistrict(city.id)
}

const handleSelection = (item) => {
  if (selection.value.includes(item.id)) {
    const idx = selection.value.findIndex(id => id === item.id)
    selection.value.splice(idx, 1)
  } else {
    selection.value.push(item.id)
  }
}

const handleConfirm = () => {
  if (selection.value.length === 0) {
    uni.showToast({
      title: '请至少选择一个区域',
      icon: 'none'
    })
    return
  }
  emit('confirm', {
    areaList: selection.value,
    city: selectedCity.value,
    province: selectedProvince.value
  })
}
</script>

<style scoped lang='scss'>
.wrapper {
  width: 100%;
  box-sizing: border-box;
  padding: 0 32rpx;
  margin-top: 32rpx;

  .feature-item-wrapper {
    width: 100%;
    height: 64rpx;
    box-sizing: border-box;
    padding: 16rpx 24rpx;
    background: #F7F8FA;
    border-radius: 12rpx 12rpx 12rpx 12rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .label {
      font-size: 24rpx;
      color: #1F1F1F;
    }

    .icon {
      width: 30rpx;
      height: 30rpx;
    }
  }

  .city-wrapper {
    margin-top: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    width: 164rpx;
    height: 60rpx;
    background: #F7F8FA;
    border-radius: 36rpx 36rpx 36rpx 36rpx;
    font-size: 28rpx;
    color: #1D2129;

    .icon {
      width: 24rpx;
      height: 24rpx;
    }
  }

  .area-wrapper {
    margin-top: 32rpx;
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    height: 394rpx;
    overflow-y: auto;

    .area-item {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 24rpx;
      border-bottom: 2rpx solid #EEEEEE;
      font-size: 32rpx;
      color: #1F1F1F;

      &:last-child {
        border: none;
      }

      &.active {
        color: #FE58B7;
      }

      .checked-icon {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
}

.footer {
  box-sizing: border-box;
  padding: 20rpx 64rpx;
  width: 100%;
  height: 118rpx;
  gap: 16rpx;
  margin-top: 40rpx;
  border-top: 2rpx solid #EEEEEE;

  .btn {
    flex: 1;
    height: 78rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 40rpx;
    font-weight: 700;
    font-size: 32rpx;
    color: #1F1F1F;

    &.primary {
      background: #FE58B7;
      color: #fff;
    }

    &.secondary {
      background-color: #eee;
    }
  }
}
</style>