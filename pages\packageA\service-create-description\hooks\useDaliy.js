import { ref } from 'vue'
import {
  updateDimensionService
} from '@/api/mineService'

export const useDaliy = ({
  detail,
  queryServiceDetail
}) => {
  const popup = ref(null)
  const popupType = ref(null)
  function openPopup(type) {
    popupType.value = type
    popup.value.open()
  }

  function closePopup() {
    popup.value.close()
    popupType.value = null
  }

  function popupConfirm(data) {
    if (popupType.value === 'date-range') {
      dateConfirm(data)
    }
    if (popupType.value === 'area') {
      areaConfirm(data)
    }
    closePopup()
  }

  function dateConfirm({ timeSlot, dateSlot }) {
	  console.log('dateConfirm:', { timeSlot, dateSlot })
    createOrUpdateService({ time_slot: timeSlot, date_slot: dateSlot.map(item => new Date(item).getTime()) })
  }

  function areaConfirm(locales) {
	console.log('********areaConfirm:', JSON.stringify(locales))
	createOrUpdateService({ locales: locales })
  }

  async function createOrUpdateService(data) {
    const params = {
      ...detail.value,
      ...data
    }
    try {
      if (detail.value.id === null) return
      const res = await updateDimensionService(detail.value.id, params)
      if(res.code === 20000){
        queryServiceDetail()
      }
    } catch (error) {
      console.log(error, 'createOrUpdateService')
    }
  }

  return {
    popup,
    popupType,
    openPopup,
    closePopup,
    popupConfirm
  }
}