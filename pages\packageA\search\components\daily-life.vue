<template>
	<uni-list class="dust-uni-list" :border="false">
		<uni-list-item :border="false" class="dust-uni-list-item" v-for="(item, index) in dataList" :key="item.id">
			<template v-slot:body>
				<view class="article-container" @tap="handleTo(item)">
					<view class="art-image-container">
						<dust-image-base :src="item.cover" :local="false" aspect-ratio="16:9"></dust-image-base>
					</view>
					<view class="create-container">
						<view class="creator-container">
							<view class="avatar-bar">
								<image class="avatar" :src="prefixFilePath(item.user_avatar)"></image>
								<image v-if="item.user_gender === 1" class="gender-icon"
									src="/static/my/<EMAIL>" />
								<image v-if="item.user_gender === 2" class="gender-icon"
									src="/static/my/<EMAIL>" />
							</view>
							<view class="creator-cnt">
								<text class="name">{{ item.name }}</text>
								<view class="score-bar">
									<rating-stars :score="item.score" />
									<text class="score-text">{{formattedScore(item.score)}}分</text>
									<text class="tj">推荐</text>
									<text class="reviews-count">{{item.reviews_count}}条评价</text>
								</view>
							</view>
						</view>
						<view class="tag-list" v-if="item.categories.length > 0">
							<template v-for="(itm,idx) in item.categories" :key="idx">
								<text class="tag-item" v-for="(m,i) in itm.types" :key="i">{{m.name}}</text>
							</template>
						</view>
					</view>
					<view class="suit-list">
						<view class="suit-item" v-for="(itm, idx) in item.items" :key="idx">
							<view class="flex items-center">
								<text class="price-text">￥{{itm.price}}</text>
								<text class="name-text">{{itm.name}}</text>
							</view>
							<text class="sale-text">已出售{{itm.sold_quantity}}</text>
						</view>
					</view>
				</view>
			</template>
		</uni-list-item>
	</uni-list>
</template>

<script setup>
	import {
		computed
	} from 'vue'
	import {
		useStore
	} from 'vuex';
	import {
		formatCountToString
	} from '@/common/utils'
	import RatingStars from './rating-stars.vue'

	const store = useStore();

	const props = defineProps({
		dataList: {
			type: Array,
			required: true,
			default: [],
		},
	})

	const cloudFileUrl = computed(() => store.state.common.cloudFileUrl)

	// 格式化分数
	function formattedScore(score) {
		return score.toFixed(1);
	}

	function prefixFilePath(url) {
		if (!url) return ''
		return cloudFileUrl.value + url
	}

	function handleTo(item) {
		uni.navigateTo({
			url: `/pages/packageA/photographyDetails/photographyDetails?id=${item.id}`,
		})
	}
</script>

<style lang="scss">
	.avatar-bar {
		position: relative;

		.avatar {
			width: 56rpx;
			height: 56rpx;
			border-radius: 50%;
		}
	}

	.creator-container {
		display: flex;
		gap: 8rpx;

		.name {
			font-weight: 700;
			font-size: 32rpx;
			color: #3D3D3D;
		}
	}

	.creator-cnt {
		display: flex;
		flex-direction: column;
	}

	.score-bar {
		display: flex;
		align-items: center;
		padding-top: 10rpx;
	}

	.score-text {
		color: #FE58B7;
		font-size: 28rpx;
		margin-left: 8rpx;
	}

	.tj {
		color: #FE58B7;
		font-size: 28rpx;
		margin-left: 4rpx;
	}

	.reviews-count {
		color: #85878D;
		font-size: 28rpx;
		margin-left: 50rpx;
	}

	.gender-icon {
		position: absolute;
		right: 0;
		bottom: 0;
		width: 24rpx;
		height: 24rpx;
	}

	.article-container {
		width: 100%;
		height: fit-content;
		background: #F2F4F7;
		border-radius: 32rpx;
		overflow: hidden;
		padding-bottom: 32rpx;
	}

	.art-image-container {
		width: 100%;
		height: fit-content;
		position: relative;
	}

	.create-container {
		display: flex;
		flex-direction: column;
		margin-top: 22rpx;
		color: #AAAAAA;
		font-size: 24rpx;
		padding: 0 28rpx;
	}

	.suit-list {
		margin-top: 20rpx;
		display: flex;
		flex-direction: column;
		gap: 10rpx;

		.suit-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 24rpx;
		}

		.price-text {
			color: #FE58B7;
			font-weight: 700;
			font-size: 24rpx;
		}

		.name-text {
			font-size: 28rpx;
			color: #3D3D3D;
			margin-left: 24rpx;
		}

		.sale-text {
			font-size: 24rpx;
			color: #FE58B7;
		}
	}

	.tag-list {
		display: flex;
		align-items: center;
		gap: 16rpx;
		margin-top: 20rpx;
		margin-left: 68rpx;

		.tag-item {
			height: 40rpx;
			line-height: 40rpx;
			border-radius: 8rpx;
			border: 2rpx solid #AAAAAA;
			padding: 0 16rpx;
			font-size: 24rpx;
			color: #AAAAAA;
		}
	}
</style>