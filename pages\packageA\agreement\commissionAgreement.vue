<template>
	<view class="app-container">
		<Navbar :type="1" leftText="返佣规则" leftWidth="400rpx"></Navbar>
		<view class="content-scroll">
			<scroll-view class="scroll-view" :scroll-y="true">
				<view class="content">
					平台返佣规则说明
					<view class="split" />
					次元星推出返佣功能，旨在鼓励用户积极参与平台推广及消费活动，通过合理的利益分配机制，实现用户与平台的互利共赢。返佣功能涉及多个环节与规则，以下将对其进行详细说明。本规则适用于所有参与平台返佣活动的用户，用户在使用平台服务过程中应遵循本规则内容，平台保留对规则的最终解释权。
					<view class="split" />
					返佣参与主体
					<view class="split" />
					1.邀请人：已在平台成功注册并具备邀请资格的用户，通过向其他潜在用户分享邀请信息，邀请新用户加入平台，当被邀请用户产生符合返佣条件的消费行为时，邀请人可获得相应的返佣奖励。
					<view class="split" />
					2.被邀请人（仅新注册用户）：通过邀请人分享的邀请信息（邀请码或邀请链接）在平台完成注册，并参与平台消费活动的新用户。其消费行为将作为返佣计算的基础，触发相应的返佣流程。
					<view class="split" />
					邀请方式及绑定规则
					<view class="split" />
					（一）邀请方式
					<view class="split" />
					1.专属邀请码：平台为每位具有邀请资格的用户生成唯一的专属邀请码，邀请人可将此邀请码以各种合法合规的方式（如短信、社交媒体、线下分享等）分享给潜在用户。被邀请人在注册平台账号时，需在注册页面的 “邀请码” 输入框中准确填写该邀请码，提交注册信息并成功注册后，双方将基于此邀请码建立绑定关系。
					<view class="split" />
					2.专属邀请链接：除邀请码外，平台还为用户生成专属邀请链接，该链接中内置了邀请人的唯一标识信息。被邀请人点击此链接后，将直接跳转至平台注册页面，系统会自动提取链接中的邀请码信息并完成绑定关系建立。
					<view class="split" />
					（二）绑定关系建立与层级
					<view class="split" />
					1.当被邀请人成功注册并与邀请人建立绑定关系后，系统将记录并维护这种关联。本平台支持多层级的绑定关系，目前设定的主要层级结构及相关规则如下：一级绑定关系：直接通过邀请码或邀请链接邀请新用户注册成功的用户，与被邀请新用户之间形成一级绑定关系。例如，用户 A 邀请用户 B 注册成功，那么 A 与 B 之间即为一级绑定关系，A 是 B 的一级邀请人。
					<view class="split" />
					2、二级绑定关系：若被邀请的一级用户（如上述的 B）又通过自己的邀请码或邀请链接邀请了新用户（如用户 C）注册成功，此时，最初的邀请人 A 与新用户 C 之间形成二级绑定关系，A 成为 C 的二级邀请人。
					<view class="split" />
					返佣条件与计算规则
					<view class="split" />
					（一）返佣触发条件
					<view class="split" />
					1.被邀请人需在平台完成真实、有效的消费行为，即成功提交订单并支付相应款项，且订单状态为已完成（不存在退款、退货、取消等异常情况），方可触发返佣计算流程。
					<view class="split" />
					2.消费的商品或服务需属于平台规定的可返佣商品或服务范畴，部分特殊商品、限时特价商品或平台明确标注不参与返佣的项目，即便产生消费，也不会计入返佣计算基数。平台将在商品详情页面或相关活动规则中明确标识是否参与返佣，以便用户知晓。
					<view class="split" />
					（二）返佣计算基础
					<view class="split" />
					返佣金额的计算以被邀请人符合返佣条件的消费订单金额为基础，平台会先根据各商品或服务的利润率、营销成本等因素，综合确定每个订单可用于返佣的金额比例（以下简称 “返佣提取比例”），然后按照相应的返佣层级比例进行具体返佣金额的计算。
					<view class="split" />
					（三）具体返佣比例设置
					<view class="split" />
					1.一级邀请人返佣比例：对于与邀请人存在一级绑定关系的被邀请人所产生的符合返佣条件的消费订单，平台将按照该订单可用于返佣的金额的 40%作为返佣奖励支付给一级邀请人。
					<view class="split" />
					2.二级邀请人返佣比例：针对与邀请人存在二级绑定关系的被邀请人的消费订单，平台按照该订单可用于返佣的金额的 10%向一级邀请人支付返佣奖励。
					<view class="split" />
					特殊情况处理规则
					<view class="split" />
					（一）退款订单处理
					<view class="split" />
					1.若已产生返佣的订单发生部分或全部退款情况，平台将根据退款金额占原订单金额的比例，相应扣减已支付给邀请人的返佣金额。
					<view class="split" />
					2.如果邀请人已将涉及退款订单的返佣金额提现并使用，平台有权根据用户协议及相关规则，通过限制账号部分功能、暂停返佣权益、从其他未支付返佣款项或用户在平台的其他权益（如积分、优惠券等）中扣除相应金额等方式，确保返佣金额与实际有效消费订单相符，维护平台返佣机制的公平性和合理性。
					<view class="split" />
					（二）违规行为处理
					<view class="split" />
					1.若发现用户存在以下任何一种违规行为，平台将视情节轻重采取相应的处罚措施，包括但不限于取消返佣资格、追回已发放的违规所得返佣、限制账号使用权限、永久封禁账号等，并保留追究法律责任的权利：通过不正当手段（如恶意刷邀请、虚构交易、利用系统漏洞等）获取返佣奖励；
					<view class="split" />
					a.篡改、伪造邀请信息或交易记录，干扰正常的返佣计算与绑定关系建立；
					<view class="split" />
					b.违反平台其他相关规则、协议，影响平台正常运营秩序及其他用户合法权益的行为。
					<view class="split" />
					2.平台设有专门的风险监控与审核机制，定期或不定期对返佣相关数据及用户行为进行检查和审计，确保返佣活动在合法、合规、公平的环境下开展。一旦发现疑似违规行为，平台将进行深入调查核实，在确认违规事实后，按照上述规定进行处理，并及时通知涉事用户。
					<view class="split" />
					数据查询与透明度保障
					<view class="split" />
					1.平台为每位用户提供便捷的数据查询功能，邀请人可在个人中心的 “我的返佣” 板块查看以下与返佣相关的详细信息：邀请记录：展示所有已成功邀请的用户列表，包含被邀请人昵称、注册时间、绑定时间、最近一次消费时间及金额等基础信息，方便邀请人全面了解自己的邀请推广成果。
					<view class="split" />
					a.返佣明细：呈现每一笔返佣的具体情况，涵盖返佣来源（对应被邀请人的具体消费订单信息）、返佣金额、结算周期、支付状态、扣减情况（如有退款订单导致的扣减）等内容，便于邀请人核对每一笔返佣收入的准确性，确保数据透明、清晰。
					<view class="split" />
					2.平台承诺将严格按照相关法律法规要求，妥善保管所有与返佣功能相关的数据记录，保障数据的安全性、完整性和准确性，防止数据泄露、篡改等情况发生。同时，平台也将积极配合相关监管部门的审计、检查工作，如实提供所需的数据资料。
					<view class="split" />
					规则变更与通知
					<view class="split" />
					1.平台有权根据业务发展需要、市场环境变化、法律法规调整等因素，适时对返佣功能规则进行修订和完善。规则变更可能涉及返佣比例调整、结算周期变动、特殊情况处理方式更新等各个方面。
					<view class="split" />
					2.在进行规则变更前，平台将提前通过平台公告、站内信、推送消息等多种合理且有效的方式，向全体用户发布通知，告知规则变更的具体内容、生效时间以及对用户权益可能产生的影响等重要信息。用户在规则变更生效后继续使用平台服务，即视为接受新的规则内容。
					<view class="split" />
					通过以上详细的返佣功能规则说明，希望广大用户能够清晰了解平台返佣机制的运作方式，积极、合规地参与平台推广与消费活动，共同推动平台的健康发展。如有任何疑问或需要进一步了解相关信息，用户可通过平台客服渠道进行咨询。
				</view>
			</scroll-view>
		</view>
		<uv-safe-bottom></uv-safe-bottom>
	</view>
</template>

<script setup>

</script>

<style lang="scss">
	.app-container {
		background-color: #fff;
	}

	.content-scroll {
		flex: 1;
		overflow: scroll;
	}

	.scroll-view {
		height: 100%;
	}

	.content {
		padding: 32rpx 32rpx 0;
		size: 28rpx;
		line-height: 42rpx;
		.split {
			height: 42rpx;
		}
	}
</style>