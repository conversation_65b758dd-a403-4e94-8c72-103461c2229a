/**
 * @description 自定义路由拦截
 */
import * as auth from '@/common/auth'
import {
	changeParam
} from '@/common/utils'
// 白名单
const whiteList = [
	'/', // 注意入口页必须直接写 '/'
	'/pages/tabbar/home/<USER>',
	'/pages/tabbar/dimension/dimension',
	'/pages/tabbar/mine/mine',
	'/pages/tabbar/publish/publish',
	'/pages/packageA/login-pwd/login-pwd',
	'/pages/packageA/photography/photography',
	'/pages/packageA/photographyDetails/photographyDetails',
	'/pages/packageA/exhibition-details/exhibition-details',
	'/pages/packageA/my-settings/my-settings',
	'/pages/packageA/agreement/agreement',
	'/pages/packageA/agreement/userAgreement',
	'/pages/packageA/agreement/privacyAgreement',
	'/pages/packageA/search/search',
	'/pages/announcement/detail',
	'/pages/packageA/group-booking/group-booking',
	'/pages/packageA/group-booking/group-booking-detail',
]

async function routingIntercept() {
	const list = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']
	// 用遍历的方式分别为,uni.navigateTo,uni.redirectTo,uni.reLaunch,uni.switchTab这4个路由方法添加拦截器
	list.forEach(item => {
		uni.addInterceptor(item, {
			invoke(e) {
				// 获取要跳转的页面路径（url去掉"?"和"?"后的参数）
				const url = e.url.split('?')[0]
				// console.log('url', url)

				// 判断当前窗口是白名单，如果是则不重定向路由
				let pass
				if (whiteList) {
					pass = whiteList.some((item) => {
						// console.log('item:', item)
						if (typeof(item) === 'object' && item.pattern) {
							return item.pattern.test(url)
						}
						return url === item
					})
				}
				console.log('curPage:', url)
				// 不是白`名单并且没有token
				if (!pass && !auth.getToken()) {
					// uni.showToast({
					// 	title: '请重新登录',
					//  icon: 'none',
					// })
					// 打开快捷登录弹窗
					// uni.$emit('changeLogin', true);
					// uni.reLaunch({
					// 	url: '/pages/login/index'
					// })
					// console.log('curPage2:', url)
					// let toUrl = changeParam('/pages/packageA/login-pwd/login-pwd', {
					// 	backUrl: url,
					// })
					uni.navigateTo({
						url: '/pages/packageA/login-pwd/login-pwd',
					})
					return false
				}

				getApp().globalData.curPage = url
				return e
			},
			fail(err) { // 失败回调拦截
				console.log(err)
			}
		})
	})
}

function checkUrl(e) {
	console.log('invoke e:', e)
	// 获取要跳转的页面路径（url去掉"?"和"?"后的参数）
	const url = e.url.split('?')[0]
	// console.log('url', url)

	// 判断当前窗口是白名单，如果是则不重定向路由
	let pass
	if (whiteList) {
		pass = whiteList.some((item) => {
			// console.log('item:', item)
			if (typeof(item) === 'object' && item.pattern) {
				return item.pattern.test(url)
			}
			return url === item
		})
	}
	console.log('curPage:', url, pass)
	// 不是白`名单并且没有token
	if (!pass && !auth.getToken()) {
		// uni.showToast({
		// 	title: '请重新登录',
		//  icon: 'none',
		// })
		// 打开快捷登录弹窗
		uni.$emit('changeLogin', true);
		// uni.reLaunch({
		// 	url: '/pages/login/index'
		// })
		return false
	}

	return true
}

export {
	routingIntercept,
	checkUrl,
}