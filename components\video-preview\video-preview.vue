<template>
	<uv-popup ref="popup" @change="change" :safe-area-inset-bottom="false" custom-style="background-color: transparent;"
		overlayOpacity="0.6">
		<view class="video-view" v-if="show">
			<video class="video" :src="src" :autoplay="autoplay"></video>
		</view>
	</uv-popup>
</template>
<script lang="ts" setup>
import { computed, ref } from "vue";
interface Props {
	src?: string;
	autoplay?: boolean;
}
const props = defineProps<Props>();
defineExpose({
	open,
	close
});

const src = ref("");
const popup = ref(null);
function open(url: string) {
	if (!url) return;
	src.value = url;
	popup.value.open();
}

function close() {
	popup.value.close();
}

const show = ref(true);
function change(e) {
	show.value = e.show;
}
</script>
<style scoped lang="scss">
.video-view {
	width: 750rpx;

	.video {
		width: 750rpx;
	}
}
</style>