import { requestApi } from './request';

/** 获取系统消息 */
export const getSystemMessage = () => {
  return requestApi({
    url: '/api/v1/foreground/message/system/message',
    method: 'GET'
  })
}

/** 获取订单消息 */
export const getOrderMessage = (params) => {
  return requestApi({
    url: `/api/v1/foreground/message/consult`,
    method: 'GET',
    data: params,
  })
}

/** 获取他人主页用户信息 */
export const getOtherUserInfo = (user_id:number) => {
  return requestApi({
    url: `/api/v1/foreground/message/other/${user_id}`,
    method: 'GET',
    isToken: false
  })
}

/** 修改他人备注信息 */
export const putUserRemark = (user_id: number,remark:string) => {
	return requestApi({
		url: `/api/v1/foreground/message/other/${user_id}/remark`,
		method: 'POST',
		data: {
			remark
		}
	});
};

/** 他人设置不让ta看 */
export const putUserNoLookMy = (user_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/message/other/${user_id}/my`,
		method: 'POST'
	});
};

/** 他人设置不看ta */
export const putUserNoLookTheir = (user_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/message/other/${user_id}/their`,
		method: 'POST'
	});
};

/** 提交好友申请 */
export const addFriend = (friend_id: number, message: string) => {
	return requestApi({
		url: `/api/v1/foreground/message/apply`,
		method: 'POST',
		data: {
			friend_id: Number(friend_id),
			message
		}
	});
};

/** 删除好友 */
export const delFriend = (socialize_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/message/socialize/${socialize_id}`,
		method: 'DELETE'
	});
};

/** 举报用户 */
export const reportUser = (user_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/message/socialize/report`,
		method: 'POST',
		data: {
			"user_id": Number(user_id)
		}
	});
};

/** 提交亲友申请 */
export const addBest = (friend_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/message/best`,
		method: 'POST',
		data: {
		    friend_id: Number(friend_id)
		}
	});
};

/** 移除亲友 */
export const delBest = (best_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/message/best/${best_id}`,
		method: 'DELETE'
	});
};

/** 获取好友列表 */
export const getFriendList= () => {
  return requestApi({
    url: `/api/v1/foreground/message/socialize`,
    method: 'GET',
  })
}

/** 分页 Params */
interface GetUserListParams {
	page : number;
	size : number;
	skip : number;
	limit : number;
	keyword : string;
}
// 查询用户
export const getUserList = (params : GetUserListParams) => {
	return requestApi({
		url: `/api/v1/foreground/message/socialize/user`,
		method: 'GET',
		data: params,
	});
}

export const getOtherUserServices = (user_id: number, params: { categories: string }) => {
	return requestApi({
		url: `/api/v1/foreground/message/other/${user_id}/service`,
		method: 'GET',
		data: params,
	})
}