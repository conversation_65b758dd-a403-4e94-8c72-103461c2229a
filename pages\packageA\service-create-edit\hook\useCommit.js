
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import {
  getMineServiceDetailItems,
  createMineServiceDetailItems,
  deleteMineServiceDetailItems,
  updateMineServiceDetailItems
} from '@/api/mineService'
import { uploadFile } from '@/api/request'

export function useCommit({ form, serviceId, itemId }) {
  const store = useStore()
  const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

  const originForm = ref({})

  const getItemDetail = async () => {
    try {
      let res = await getMineServiceDetailItems(serviceId.value)
      if (res.code === 20000) {
        return res.data.find(item => item.id === +itemId.value)
      }
      return null
    } catch (error) {
      console.log(error)
      return null
    }
  }

  onMounted(async () => {
    if (itemId.value) {
      const itemDetail = await getItemDetail()
      if (!!itemDetail) {
        form.value.name = itemDetail.name
        form.value.cover = itemDetail.cover
        form.value.previewCover = cloudFileUrl.value + itemDetail.cover
        form.value.price = itemDetail.price / 100
        form.value.bond = itemDetail.bond
        form.value.is_on_sale = itemDetail.is_on_sale

        originForm.value.name = itemDetail.name
        originForm.value.cover = itemDetail.cover
        originForm.value.previewCover = cloudFileUrl.value + itemDetail.cover
        originForm.value.price = itemDetail.price / 100
        originForm.value.bond = itemDetail.bond
        originForm.value.is_on_sale = itemDetail.is_on_sale
      }
    }
  })

  const getUploadFileUrl = async (file) => {
    try {
      const res = await uploadFile(file)
      if (res.code === 20000) {
        return res.data.url
      } else {
        return ''
      }
    } catch (error) {
      return ''
    }
  }

  const checkForm = () => {
    if (!form.value.cover) {
      uni.showToast({
        title: '请上传封面',
        icon: 'none'
      })
      return false
    }

    if (!form.value.name) {
      uni.showToast({
        title: '请输入标题',
        icon: 'none'
      })
      return false
    } else {
      if (form.value.name.length > 20) {
        uni.showToast({
          title: '请输入20字以内的标题',
          icon: 'none'
        })
        return false
      }
    }

    if (!form.value.price) {
      uni.showToast({
        title: '请输入价格',
        icon: 'none'
      })
      return false
    }
    return true
  }

  const handleCreate = async () => {
    if (!checkForm()) {
      return
    }

    uni.showModal({
      content: '确认新增套餐?',
      confirmColor: '#FE58B7',
      success: async ({ confirm }) => {
        try {
          if (confirm) {
			// const coverUrl = await getUploadFileUrl(form.value.cover)
			// if (!coverUrl) {
			// 	return 
			// }
			const coverUrlRes = await uploadFile(form.value.cover)
			if (coverUrlRes.code !== 20000) {
			  return 
			}
            const params = {
              name: form.value.name,
              cover: coverUrlRes.data.url,
              price: +form.value.price * 100,
              bond: +form.value.bond,
              is_on_sale: form.value.is_on_sale
            }

            const res = await createMineServiceDetailItems(serviceId.value, params)
            if (res.code === 20000) {
              uni.$emit('service-create-detail-daliy:reload-data', 2)
              uni.$emit('service-expo-combo-list:reload-data')
              uni.$emit('service-combo-list:reload-data')
              uni.navigateBack({
                delta: 1
              })
            }
          }
        } catch (error) {
          console.log(error, 'createDimensionServiceItem')
        }
      }
    })



  }

  const handleDelete = async () => {
    try {
      if (!itemId.value) return

      const res = await deleteMineServiceDetailItems(serviceId.value, itemId.value)
      if (res.code === 20000) {
        uni.$emit('service-create-detail-daliy:reload-data', 2)
        uni.$emit('service-expo-combo-list:reload-data')
        uni.$emit('service-combo-list:reload-data')
        uni.navigateBack({
          delta: 1
        })
      }
    } catch (error) {
      console.log(error, 'deleteDimensionServiceItem')
    }
  }

  const handleUpdate = async () => {
    try {
      if (!itemId.value) return

      if (isEqual(form.value, originForm.value)) return

      if (!checkForm()) {
        return
      }

      const params = {
        name: form.value.name,
        cover: form.value.cover,
        price: +form.value.price * 100,
        bond: +form.value.bond,
        is_on_sale: form.value.is_on_sale
      }

      if (form.value.cover !== originForm.value.cover) {
        // const coverUrl = await getUploadFileUrl(form.value.cover)
        // params.cover = coverUrl
		const coverUrlRes = await uploadFile(form.value.cover)
		if (coverUrlRes.code !== 20000) {
		  return 
		}
		params.cover = coverUrlRes.data.url
      }

      const res = await updateMineServiceDetailItems(serviceId.value, itemId.value, params)
      if (res.code === 20000) {
        uni.$emit('service-create-detail-daliy:reload-data', 2)
        uni.$emit('service-expo-combo-list:reload-data')
        uni.$emit('service-combo-list:reload-data')
        uni.navigateBack({
          delta: 1
        })
      }
    } catch (error) {
      console.log(error, 'updateDimensionServiceItem')
    }
  }

  function isEqual(obj1, obj2) {
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) {
      return false;
    }

    for (const key of keys1) {
      if (obj1[key] !== obj2[key]) {
        return false;
      }
    }

    return true;
  }

  return {
    handleCreate,
    handleDelete,
    handleUpdate
  }
}
