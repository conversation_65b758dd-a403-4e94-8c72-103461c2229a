<template>
	<view class="product-wrapper">
		<view class="header">
			<view class="left-wrapper">
				<image class="icon" src="/static/icon/service/icon_dd.png"></image>
				<text class="content">订单编号：{{ order.number }}</text>
			</view>
			<view class="right-wrapper">
				<text class="status-text" :class="{ cancel: order.status === 16 || order.status === 32, refund: order.status === 64 || order.status === 128 }">
					{{ statusMap[order.status] || '' }}
				</text>
			</view>
		</view>
		<view class="main">
			<view class="imageView">
				<image class="item-cover" :src="prefixFilePath(order.snapshot.cover)" mode="aspectFit"></image>
			</view>
			<view class="item-content">
				<view class="item-title">
					<text class="txtellipsis">
						{{ order.snapshot.name }}
					</text>
				</view>
				<view class="item-desc">下单时间：{{ formatTimeFn(order.created_at) }}</view>
				<view class="item-desc">
					实付：
					<text class="price-wrapper">
						￥
						<text class="price">{{ order.amount / 100 }}</text>
					</text>
				</view>
			</view>
		</view>

		<view class="footer">
			<view class="btn-group" v-if="order.status === 2">
				<view class="cancel-btn" @click.native.stop="handleSecondaryClick">取消订单</view>
			</view>
			<view class="btn-group" v-if="order.status == 4">
				<view class="cancel-btn" @click.native.stop="handleSecondaryClick">取消订单</view>
				<view class="primary-btn" @click.native.stop="handlePrimaryClick">去完成</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { useStore } from 'vuex';
import { computed } from 'vue';
/**
 * 订单状态 - 接口对应
 */
const statusMap = {
	1: '待支付',
	2: '待完成',
	4: '已使用',
	8: '已完成',
	16: '用户取消',
	32: '商家弃用',
	64: '用户违约',
	128: '商家违约',
	256: '待支付',
	512: '订单超时'
};

const props = defineProps({
	order: {
		type: Object,
		required: true
	},
	arrid: {
		type: Number,
		required: true
	}
});

const store = useStore();
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

const prefixFilePath = (path) => {
	if (!path) return '';
	return cloudFileUrl.value + path;
};

const formatTimeFn = (time) => {
	function padZero(n) {
		return (n < 10 ? '0' : '') + n;
	}

	if (!time) return '';

	const date = new Date(time);
	const year = date.getFullYear();
	const month = padZero(date.getMonth() + 1);
	const day = padZero(date.getDate());
	return `${year}.${month}.${day}`;
};

const emit = defineEmits(['primaryClick', 'secondaryClick']);

const handlePrimaryClick = () => {
	emit('primaryClick', props.order, props.arrid);
};

const handleSecondaryClick = () => {
	emit('secondaryClick', props.order, props.arrid);
};
</script>

<style scoped lang="scss">
.product-wrapper {
	margin-bottom: 24rpx;
	width: 100%;
	height: fit-content;
	background: #f7f8fa;
	border-radius: 24rpx;
	// padding: 30rpx 24rpx 30rpx 24rpx;

	.header {
		padding-top: 24rpx;
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.left-wrapper {
			display: flex;
			align-items: center;
			margin-left: 24rpx;
			.icon {
				width: 40rpx;
				height: 40rpx;
			}

			.content {
				font-weight: 400;
				font-size: 28rpx;
				color: #85878d;
				line-height: 28rpx;
			}
		}

		.right-wrapper {
			font-weight: 400;
			font-size: 28rpx;
			color: #48daea;
			line-height: 28rpx;
			margin-right: 24rpx;
			.cancel {
				color: #85878d;
			}

			.refund {
				color: #fe1e42;
			}
		}
	}

	.main {
		width: 100%;
		display: flex;
		margin-top: 28rpx;
		padding-bottom: 24rpx;
		.imageView {
			width: 208rpx;
			height: 208rpx;
			border-radius: 20rpx;
			overflow: hidden;
			background-color: #1f1f1f;
			margin-left: 24rpx;
			margin-right: 32rpx;
			.item-cover {
				width: 208rpx;
				height: 208rpx;
			}
		}

		.item-content {
			width: 414rpx;
			display: flex;
			flex-direction: column;
			gap: 24rpx;

			.item-title {
				font-weight: 500;
				font-size: 32rpx;
				color: #3d3d3d;
				line-height: 44rpx;
			}

			.item-desc {
				font-weight: 400;
				font-size: 24rpx;
				color: #767676;
				line-height: 36rpx;

				.price-wrapper {
					font-weight: 700;
					font-size: 24rpx;
					color: #fe58b7;
					line-height: 34rpx;

					.price {
						font-size: 36rpx;
					}
				}
			}
		}
	}

	.footer {
		width: 100%;
		.btn-group {
			padding-top: 20rpx;
			padding-bottom: 20rpx;
			width: 100%;
			display: flex;
			justify-content: flex-end;
			align-items: center;
			border-top: 2rpx solid #eeeeee;

			.cancel-btn {
				width: 144rpx;
				height: 56rpx;
				border-radius: 48rpx 48rpx 48rpx 48rpx;
				border: 2rpx solid #d8d8d8;
				text-align: center;
				line-height: 56rpx;
				font-weight: 500;
				font-size: 24rpx;
				color: #1f1f1f;
				margin-right: 24rpx;
			}

			.primary-btn {
				width: 144rpx;
				height: 56rpx;
				background: #fe58b7;
				border-radius: 48rpx 48rpx 48rpx 48rpx;
				color: #fff;
				text-align: center;
				line-height: 56rpx;
				font-weight: 500;
				font-size: 24rpx;
				margin-right: 24rpx;
			}
		}
	}
}
</style>
