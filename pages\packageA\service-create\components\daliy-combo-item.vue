<template>
  <view class="combo-item-wrapper">
    <view class="cover-wrapper">
      <dust-image-base  wrapperRadius="20rpx" :src="item.cover" :local="false" aspect-ratio="1:1"></dust-image-base>
    </view>
    <view class="info-container">
      <view class="title">{{ item.name }}</view>
      <view class="status" :class="{ active: item.is_on_sale }">{{ item.is_on_sale ? '已上架' : '已下架' }}</view>
      <view class="description">商家保证金{{ item.bond }}%</view>
      <view class="description">
        <text>参考价：</text>
        <view class="price-wrapper">
          <view class="prefix">￥</view>
          <view class="price">{{ item.price / 100 }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'

const store = useStore()
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

function prefixFilePath(path) {
  if (!path) return ''
  return cloudFileUrl.value + path
}

const props = defineProps(['item'])
</script>

<style scoped lang='scss'>
.combo-item-wrapper {
  width: 100%;
  // height: 274rpx;
  box-sizing: border-box;
  margin-bottom: 20rpx;
  padding-bottom: 32rpx;
  border-bottom: 2rpx solid #EEEEEE;
  display: flex;
  gap: 32rpx;

  .cover-wrapper {
    width: 208rpx;
    height: 208rpx;
  }

  .info-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12rpx;

    .title {
      font-weight: 700;
      font-size: 32rpx;
      color: #3D3D3D;
      line-height: 44rpx;
      word-break: break-all;
    }

    .status {
      width: 96rpx;
      height: 40rpx;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      border: 2rpx solid #D8D8D8;
      font-size: 24rpx;
      color: #D8D8D8;
      text-align: center;
      line-height: 40rpx;

      &.active {
        border: 2rpx solid #48DAEA;
        color: #48DAEA;
      }
    }

    .description {
      font-size: 28rpx;
      color: #85878D;
      display: flex;
      align-items: center;

      .price-wrapper {
        display: flex;
        align-items: center;
        font-weight: 700;
        color: #FE58B7;

        .prefix {
          font-size: 24rpx;
          transform: translateY(4rpx);
        }

        .price {
          font-size: 36rpx;
        }
      }
    }
  }
}
</style>