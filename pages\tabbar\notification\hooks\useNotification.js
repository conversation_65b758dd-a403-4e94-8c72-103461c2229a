import { ref, computed } from 'vue'
import { getSystemMessage, getOrderMessage } from '@/api/message'
import { getFriendConversationList } from '@/api/notification'
import { timeToString } from '@/common/utils'
import { useStore } from 'vuex'
import { onShow } from '@dcloudio/uni-app'

export const SYSTEM_MESSAGE_CACHE_KEY = 'SYSTEM_MESSAGE_CACHE_KEY'

export const useNotification = () => {
  const vendorOrderMessagetList = ref([])
  const userOrderMessagetList = ref([])
  const systemMessageList = ref([])
  const systemNewMsgCount = ref(0)

  const store = useStore()
  
  const friendConversationList = computed(() => {
    console.log(store.state.friendNotification.friendConversationList, 'friendConversationList')
    return store.state.friendNotification.friendConversationList
  })
  // 移除之前的监听避免重复
  uni.$off('notification:refresh-newMsgCount')
  uni.$off('notification:new-conversation')

  // 处理订单/咨询消息未读数量
  uni.$on('notification:refresh-newMsgCount', (id, count) => {
    userOrderMessagetList.value.forEach(item => {
      if (item.id === id) {
        item.new_message_count += count
      }
    })
    vendorOrderMessagetList.value.forEach(item => {
      if (item.id === id) {
        item.new_message_count += count
      }
    })
  })

  // 监听新conversation创建
  uni.$on('notification:new-conversation', (conversationId) => {
    store.dispatch('message/loginSend', 'user-chat-' + conversationId);
  })

  onShow(() => {
    Promise.all([
      queryOrderMessageList('vendor'),
      queryOrderMessageList('user'),
      querySystemMessageList(),
      queryFriendConversationList()
    ]).then(([res1, res2, res3, res4]) => {
      vendorOrderMessagetList.value = res1
      userOrderMessagetList.value = res2
      systemMessageList.value = res3
      // 更新store中的好友会话列表
      store.commit('friendNotification/updateFriendConversationList', res4)
      
      // 为所有好友会话登录WebSocket频道
      res4.forEach(conversation => {
        store.dispatch('message/loginSend', 'user-chat-' + conversation.id)
      })
    })
  })

  const getNewMessageCount = (list) => {
    return list.reduce((prev, item) => {
      return prev + item.new_message_count
    }, 0)
  }

  const getLastMessageTime = (list) => {
    const newList = (list || []).filter(item => item.last_message).sort((a, b) => {
      return b.last_message.created_at - a.last_message.created_at
    })
    if (newList.length > 0) {
      return timeToString(newList[0].last_message.created_at, 5)
    } else {
      return ''
    }
  }

  const messageList = computed(() => {
    const sysTime = systemMessageList.value?.[0]?.created_at

    return [
      {
        name: '系统信息',
        time: !sysTime ? '' : timeToString(sysTime, 5),
        body: systemMessageList.value?.[0]?.title || '',
        image: "../../../static/icon/<EMAIL>",
        type: 0,
        count: systemNewMsgCount.value
      },
      {
        name: '订单消息',
        time: getLastMessageTime(userOrderMessagetList.value),
        body: getNewMessageCount(userOrderMessagetList.value) > 0 ? "有新的订单消息" : "暂无新消息",
        image: "../../../static/icon/<EMAIL>",
        type: 1,
        count: getNewMessageCount(userOrderMessagetList.value)
      },
      {
        name: '咨询消息',
        time: getLastMessageTime(vendorOrderMessagetList.value),
        body: getNewMessageCount(vendorOrderMessagetList.value) > 0 ? "有新的咨询消息" : "暂无新消息",
        image: "../../../static/icon/<EMAIL>",
        type: 2,
        count: getNewMessageCount(vendorOrderMessagetList.value)
      },
    ]
  })


  async function querySystemMessageList() {
    try {
      const p = {
        page: 1,
        size: 10
      }
      const res = await getSystemMessage(p)
      if (res.code === 20000) {
        const systemMessageCache = uni.getStorageSync(SYSTEM_MESSAGE_CACHE_KEY)
        const count = res.data.results?.length - systemMessageCache?.length
        systemNewMsgCount.value = isNaN(count) ? 0 : count < 0 ? 0 : count
        uni.setStorageSync(SYSTEM_MESSAGE_CACHE_KEY, res.data.results)
        return res.data.results
      }
      return []
    } catch (error) {
      return []
    }
  }

  async function queryOrderMessageList(type) {
    try {
      const p = {
        role: type
      }

      const res = await getOrderMessage(p)
      if (res.code === 20000) {
        return res.data
      }
      return []
    } catch (error) {
      console.log(error, 'getOrderMessage');
      return []
    }
  }

  async function queryFriendConversationList() {
    try {
      const res = await getFriendConversationList()
      if (res.code === 20000) {
        return res.data
      }
      return []
    } catch (error) {
      console.log(error, 'getFriendConversationList');
      return []
    }
  }


  return {
    messageList,
    friendConversationList,
    systemNewMsgCount
  }
}