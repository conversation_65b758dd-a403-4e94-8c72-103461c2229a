<template>
  <view v-if="activeTab === 0" class="expo-detail-wrapper">
    <image class="expo-detail-image" v-for="(image, index) in detail?.detail" :key="`expo-detail-${index}`"
      :src="prefixFilePath(image)" :show-menu-by-longpress="true" :show-loading="true" mode="widthFix" @tap="ckimg(prefixFilePath(image))"/>
  </view>
  <view v-if="activeTab === 1" class="comment-container">
    <sample-list />
  </view>
  <view v-if="activeTab === 2" class="joiner-container" @tap="handleTo('joiner')">
    <view class="viewer-avatar-container" v-if="detail?.joinins?.length">
      <image v-for="(item, index) in detail?.joinins?.slice(0, 4)" :key="index" :src="prefixFilePath(detail?.joinins?.[index]?.avatar)"
        class="viewer-avatar" @tap.stop="handleToUserMine(item)"></image>
    </view>
    <view class="action-container">
      <text>{{ (detail?.joinin_count || 0) + ' 人参加' }}</text>
      <image class="icon" src="/static/icon/<EMAIL>"></image>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
import SampleList from '../exhibition-comment/sample-list.vue'

const store = useStore()
const expoDetailState = computed(() => store.state.expoDetail)
const detail = computed(() => expoDetailState.value.expoDetail)
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

function prefixFilePath(url) {
  if (!url) return '';
  return cloudFileUrl.value + url;
}

const props = defineProps({
  activeTab: {
    type: Number,
    required: true,
    default: 0
  }
})

function handleTo(id) {
  let url = ''
  switch (id) {
    case 'joiner':
      url = '/pages/packageA/exhibition-joiner/exhibition-joiner'
      break
  }

  url && uni.navigateTo({
    url
  })
}
function handleToUserMine(item) {
	uni.navigateTo({
		url: '/pages/home-pages/user-mine?id=' + item.id,
	})
}
function ckimg(img) {
	console.log('ckimg:', img)
	uni.previewImage({
		urls: [img],
		longPressActions: {
			itemList: ['发送给朋友', '保存图片', '收藏'],
			success: function(data) {},
			fail: function(err) {}
		}
	});
}
</script>

<style scoped lang='scss'>
.expo-detail-image {
  width: 100%;
  aspect-ratio: 1;
}

.joiner-container {
  margin: 0 24rpx;
  width: calc(100% - 48rpx);
  box-sizing: border-box;
  padding: 0 8rpx 0 24rpx;
  margin-bottom: 300rpx;
  height: 98rpx;
  background: #F7F8FA;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .viewer-avatar-container {
    display: flex;

    .viewer-avatar {
      width: 54rpx;
      height: 54rpx;
      border-radius: 50%;
      margin-left: -20rpx;
      border: 2rpx solid #fff;

      &:first-child {
        margin-left: 0;
      }
    }
  }

  .action-container {
    display: flex;
    align-items: center;

    .icon {
      width: 40rpx;
      height: 40rpx;
    }
  }
}
</style>