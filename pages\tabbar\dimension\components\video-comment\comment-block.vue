<template>
  <view class="wrapper">
    <image :src="item.avatar" class="avatar"></image>

    <view class="content">
      <view class="name">{{ item.name }}</view>
      <view class="comment">{{ item.comment }}</view>
      <view class="footer">
        <view class="time">{{ item.time }}</view>
        <view class="acton">回复</view>
        <image src="/static/icon/dimension/more-horizontal.png" @click="handleMoreAction"></image>
      </view>
    </view>
  </view>
</template>

<script setup>
const props = defineProps(['item'])

const emit = defineEmits(['onMore'])
const handleMoreAction = () => {
  emit('onMore')
}
</script>

<style scoped lang='scss'>
.wrapper {
  width: 100%;
  display: flex;
  gap: 24rpx;

  .avatar {
    width: 84rpx;
    height: 84rpx;
    border-radius: 50%;
    border: 2rpx solid #fff;
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16rpx;

    .name {
      font-weight: 400;
      font-size: 28rpx;
      color: #85878D;
      line-height: 28rpx;
    }

    .comment {
      font-weight: 400;
      font-size: 32rpx;
      color: #1F1F1F;
      line-height: 48rpx;
    }

    .footer {
      display: flex;
      align-items: center;
      gap: 40rpx;

      .time {
        font-weight: 400;
        font-size: 24rpx;
        color: #85878D;
        line-height: 24rpx;
      }

      .action {
        font-weight: 400;
        font-size: 28rpx;
        color: #1F1F1F;
        line-height: 28rpx;
      }

      image {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
}
</style>