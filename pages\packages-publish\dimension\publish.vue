<!-- 活动发布主界面 -->
<template>
	<view class="container">
		<view style="transform: translate(0);">
			<Navbar :type="1"  leftText="发次元"></Navbar>
		</view>
		
		<view class="main">
			<view class="form-wrapper">
				<view class="upload-container">
					<image-upload :fileList="imageUrls" @delete="deleteImage" @uploadSucess="uploadImage" multiple maxCount="9"/>
				</view>
				
				<view class="title-container">
					<rich-textarea ref="richText" placeholder="添加作品描述" v-model="content" @blureCursor="onBlureCursor" @deleteSpeciaText="onDeleteSpeciaText" style="height: 100%; width: 100%;" />
				</view>
		
				<view class="nav-wrapper">
					<publish-alt-item label="话题" icon="/pages/packages-publish/static/<EMAIL>" @click="topics.show = true;"/>
					<publish-alt-item label="用户" icon="/pages/packages-publish/static/<EMAIL>" @click="alts.show = true;"/>
				</view>
				
				<publish-navigate-row  label="活动地址" :show-clear="false" :model-value="addressInfo && addressInfo.title" @click="selectAddressHandle" />
			</view>
			
			
			<!-- 选择话题 -->
			<publish-pop-select v-if="topics.show" 
				ref="topicsRef"
				:title= "topics.title"
				:localdata="topics.options"
				@close="topics.show = false"
				@confirm="onTopicConfirm"
				:multiple="topics.multiple"
				:queryList="onTopicsQueryList"
				showSearch
				enableLoad
			>	
				<template #header>
					<view style="width: 100%;">
						<publish-search-input></publish-search-input>
					</view>
				</template>
			</publish-pop-select>
			
			<!-- 选择@用户 -->
			<publish-pop-select v-if="alts.show" 
				ref="altsRef"
				:title= "alts.title"
				:localdata="alts.options"
				@close="alts.show = false"
				@confirm="onAltConfirm"
				:multiple="alts.multiple"
				:queryList="onAltsQueryList"
				showSearch
				enableLoad
			>
				<template #header>
					<view style="width: 100%;">
						<publish-search-input></publish-search-input>
					</view>
				</template>
			</publish-pop-select>

			<view class="footer-contener">
				<view class="btn-container save" @tap.stop="saveHandle">
					<image src="/pages/packages-publish/static/<EMAIL>" mode="aspectFit"></image>
					<text>存草稿</text>
				</view>
		
				<view class="btn-container publish" @tap.stop="commitHandle">
					<image src="/pages/packages-publish/static/<EMAIL>" mode="aspectFit"></image>
					<text>发布活动</text>
				</view>
			</view>
			<view class="message">
				<text @tap="publishNoticHandle">点击查看发布规则</text>
			</view>
		</view>
		<publish-notice :cutdown="cutdown" v-if="showPublishNotice" @close="onClosePublishNotice" @confirm="onClosePublishNotice" />
	</view>
	
</template>
<script setup lang="ts">
	import { ref, toRaw, onUnmounted, watchEffect, computed, Ref } from 'vue';
	import { WrappedBeginCode, WrappedEndCode, getSpeciaMatchCount } from "@/components/rich-textarea/index.ts";
	import RichTextArea from "@/components/rich-textarea/rich-textarea.vue";
	import { useStore } from 'vuex';
	
	import { PUBLISH_ACTIVITY_ADDRESS_SELECT } from "../activity/const";
	import type { AddressItem } from "../activity/const"

	import {
		getAltList,
		getTopicList,
		createPublishDimension
	} from "@/api/publish";
	
	interface LocalSaveInfo {
		imageUrls?: {url: string}[], // 图片url列表
		content?: string; 	// 输入内容
		alts?: string[];		// @数组
		topics?: string[];    // #话题
		address?: AddressItem;	// 活动地址
		selectOptions?: SelectInfo["options"]; // 本地保存的选择数组
	}
	
	const localSave = getLocalSaveInfo();

	// 上传的图片数组
	const imageUrls = ref<{url: string}[]>(localSave.imageUrls || []);
	// 图片上传
	const uploadImage = (files: any[]) => {
		files.forEach(item => {
			imageUrls.value.push({url: item.url});
		})
	};
	// 图片删除
	const deleteImage = (item: {index: number}) => {
		imageUrls.value.splice(item.index, 1)
	}
	
	// 内容
	const richText = ref<InstanceType<typeof RichTextArea>>(null);
	const content = ref<string>(localSave.content || "");
	interface SelectInfo {
		show: boolean;  // 是否显示下拉框
		ref: Ref;		// 
		model: string[];	  // 已选中的value
		options: {text: string, value: string }[]; // 下拉选项
		multiple: boolean; // 是否可以多选
		title: string; // 选择框弹出的标题文本
		get displayModel(): string;
	}
	
	// 话题
	const topicsRef = ref(null);
	const topics = ref<SelectInfo>({
		show: false,
		ref: ref(null),
		model: localSave.topics || [],
		options: [],
		multiple: false,
		title: "选择话题",
		get displayModel() {
			const model = this.model;
			const item = this.options.find(item => item.value == model);
			if (!item) return "";
			return item.text;
		}
	});

	// @人员
	const altsRef = ref(null);
	const alts = ref<SelectInfo>({
		show: false,
		ref: ref(null),
		model: localSave.alts || [],
		options: [],
		multiple: false,
		title: "选择@用户",
		get displayModel() {
			const model = this.model || [];
			if (!model.length) return "";
			return model.join(" ~ ");
		}
	});
	// 地址选择
	const addressInfo = ref<AddressItem | undefined>(localSave.address);
	const selectAddressHandle = function() {
		uni.$off(PUBLISH_ACTIVITY_ADDRESS_SELECT);
		uni.$on(PUBLISH_ACTIVITY_ADDRESS_SELECT, (item: AddressItem) => {
			console.log("接收到的选择地址", item);
			addressInfo.value = item;
		});
		let loactionStr = "";
		const location = addressInfo.value;
		if (location && location.latitude && location.longitude) {
			loactionStr = `longitude=${location.longitude}&latitude=${location.latitude}`;
		}
		uni.navigateTo({
			url: "/pages/packages-publish/activity/chooseAddress?" + loactionStr
		});
	};
	// 输入框失活时光标位置 默认为-1 需要在输入改变后重置为-1，防止未输入时计算异常
	let cursorIndex = -1;
	let selectAllOptions: SelectInfo["options"] = localSave.selectOptions || [];
	const onBlureCursor = (num: number) => {
		cursorIndex = num;
	}
	const onTopicConfirm = function(select: {text: string, value: string}) {
		topics.value.show = false;
		if (!select) return;
		
		const model = content.value;
		const cursor = cursorIndex == -1 ? model.length : cursorIndex;
		cursorIndex = -1;
		
		const inserText = `#${select.text}`;
		// content.value = model.slice(0, cursor) + inserText + model.slice(cursor);
		topics.value.model.push(select.value);
		selectAllOptions.push({value: select.value, text: inserText});
		// 内部会同步content的值
		richText.value.insertStyleText(`#${select.text}`);
	};
	
	const onAltConfirm = function(select: {text: string, value: string}) {
		alts.value.show = false;
		if (!select) return;
		
		const model = content.value;
		const cursor = cursorIndex == -1 ? model.length : cursorIndex;
		cursorIndex = -1;
		
		const inserText = `@${select.text}`;
		// content.value = model.slice(0, cursor) + inserText + model.slice(cursor);
		alts.value.model.push(select.value);
		selectAllOptions.push({value: select.value, text: inserText});
		console.log("11111", selectAllOptions)
		// 内部会同步content的值
		richText.value.insertStyleText(`@${select.text}`);
	};
	
	const onDeleteSpeciaText = (text: string, deleteIndex: number) => {
		const isAltStr = text.includes(`@`);
		// 不能使用filter, 因为删除的内容可能在数组有多个，使用filter后数组个数不正确
		let deleteSelectIndex = selectAllOptions.findIndex(item => text.includes(item.text));
		let deleteOption = selectAllOptions[deleteSelectIndex];
		deleteOption && selectAllOptions.splice(deleteSelectIndex, 1);

		if (isAltStr) {
			const index = alts.value.model.findIndex(varlue => varlue === deleteOption.value);
			alts.value.model.splice(index, 1);
		} else {
			const index = topics.value.model.findIndex(varlue => varlue === deleteOption.value);
			topics.value.model.splice(index, 1);
		}
	};
	
	const onTopicsQueryList = (page: number, pageSize: number) => {
		uni.showLoading({
			title: "获取话题中..."
		});
		getTopicList().then(res => {
			const isSucessed = res.code === 20000;
			const userList = res.data.results || [];
			// const canLoadMore = !(userList.length < res.data.count);
			topicsRef.value.complete(false, !isSucessed);
			if (!isSucessed) return uni.showToast({
				icon: "error",
				title: "获取话题失败"
			});
			
			if (!userList.length) return uni.showToast({
				icon: "none",
				title: "暂无话题信息"
			});
			setTimeout(() => uni.hideLoading(), 150);
			topics.value.options = userList.map(item => ({text: item.name, value: item.id}));
		});
		
	}
	const onAltsQueryList = (page: number, pageSize: number) => {
		uni.showLoading({
			title: "获取粉丝中..."
		});
		getAltList().then(res => {
			const isSucessed = res.code === 20000;
	
			const userList = res.data.results || [];
			// const canLoadMore = !(userList.length < res.data.count);
			altsRef.value.complete(false, !isSucessed);
			if (!isSucessed) return uni.showToast({
				icon: "error",
				title: "获取人员失败"
			});
			
			if (!userList.length) return uni.showToast({
				icon: "none",
				title: "暂无关注用户"
			});
			setTimeout(() => uni.hideLoading(), 150);
			alts.value.options = userList.map(item => ({text: item.nickname, value: item.id}));
		});
	}
	
	// 提交检验输入项
	const preCommiteCheck = function() {
		interface ChackItem {
			name: string;
			validate: () => string | undefined;
		}
		console.log("上传的图片：", imageUrls.value.length)
		const checkItems: ChackItem[] = [
			{name: "封面图片", validate: () => {
				if (imageUrls.value.length) return;
				return "请上传封面图片"
			}},
			{name: "次元详情图片", validate: () => {
				if (imageUrls.value.length > 1) return;
				return "请再上传一张详情图片"
			}},
			{name: "作品描述", validate: () => {
				if (content.value) return;
				return "请输入作品描述"
			}},
			{name: "活动地址", validate: () => {
				if (addressInfo.value && addressInfo.value.title) return;
				return "请选择活动地址"
			}},
		];
		 
		for (let item of checkItems) {
			const errMsg = item.validate();
			if (errMsg) {
				uni.showToast({
					title: errMsg,
					icon: "none"
				});
				return false
			}
		}
		return true;
	}
	
	type SendParams = Parameters<typeof createPublishDimension>[0];
	type Resource = SendParams["resources"];
	const createSentParams = (): SendParams => {
		const resources: Resource = [];
		imageUrls.value.forEach((item, index) => {
			if (!index) return;
			const videoTyps = ["mp4", "avi", "kov"];
			const formatType = item.url.split(".").pop();
			const isVideo = videoTyps.includes(formatType);
			resources.push({
				url: item.url, 
				sequence: index, 
				type: isVideo ? 2 : 3,
				mime: isVideo ? `media/${formatType}` : `image/${formatType}`
			});
		});
		
		return {
			"name": "",
			"description": content.value,
			"cover": imageUrls.value[0].url,
			"publish_at": Date.now(),
			"subjects": topics.value.model,  // 主题
			"mentioned": alts.value.model, // @用户
			"resources": resources,
			
			"address": addressInfo.value.address,
			"province_id": addressInfo.value.province || 0, //省份 ID，可为 null
			"city_id": addressInfo.value.city || 0, //城市 ID，可为 null
			"district_id": addressInfo.value.district || 0, //区县 ID，可为 null
			"longitude": addressInfo.value.longitude,
			"latitude": addressInfo.value.latitude,
		}
	};
	const commitHandle = () => {
		const validate = preCommiteCheck();
		if (!validate) return;
		// if (showFirstShowNotice()) return;
		uni.showLoading({
			title: "次元发布中..."
		});
		
		createPublishDimension(createSentParams()).then(res => {
			const isSucessed = res.code === 20000;
			uni.hideLoading();
			if (!isSucessed) return uni.showToast({
				icon: "error",
				title: "次元发布失败"
			});
			// 保存成功后清空上次的本地缓存
			removeLocalSaveInfo();
			uni.navigateBack();
		}).finally(() => uni.hideLoading());
		
	}
	
	// 保存草稿相关
	const LOCAL_SAVE_KEY = "dimension-save-key";
	const createLocalSaveInfo = function(): LocalSaveInfo {
		return {
			imageUrls: imageUrls.value,
			content: content.value,
			topics: topics.value.model,
			alts: alts.value.model,
			address: addressInfo.value,
			selectOptions: selectAllOptions,
		}
	}
	
	function saveHandle() {
		uni.setStorage({
			key: LOCAL_SAVE_KEY,
			data: createLocalSaveInfo(),
			success: () => uni.showToast({
				title: "保存成功"
			})
		})
	}
	
	function getLocalSaveInfo(): LocalSaveInfo {
		const localInfo = uni.getStorageSync(LOCAL_SAVE_KEY) || {};
		console.log("本地数据：", localInfo)
		return localInfo;
	}
	
	function removeLocalSaveInfo() {
		uni.removeStorage({
			key: LOCAL_SAVE_KEY
		})
	}
	
	// 2025/05/16新增：发布时需要查看发布规则
	const localNoticeKey = "publish-dimension-notice-show";
	let alreadyShowNotice = uni.getStorageSync(localNoticeKey) || false;
	const showPublishNotice = ref(alreadyShowNotice ? false : true);
	const cutdown = ref(alreadyShowNotice ? 0 : 5);
	function showFirstShowNotice() {
		if (alreadyShowNotice) return false;
		alreadyShowNotice = true;
		showPublishNotice.value = true;
		return true;
	}
	const publishNoticHandle = () => {
		if (showPublishNotice.value) return;
		showPublishNotice.value = true;
		if (alreadyShowNotice) return
		alreadyShowNotice = true;
		cutdown.value = 0;
		uni.setStorage({
			key: localNoticeKey,
			data: true
		});
	}
	
	const onClosePublishNotice = () => {
		showPublishNotice.value = false;
		if (alreadyShowNotice) return
		alreadyShowNotice = true;
		cutdown.value = 0;
		uni.setStorage({
			key: localNoticeKey,
			data: true
		});
	}
	
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		height: 100%;
		box-sizing: border-box;
		
		padding-bottom: constant(safe-area-inset-bottom);;
		padding-bottom: env(safe-area-inset-bottom);
	}
	
	:deep(.uv-upload__wrap .uv-upload__wrap__preview .uv-upload__wrap__preview__image) {
		width: 208rpx !important;
		height: 208rpx !important;
		border-radius: 16rpx !important;
	}
	
	:deep(.uni-date-btn--ok .uni-datetime-picker--btn) {
		background-color: rgba(254, 88, 183) !important;
	}
	
	:deep(.uni-calendar-item--multiple .uni-calendar-item--before-checked) {
		background-color: rgba(254, 88, 183) !important;
	}
	
	:deep(.uni-calendar-item--multiple .uni-calendar-item--after-checked) {
		background-color: rgba(254, 88, 183) !important;
	}
	
	:deep(.uni-calendar-item__weeks-box .uni-calendar-item--checked) {
		background-color: rgba(254, 88, 183) !important;
	}
	.label-title {
		color: black;
		font-weight: bold;
	}
	
	.main {
		flex: 1 1 auto;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		box-sizing: border-box;
		padding: 32rpx 32rpx 24rpx;
		// overflow: hidden;
		.upload {
			width: 208rpx;
			height: 208px; 
			border-radius: 16rpx;
		}
		.form-wrapper {
			flex: 1 1 auto;
			overflow-y: auto;
		}
		
		.upload-container {
			padding: 0 0 20rpx;
			box-sizing: border-box;
		}
		
		.title-container {
			display: flex;
			flex: 0 0 auto;
			background-color: red;
			width: 100%;
			height: 292rpx;
			flex-direction: column;
			gap: 20rpx;
			background-color: rgba(247, 248, 250);
			border-radius: 16rpx;
			padding: 24rpx;
			
			box-sizing: border-box;
			color: rgba(31, 31, 31);
			.title {
				width: 100%;
				background-color: transparent;
				flex: 0 0 auto;
				height: 56rpx;
				font-size: 40rpx;
				line-height: 56rpx;
				font-weight: 700;
			}
			.title-placeholder {
				color: rgba(133, 135, 141);
			}
			.desc {
				width: 100%;
				flex: 1 1 auto;
				background-color: transparent;
				font-size: 36rpx;
				line-height: 56rpx;
				font-weight: 400;
			}
			.desc-placeholder {
				color: rgba(133, 135, 141);
			}
			
		}
		
		.nav-wrapper {
			display: flex;
			gap: 24rpx;
			padding: 32rpx 0;
		}
		
		.form-container {
			flex: 1 1 auto;
			overflow-y: auto;
		}
		
		.footer-contener {
			display: flex;
			height: 118rpx;
			flex: 0 0 auto;
			gap: 32rpx;
			align-items: center;
			padding: 0rpx 8rpx;
			box-sizing: border-box;
			
			.btn-container {
				display: flex;
				justify-content: center;
				align-items: center;
				gap: 16rpx;
				
				height: 78rpx;
				padding: 0 44rpx;
				flex: 1 1 auto;
				background-color: rgba(238, 238, 238);
				border-radius: 40rpx;
		
				font-size: 32rpx;
				font-weight: 700;
				image {
				   width: 40rpx;
				   height: 40rpx;
				}
			}
			
			.save {
				flex: 0 0 auto;
			}
			.publish {
				flex: 1 1 auto;
			}
			
		}
		.message {
			font-size: 14px;
			color: rgba(255, 206, 89, 1);
			flex: none;
			text-align: center;
		}
	}

</style>
