<template>
	<view class="container">
		<z-paging ref="paging" :fixed="true" 
			v-model="dataList" @query="queryList"
			:emptyViewSuperStyle="{'position': 'absolute', 'width': '100%', 'height': '100%'}"
		>
			<template #top>
				<Navbar :type="1" leftText="黑名单"></Navbar>
				<view class="header">
					<uni-search-bar placeholder="请输入搜索关键字" v-model="keyword" @cancel="cancelSearch" @confirm="searchConfirm">
						<template #searchIcon>
							<image class="icon-search" src="/static/icon/<EMAIL>" />
						</template>
					</uni-search-bar>
				</view>
			</template>
			
		<!-- 	<template #empty>
				<view class="empty-list">
					<image class="icon" src="/static/<EMAIL>"></image>
					<text>暂无黑名单</text>
				</view>
			</template> -->
	
			<view class="main-container">
				<view class="row-item" v-for="item, index in dataList" :key="item.user_id">
					<image class="item-cover" :src="item.target.avatar" mode="aspectFill" @tap="handleToUserMine(item.target)"></image>
					<view class="item-right">
						<view class="item-info">
							<view class="item-info-name" @tap="handleToUserMine(item.target)">{{item.target.nickname}}</view>
							<view class="item-info-text">{{item.reason}}</view>
						</view>
						<view class="item-btn" @click="removeItem(item, index)">移出</view>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup lang="ts">
import { onMounted, computed, ref } from 'vue';
import { getBlackList, deleteBlackItem } from '@/api/user';
import { onPageScroll, onReachBottom } from '@dcloudio/uni-app';
import { useStore } from 'vuex';


const store = useStore();
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

type RequestParams = Parameters<typeof getBlackList>[0];
type Response = ReturnType<typeof getBlackList> extends Promise<infer T> ? T : never;
type List = Response["data"]["results"];

const keyword = ref<string>(undefined);
const paging = ref(null);
const dataList = ref<List>([]);

function queryList(page: number, size: number) {
	const params: RequestParams = Object.assign({page,size}, keyword.value && {
		keyword: keyword.value
	});
	getBlackList(params).then((res) => {
		const isSuccess = res.code === 20000;
		if (isSuccess) {
			const result = res.data.results.map(item => {
				const avatar = item.target.avatar;
				if (avatar) {
					item.target.avatar = cloudFileUrl.value + avatar;
				} else {
					item.target.avatar = "/static/Default_Avatar_boy3.png";
				}
				return item;
			});
			// 测试删除数据
			// let test = result[0];
			// test.id = "-1111";
			// result.push(test)
			paging.value.complete(result);
		} else {
			paging.value.complete(false).catch(console.log);
			return uni.showToast({
				title: '搜索失败',
				icon: "error"
			})
		}
	});
}

const cancelSearch = () => {
	// 如果是删除按钮时keyword.value等于"",非空子串
	if (keyword.value === undefined) return;
	keyword.value = undefined;
	paging.value?.reload();
}
const searchConfirm = () => {
	paging.value?.reload();
};
const removeItem = (item: List[0], index: number) => {
	uni.showModal({
		content: `确认将${item.target.nickname}移出黑名单？`,
		confirmText: '确认',
		showCancel: true,
		success: res => {
			if (res.cancel) return;
			deleteBlackItem(item.target.id).then(res => {
				const isSuccess = res.code === 20000;
				if (!isSuccess) return uni.showToast({
					title: '删除失败',
					icon: "error"
				})
				dataList.value.splice(index, 1);
				uni.showToast({
					title: '删除成功',
					icon: "success"
				})
			});
		}
	})
}
function handleToUserMine(item: { id: string; }) {
	uni.navigateTo({
		url: '/pages/home-pages/user-mine?id=' + item.id,
	})
}
</script>

<style scoped lang="scss">
	.container {
		height: 100%;
		width: 100%;
		.header {
			padding: 32rpx;
			:deep(.uni-searchbar) {
				padding: 0px;
			}
		}
		.main-container {
			padding: 0 32rpx;
		}
		.empty-list {
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			color: #85878D;
		
			.icon {
				width: 380rpx;
				height: 380rpx;
			}
		}
		.row-item {
			display: flex;
			justify-content: space-between;
			gap: 24rpx;
			.item-cover {
				flex: none;
				height: 96rpx;
				width: 96rpx;
				border-radius: 50%;
				align-self: center;
				border: 2rpx solid rgba(238, 238, 238, 1);
			}
			.item-right {
				display: flex;
				flex: auto;
				justify-content: space-between;
				align-items: center;
				gap: 24rpx;
				border-bottom: 2rpx solid rgba(238, 238, 238, 1);
			}
			.item-info {
				display: flex;
				align-self: stretch;
				flex-direction: column;
				justify-content: start;
				align-items: stretch;
				flex: auto;
				gap: 16rpx;
				padding: 32rpx 0;
				.item-info-name {
					font-size: 32rpx;
					color: #1F1F1F;
				}
				
				.item-info-text {
					font-size: 28rpx;
					color: #85878D;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 3;
					-webkit-box-orient: vertical;
					max-height: 60px;
				}
			}
			
			.item-btn {
				width: 144rpx;
				height: 72rpx;
				flex: none;
				background: #FE58B7;
				border-radius: 48rpx;
				font-size: 32rpx;
				color: #FFFFFF;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
.empty-text {
	text-align: center;
	font-size: 28rpx;
	padding: 30px;
	color: #999;
}
.search-box {
	position: sticky;
	top: 0;
	left: 0;
	z-index: 1;
	background: #fff;
}
.icon-search {
	width: 32rpx;
	height: 32rpx;
}
.black-list {
	padding: 0 32rpx;
}
.black-item {
	padding: 32rpx 0;
	margin-left: 120rpx;
	position: relative;
	border-bottom: 2rpx solid #EEEEEE;
	display: flex;
	align-items: center;
	.v-cover {
		position: absolute;
		left: -120rpx;
		width: 96rpx;
		height: 96rpx;
		border-radius: 100%;
	}
	.v-info {
		flex: 1;
	}
	.v-name {
		font-size: 32rpx;
		color: #1F1F1F;
		line-height: 32rpx;
	}
	.v-text {
		font-size: 28rpx;
		color: #85878D;
		line-height: 28rpx;
		margin-top: 16rpx;
	}
}
.black-btn {
	width: 144rpx;
	height: 72rpx;
	background: #FE58B7;
	border-radius: 48rpx;
	font-size: 32rpx;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
}
</style>
