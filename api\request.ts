import Config from '@/common/config';
import * as auth from '@/common/auth';
import { changeParam, replacenull, getCompletedImageUrl } from '@/common/utils';
import { increaseRequestCount, decreaseRequestCount, getRequestCount } from './requestCount';

/**
 * 扩展 RequestOptions
 */
export interface RequestProps extends UniNamespace.RequestOptions {
	/** 请求携带的入参，GET 自动拼接到 url 上，所以 GET 的时候不要把入参传入 params */
	data ?: any;
	/** 是否显示加载动画 */
	loading ?: boolean;
	/** 是否需要携带 token */
	isToken ?: boolean;
	/** 是否显示错误提示 */
	showError ?: boolean;
}
/**
 * 服务器数据返回
 */
export interface OnlineSvcResult<T> {
	/** 返回的状态码 */
	code : number;
	/** 状态信息 */
	msg : string;
	/** 返回的数据 */
	data ?: T;
	/** 数据行 */
	rows ?: T;
	/** 新的 token */
	token ?: string;
	/** 数据总数 */
	tatal ?: number;
}

/**
 * 存储取消请求任务的变量
 * @type {UniApp.RequestTask | null}
 */
let cancelCurrentRequest : UniApp.RequestTask | null = null;

/**
 * 通用请求方法
 * @template ResponseData
 * @param {RequestProps} options 请求参数
 * @param {string} [options.method=POST] 默认 POST
 * @param {*} options.data 请求携带的入参，GET 自动拼接到 url 上，所以 GET 的时候不要把入参传入 params
 * @param {boolean} options.loading 是否显示加载动画
 * @param {boolean} options.isToken 是否需要携带 token
 * @param {boolean} options.showError 是否显示错误提示
 * @returns {Promise<OnlineSvcResult<ResponseData>>} 返回一个 Promise 对象，resolve 时返回以下结构：
 * @property {number} code 返回的状态码，20000 表示成功
 * @property {string} msg 消息描述，例如 "success"
 * @property {*} data 响应数据
 */
export function requestApi<ResponseData>({
	url,
	data,
	method = 'POST',
	header = {},
	timeout = 30000,
	loading = true,
	isToken = true,
	showError = true,
	...other // 其他配置参数 参考 UniNamespace.RequestOptions
} : RequestProps) : Promise<OnlineSvcResult<ResponseData>> {
	// 记录请求开始时间
	const startTime = +new Date();
	const token = auth.getToken();
	// 在这里使用 uni.request 发送请求，然后将响应数据解析为 ResponseData 类型
	if (loading) {
		increaseRequestCount();
		uni.showLoading();
	}
	console.log('requestApi token:', token);
	const options = {
		url: `${Config.hostUrl}${url}`,
		method: method,
		header: {
			// 'Content-Type': 'application/x-www-form-urlencoded',
			...header,
			Authorization: token ? token : '',
			"X-Lang": 'zh',
			"X-Version": Config.version,
		},
		timeout,
		...other
	} as UniNamespace.RequestOptions;

	if (method === 'GET' && data) {
		options.url = changeParam(options.url, data);
	} else {
		options.data = data;
	}

	console.log('request options:', options, 'getRequestCount:', getRequestCount());
	return new Promise((resolve) => {
		if (isToken) {
			// 如果接口要 token，则判断 token 是否存在，不存在则拦截
			if (!token) {
				// hideLoading(loading);
				if (loading) {
					decreaseRequestCount();
					// 所有请求完成时隐藏
					if (getRequestCount() === 0) {
						uni.hideLoading();
					}
				}
				resolve({
					code: 0,
					msg: '用户未登录'
				});
				auth.setToken('');
				uni.showToast({
					title: '请重新登录',
					icon: 'none',
					complete() { }
				});
				return;
			}
		}
		cancelCurrentRequest = uni.request({
			...options,
			success: (res) => {
				// 计算请求耗时
				const costTime = +new Date() - startTime;
				// hideLoading(loading);
				if (loading) {
					decreaseRequestCount();
					// 所有请求完成时隐藏
					if (getRequestCount() === 0) {
						uni.hideLoading();
					}
				}
				console.log('request res:', options.url, res);
				const data = res.data as OnlineSvcResult<ResponseData>;
				// 上报监控事件（官方要求的格式）
				reportApiEvent({
					apiId: url,
					level: 0, // 默认普通接口
					errorCode: res.statusCode || 0,
					errorMsg: res.errMsg || 'ok',
					costTime,
					method,
				});
				// 判断服务器返回的 code 如果是 500 就显示错误信息
				checkResult(data, showError);
				resolve(data);
			},
			fail: (e) => {
				console.log('接口异常:', e);
				// 计算请求耗时
				const costTime = +new Date() - startTime;
				// hideLoading(loading);
				if (loading) {
					decreaseRequestCount();
					// 所有请求完成时隐藏
					if (getRequestCount() === 0) {
						uni.hideLoading();
					}
				}
				// 上报监控事件
				reportApiEvent({
					apiId: url,
					level: 1, // 重要接口
					errorCode: -1,
					errorMsg: e.errMsg || '网络异常',
					costTime,
					method,
				});
				showError && uni.showToast({
					title: '服务器或网络异常，请稍后再试',
					icon: 'none'
				});
				resolve({
					code: -1,
					msg: '服务器或网络异常，请稍后再试'
				});
			},
			// complete: () => {
			// 	hideLoading(loading); 
			// }
		});
	});
}

/**
 * 检查返回结果
 * @template ResponseData
 * @param {OnlineSvcResult<ResponseData>} res - 响应结果
 * @param {boolean} showError - 是否显示错误提示
 */
function checkResult<ResponseData>(res : OnlineSvcResult<ResponseData>, showError : boolean) {
	if (res.code === 20000) {
		return
	}
	let message = res.msg || '系统繁忙，请稍后重试';
	// 执行处理逻辑
	switch (res.code) {
		case 0:
		case 1:
			// 未登录
			uni.showModal({
				title: '提示',
				content: message,
				confirmText: '去登录',
				cancelText: '取消',
				success(e) {
					auth.setToken('');
					uni.$emit('resetLogin');
					if (e.confirm) {
						uni.navigateTo({
							url: '/pages/packageA/login-pwd/login-pwd'
						})
					} else {
					}
				}
			})
			break;
		case 40016:
			// 未绑定手机号
			uni.showModal({
				title: '提示',
				content: message,
				confirmText: '去绑定',
				cancelText: '取消',
				success(e) {
					if (e.confirm) {
						uni.navigateTo({
							url: '/pages/packageA/change-phone/change-phone?isEdit=0'
						})
					}
				}
			})
			break
		case 40017:
		case 40018:
		case 40019:
			// 未实名认证或者实名认证过期
			uni.showModal({
				title: '提示',
				content: message,
				confirmText: '去认证',
				cancelText: '取消',
				success(e) {
					if (e.confirm) {
						uni.navigateTo({
							url: '/pages/packageA/verify-idcard/verify-idcard'
						})
					}
				}
			})
			break
		default:
			showError &&
				setTimeout(() => {
					uni.showToast({
						title: message,
						icon: 'none',
						duration: 3000,
					});
				}, 100)
			break;
	}
}

/**
 * 隐藏加载动画
 * @param {boolean} loading - 是否显示加载动画
 */
function hideLoading(loading : boolean) {
	if (loading) {
		decreaseRequestCount();
		// 所有请求完成时隐藏
		if (getRequestCount() === 0) {
			uni.hideLoading();
		}
	}
}

/**
 * 取消当前请求
 */
export function cancelRequest() {
	console.log('cancelRequest:', cancelCurrentRequest);
	if (cancelCurrentRequest) {
		cancelCurrentRequest.abort();
		cancelCurrentRequest = null;
	}
}

/** 微信API监控上报 */
export function reportApiEvent(params: {
    apiId: string;
    level?: number;
    errorCode: number;
    errorMsg: string;
    costTime: number;
	method: string,
}) {
    // 微信环境下才上报
    if (typeof wx !== 'undefined' && wx.reportEvent) {
        try {
            wx.reportEvent('wxdata_perf_monitor', {
                'wxdata_perf_monitor_id': params.apiId,   // 接口ID
                'wxdata_perf_monitor_level': params.level || 0,  // 接口等级（默认为普通）
                'wxdata_perf_error_code': params.errorCode,     // 错误码
                'wxdata_perf_error_msg': params.errorMsg.length > 100 
                    ? params.errorMsg.substring(0, 100) 
                    : params.errorMsg,  // 错误信息（限制100字符）
                'wxdata_perf_cost_time': params.costTime,      // 请求耗时(ms)
                // 以下为补充字段（可选）
                'wxdata_perf_extra_info1': `${params.method}`,
            });
        } catch (e) {
            console.error('微信监控上报失败:', e);
        }
    }
}

/**
 * 文件上传接口 (图片或视频)
 * @template ResponseData
 * @param {string} fileUri - 文件路径
 * @returns {Promise<OnlineSvcResult<ResponseData>>} - 返回服务器响应的数据
 */
export function uploadFile<ResponseData>(fileUri : string) : Promise<OnlineSvcResult<ResponseData>> {
	// 记录请求开始时间
	const startTime = +new Date();
	increaseRequestCount();
	uni.showLoading({
		title: '上传中...',
		mask: true
	});
	const options = {
		url: Config.hostUrl + '/api/v1/file/upload',
		method: 'POST',
		header: {
			'Content-Type': 'multipart/form-data',
			Authorization: replacenull(auth.getToken())
		},
		timeout: 30000,
		filePath: fileUri,
		name: 'file'
	} as UniNamespace.RequestOptions;
	console.log('uploadFile options:', options);
	return new Promise((resolve) => {
		uni.uploadFile({
			...options,
			success: (res) => {
				// 计算请求耗时
				const costTime = +new Date() - startTime;
				console.log('request res:', res);
				reportApiEvent({
					apiId: '/api/v1/file/upload',
					level: 0, // 默认普通接口
					errorCode: res.statusCode || 0,
					errorMsg: res.errMsg || 'ok',
					costTime,
					method: 'POST',
				});
				
				if (res.statusCode === 200) {
					let data : OnlineSvcResult<ResponseData> = JSON.parse(res.data);
					checkResult(data, true);
					handleResponImageUrl(data);
					resolve(data);
				}
			},
			fail: (e) => {
				// 计算请求耗时
				const costTime = +new Date() - startTime;
				console.log('接口异常:', e);
				reportApiEvent({
					apiId: '/api/v1/file/upload',
					level: 1, // 重要接口
					errorCode: -1,
					errorMsg: e.errMsg || '网络异常',
					costTime,
					method: 'POST',
				});
				uni.showToast({
					title: '服务器或网络异常，请稍后再试',
					icon: 'none'
				});
				resolve({
					code: -1,
					msg: '服务器或网络异常，请稍后再试'
				});
			},
			complete: () => {
				// hideLoading(true);
				decreaseRequestCount();
				// 所有请求完成时隐藏
				if (getRequestCount() === 0) {
					uni.hideLoading();
				}
			}
		});
	});
}

// 图片上传后返回的图片地址为路径，需要手动拼接故增加一个完整地址字段completeUrl
function handleResponImageUrl(res : OnlineSvcResult<any>) {
	if (res.code !== 20000) return;
	const data = res.data;
	if (!data) return;
	const imagePath = data.url;
	if (!imagePath) return;
	data.completeUrl = getCompletedImageUrl(imagePath);
}