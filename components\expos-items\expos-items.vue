<template>
	<uni-list class="dust-uni-list" :border="false">
		<template v-for="(item, index) in dataList" :key="item.id">
			<uni-list-item class="dust-uni-list-item" :border="false">
				<template v-slot:body>
					<view class="anime-container" @tap="() => handleTo(item)">
						<article-item :article="item" :type="0">
							<view class="anime-item-wrap">
								<view class="flex title-wrap">
									<text class="title">{{ item.name }}</text>
									<view class="flex collect-wrap" @tap.stop="() => handleCollect(item)">
										<image
											:src="item.is_collected ? '/static/icon/star_selected.png' : '/static/icon/star_noselected.png'">
										</image>
										<text>收藏</text>
									</view>
								</view>
								<view class="time-range-container">
									<image class="icon" src="/static/img/exhibition-details/icon_sj.png"></image>
									<text>{{ formatDateRange(item.start_datetime, item.end_datetime) }}</text>
								</view>
								<view class="people-container" v-if="item.friends_count">
									<view class="viewer-avatar-container">
										<image v-for="(fri, index) in item.friends" :key="index"
											:src="prefixFilePath(fri.avatar)" class="viewer-avatar"></image>
									</view>
									<text>{{ item.friends_count + ' 好友参加' }}</text>
								</view>
							</view>
						</article-item>
					</view>
				</template>
			</uni-list-item>
			<!-- #ifdef MP-WEIXIN -->
			<uni-list-item class="dust-uni-list-item" :border="false"
				v-if="type === 'home' && adPositions.includes(index)">
				<template v-slot:body>
					<view class="ad-item-wrapper">
						<ad-custom unit-id="adunit-683ade455c0dedcb"></ad-custom>
					</view>
				</template>
			</uni-list-item>
			<!-- #endif -->
		</template>
	</uni-list>
</template>

<script setup>
	import {
		ref,
		computed,
		watch,
		onMounted
	} from 'vue';
	import {
		useStore
	} from 'vuex';
	import {
		calculateHeight,
		toLog,
		formatDateRange,
	} from '@/common/utils';
	import {
		collectExhibition
	} from '@/api/exhibitionExpo';

	const props = defineProps({
		dataList: {
			type: Array,
			required: true,
		},
		type: {
			type: String,
			default: ''
		},
	})

	// const emit = defineEmits(['onClick', 'onCollect']);

	const store = useStore();
	const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

	// 存储所有广告的全局索引位置
	const adPositions = ref([])

	// 存储每页的广告位置计算状态
	const pageCalculated = ref({})

	// 计算每页的起始索引
	function getPageStartIndex(pageNum) {
		return (pageNum - 1) * 10
	}

	// 计算每页的数据量
	function getPageSize(pageNum) {
		const start = getPageStartIndex(pageNum)
		const end = start + 10
		return Math.min(end, props.dataList.length) - start
	}

	// 计算广告位置
	function calculateAdPosition(pageNum) {
		// 如果已经为该页计算过位置，不再重新计算
		if (pageCalculated.value[pageNum]) return

		const pageSize = getPageSize(pageNum)

		// 当前页数据少于5条时不显示广告
		if (pageSize < 5) {
			// 标记该页已计算，但无广告
			pageCalculated.value[pageNum] = true
			return
		}

		// 计算广告位置
		let min, max

		// 第一页：5-10之间随机位置
		if (pageNum === 1) {
			min = 4 // 第5条对应索引4
			max = Math.min(9, pageSize - 1) // 第10条对应索引9
		}
		// 其他页：5-8之间随机位置
		else {
			min = 4 // 第5条对应索引4
			max = Math.min(7, pageSize - 1) // 第8条对应索引7
		}

		// 生成随机位置（当前页内的相对位置）
		const relativePosition = Math.floor(Math.random() * (max - min + 1)) + min

		// 转换为全局索引
		const globalPosition = getPageStartIndex(pageNum) + relativePosition

		// 添加到广告位置数组
		adPositions.value.push(globalPosition)

		// 标记该页已计算
		pageCalculated.value[pageNum] = true
	}

	// 计算所有已加载页面的广告位置
	function calculateAllAdPositions() {
		const totalPages = Math.ceil(props.dataList.length / 10)

		for (let page = 1; page <= totalPages; page++) {
			// 如果该页尚未计算，则计算广告位置
			if (!pageCalculated.value[page]) {
				calculateAdPosition(page)
			}
		}
	}

	// 监听数据列表变化，重新计算广告位置
	watch(() => props.dataList, (newDataList, oldDataList) => {
		// 当数据列表长度变化时，重新计算所有广告位置
		if (oldDataList === undefined || newDataList.length !== oldDataList.length) {
			calculateAllAdPositions()
		}
	}, {
		deep: true,
		immediate: true
	})

	function handleTo(item) {
		console.log('onClick', item)
		uni.navigateTo({
			url: `/pages/packageA/exhibition-details/exhibition-details?id=${item.id}`
		});
	}

	async function handleCollect(item) {
		// emit('onCollect', item)
		console.log('dataList.value:', props.dataList)
		toLog();
		try {
			const res = await collectExhibition(item.id);
			if (res.code === 20000) {
				props.dataList.forEach((itm) => {
					if (itm.id === item.id) {
						itm.is_collected = !itm.is_collected;
					}
				});
			}
		} catch (error) {
			console.log(error, '  handleCollect');
		}
	}

	function prefixFilePath(url) {
		if (!url) return '';
		return cloudFileUrl.value + url;
	}
</script>

<style lang="scss">
	.anime-container {
		width: 100%;

		.anime-item-wrap {
			padding: 0 24rpx;

			.title-wrap {
				width: 100%;
				align-items: flex-start;
				justify-content: space-between;
				margin-top: 20rpx;

				.title {
					font-size: 36rpx;
					font-weight: 700;
					color: #3d3d3d;
				}

				.collect-wrap {
					padding-top: 2rpx;
					gap: 8rpx;
					align-items: center;
					color: #1f1f1f;
					font-size: 28rpx;
					flex-shrink: 0;

					image {
						width: 40rpx;
						height: 40rpx;
					}
				}
			}

			.time-range-container {
				margin-top: 16rpx;
				display: flex;
				align-items: center;
				gap: 8rpx;
				font-weight: 400;
				font-size: 28rpx;
				line-height: 40rpx;

				.icon {
					width: 28rpx;
					height: 28rpx;
				}
			}

			.people-container {
				margin-top: 24rpx;
				display: flex;
				align-items: center;
				gap: 12rpx;
				font-size: 28rpx;
				line-height: 40rpx;

				.viewer-avatar-container {
					display: flex;

					.viewer-avatar {
						width: 54rpx;
						height: 54rpx;
						border-radius: 50%;
						margin-left: -20rpx;

						&:first-child {
							margin-left: 0;
						}
					}
				}
			}
		}
	}

	.ad-item-wrapper {
		width: 100%;
		border-radius: 40rpx;
		overflow: hidden;
	}
</style>