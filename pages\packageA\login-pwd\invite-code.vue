<template>
  <view class="wrapper">
    <view class="header">
      <image class="back-icon" src="../static/login/<EMAIL>" @tap.stop="handleBack"></image>
      <view class="title">输入邀请码</view>
    </view>
    <input type="text" v-model="code" class="code-input" placeholder="请输入邀请码">
    <!-- <uv-code-input v-model="code" :focus="true" size="52" space="20" :maxlength="4"></uv-code-input> -->
    <view class="btn-submit btn-primary" @tap.stop="handleBindInviteCode">确定</view>
    <view class="btn-submit btn-back" @tap.stop="handleBack">跳过</view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
	useStore
} from 'vuex';
import {
	onLoad,
	onBackPress
} from '@dcloudio/uni-app';
import { bindInviteCode } from '@/api/user'
import * as auth from '@/common/auth';

const emit = defineEmits(['back'])
const store = useStore();
const commonInfo = computed(() => store.state.common);

const code = ref('')

onMounted(() => {
	console.log('commonInfo.value.inviteCode:', commonInfo.value.inviteCode)
	if (commonInfo.value.inviteCode) {
		code.value = commonInfo.value.inviteCode
	}
})

function handleBack() {
  // 清掉邀请码
  store.dispatch('common/setInviteCode', '');
  auth.updateInviteCode('')
  emit('back')
}

async function handleBindInviteCode() {
  try {
    const res = await bindInviteCode(code.value)
    if (res.code === 20000) {
      uni.showToast({
        title: '邀请码绑定成功',
        icon: 'none'
      })
	  // 清掉邀请码
	  store.dispatch('common/setInviteCode', '');
	  auth.updateInviteCode('')
	  uni.redirectTo({
	  	url: '/pages/packageA/login-pwd/invite-user-info?inviteCode=' + code.value,
	  })
    }
  } catch (error) {
    console.log(error, 'bindInviteCode')
    uni.showToast({
      title: '邀请码绑定失败，请稍后再试',
      icon: 'none'
    })
  }
}

</script>

<script>
export default {
  options: { styleIsolation: "shared" }
}
</script>

<style scoped lang='scss'>
.wrapper {
  height: 732rpx;
  background: rgba(255, 254, 249, 0.6);
  box-shadow: 0rpx 16rpx 40rpx 0rpx rgba(208, 168, 122, 0.16);
  border-radius: 32rpx 32rpx 32rpx 32rpx;
  border: 2rpx solid #FFFFFF;
  margin: 30rpx 32rpx 0;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  padding: 40rpx;
  padding-bottom: 56rpx;

  .header {
    width: 100%;
    display: flex;
    align-items: center;

    .back-icon {
      width: 64rpx;
      height: 64rpx;
    }

    .title {
      flex: 1;
      text-align: center;
      font-weight: 500;
      font-size: 52rpx;
      color: #1F1F1F;
      line-height: 76rpx;
    }
  }

  .btn-primary {
    margin-top: 112rpx;
    text-align: center;
  }

  .btn-back {
    margin-top: 56rpx;
    background-color: #1F1F1F !important;
    text-align: center;
  }

  .code-input {
    box-sizing: border-box;
    margin-top: 96rpx;
    padding: 16rpx;
    
    width: 100%;
    height: 104rpx;
    border-radius: 20rpx;
    background: #EEEEEE;
  }
}

:deep() {
  .uv-code-input {
    justify-content: center;
    margin-top: 96rpx;
  }

  .uv-code-input__item {
    border: none !important;
    background: #EEEEEE;
    border-radius: 24rpx 24rpx 24rpx 24rpx;
  }
}
</style>