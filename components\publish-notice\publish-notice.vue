<template>
	<view class="content" @tap.stop>
		<view class="main">
			<view class="header">
				<text>发布须知</text>
				<image class="close"  src="@/static/icon/publish/<EMAIL>" mode="aspectFit" @tap="onClose" />
			</view>
			<view class="body">
				<slot>
					<text class="message">
						为及时收到咨询推送，避免错过订单，请务必关注同名公众号，平台提供一次短信通知服务；
						\n
						此外，您需要遵循以下规范：
						\n
						1、请勿发布含联系方式的海报、详情图片和文本描述内容，平台将会对违规服务项进行下架以及其他违规处理；
						\n
						2、请勿引导用户线下交易，若经查实，可能会冻结您的接单资格。
						\n
						3、请勿发布含敏感元素的内容如色情、暴力等，平台有权对此类内容进行下架等处理。
						\n
						4、请勿发布违法犯罪内容，您对您发布的内容负有法律责任，平台仅作为内容展示平台提供展示空间，若引起相关司法或刑事责任，平台保留配合执法机关进行调查取证的权力。
						\n
						平台鼓励发布原创内容，请勿盗用他人作品，若涉及侵权问题，平台有义务配合进行下架处理。
					</text>
				</slot>
			</view>
			<view class="footer">
				<view class="btn" :class="{'disabled': count}" @tap="onConfirm">{{btnText}}</view>
			</view>
		</view>
		
	</view>
</template>
<script lang="ts" setup>
	import { computed, ref, watchEffect } from "vue";
	// 倒计时
	const props = defineProps({
		cutdown: Number,
		content: String,
	});
	
	let timer: any = 0;
	const count = ref(props.cutdown || 0);
	const btnText = computed(() => {
		const title = "我已知晓并遵守";
		if (!props.cutdown) return title;
		return count.value ? `倒计时${count.value}秒` : title;
	});
	watchEffect(() => {
		const isFirstShow = props.cutdown;
		if (!isFirstShow) return clearInterval(timer);
		timer = setInterval(() => {
			if (count.value <= 0) return clearInterval(timer);
			count.value = count.value - 1;
		}, 1000);
		
	});
	interface Emits {
		(e: "close"): void;
		(e: "confirm"): void;
	}
	const emits = defineEmits<Emits>();
	const disable = ref(false);
	const onClose = () => {
		if (count.value) return
		emits("close");
	}
	const onConfirm = () => {
		if (count.value) return
		emits("confirm");
	}
	
</script>
<style scoped lang="scss">
	.content {
		position: fixed;
		display: flex;
		justify-content: center;
		align-items: center;
		left: 0;
		top: 0;
		height: 100%;
		width: 100%;
		background-color: rgba(0, 0, 0, 0.4);
		padding: 0 50rpx;
		box-sizing: border-box;
		z-index: 100;
		.main {
			display: flex;
			flex-direction: column;
			background-color: white;
			border-radius: 32rpx;
			overflow: hidden;
			padding: 0 40rpx 48rpx;
			.header {
				display: flex;
				position: relative;
				justify-content: center;
				align-items: center;
				height: 112rpx;
				font-size: 32rpx;
				.close {
					position: absolute;
					right: -8rpx;
					top: 32rpx;
					height: 48rpx;
					width: 48rpx;
					font-size: 0;
				}
			}
			.body {
				font-size: 24rpx;
				line-height: 44rpx;
				padding-bottom: 40rpx;
				color: #3D3D3D;
				
				.message {
					
				}
			}
			.footer {
				display: flex;
				justify-content: center;
				align-items: center;
				.btn {
					width: 400rpx;
					height: 80rpx;
					line-height: 80rpx;
					font-size: 36rpx;
					font-weight: 500;
					color: white;
					text-align: center;
					border-radius: 40rpx;
					background-color: rgba(254, 88, 183, 1);
				}
				
				.disabled {
					pointer-events: none;
					background-color: rgba(133, 135, 141, 1);
				}
			}	
		}
	}
</style>