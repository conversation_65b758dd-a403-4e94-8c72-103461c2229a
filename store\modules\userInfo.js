// 用户模块
import * as auth from '@/common/auth'
import * as userApi from '@/api/user'

const USER_INFO_STATE = {
	avatar: '',
	username: '',
	nickname: '',
	phone: '',
	id: '',
	idNumber: '',
	sex: null,
	token: '',
	uid: '',
	bio: '',
	gender: null,
	province: null,
	city: '',
	district: null,
	follow_count: 0,
	fans_count: 0,
	thumbs_up_count: 0,
	friend_count: 0,
	background_url: '',
	invite_url: '',
	qrcode_url: '',
	birthday: '',
	cert_level: 0,
	cert_until: 0,
	is_super: false,
	permissions: [],
}

const userInfo = {
	namespaced: true, // 添加命名空间
	state: {
		...USER_INFO_STATE
	},
	getters: {

	},
	mutations: {
		UPDATE_USER_INFO(state, payload) {
			for (let key in payload) {
				state[key] = payload[key]
			}
		},
		SET_TOKEN(state, token) {
			state.token = token;
			auth.setToken(token)
		},
		SET_AVATAR(state, avatar) {
			state.avatar = avatar;
		},
	},
	actions: {
		async login({
			state,
			commit
		}, payload) {
			return new Promise((resolve, reject) => {
				wx.login({
					success: async (data) => {
						console.log('登录获取 code:', data)
						const res = await userApi.login({
							wx_code: data.code, // 小程序用户登录凭证code
							phone_code: payload.phone_code, // 小程序获取手机号code
						})
						console.log('登录成功 res:', res)
						if (res.code === 20000) {
							auth.setToken(res.data.token)
							commit('UPDATE_USER_INFO', {
								token: res.data.token,
							})
						}
						resolve(res)
					}
				})
			})
		},
		// 获取用户信息
		getUserInfo({
			commit
		}) {
			return userApi.getUserInfo().then(res => {
				if (res.code === 20000) {
					console.log('获取用户信息:', res.data)
					commit('UPDATE_USER_INFO', {
						...res.data,
					})
				}
				return res
			}).catch(e => {
				console.log('getUserInfo err:', e)
			})
		},
		async logout({
			commit
		}) {
			// const res = await userApi.logout()
			// if (res.code === 20000) {
			// 	console.log('退出成功，安全退出')
			// } else {
			// 	console.log('退出成功，强制退出')
			// }
			commit('SET_TOKEN', '')
			commit('SET_AVATAR', '')
		},
		// 注销
		async logoff({
			commit
		}) {
			// const res = await userApi.logoff()
			// return res
			// return {
			// 	code: 200
			// }
		},
		// 退出登录清除用户信息
		cleanUseInfo({
			commit
		}) {
			auth.setToken('')
			commit('UPDATE_USER_INFO', {
				...USER_INFO_STATE,
			})
		},
	}
}

export default userInfo