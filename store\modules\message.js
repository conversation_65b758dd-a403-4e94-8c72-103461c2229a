import Config from '@/common/config'
import * as auth from '@/common/auth'

const MESSAGE_CACHE = 'NOTIFICATION_PAGE_MESSAGE_CACHE';

// 消息状态模块
const messageStore = {
	namespaced: true, // 添加命名空间
	state: {
		task: null,
		heartbeatInterval: 10000,
		reconnectCount: 0,
		maxReconnectCount: 5,
		isConnected: false,
		heartbeatTimer: null,
		message: uni.getStorageSync(MESSAGE_CACHE) || {},
		currenPageInfo: {
			consultId: null,
			isNotificationDetail: false,
			isFriend: false,
			conversationId: null
		}
	},
	mutations: {
		updateTask(state, socketTask) {
			state.task = socketTask
		},
		updateReconnectCount(state, reconnectCount) {
			state.reconnectCount = reconnectCount
		},
		updateIsConnected(state, isConnected) {
			state.isConnected = isConnected
		},
		updateHeartbeatTimer(state, timer) {
			state.heartbeatTimer = timer
		},
		updateMessage(state, message) {
			state.message = message
			uni.setStorageSync(MESSAGE_CACHE, message);
		},
		updateCurrentPageInfo(state, pageInfo) {
			state.currenPageInfo = pageInfo
		}
	},
	actions: {
		initClient({
			state,
			commit,
			dispatch,
			rootState
		}) {
			if (state.task) {
				return
			}
			return new Promise((resolve, reject) => {
				const socketTask = uni.connectSocket({
					url: `${Config.wsUrl}/ws/v1/foreground/message/channel`,
					complete: () => { }
				})
				commit('updateTask', socketTask)
				socketTask.onOpen(() => {
					commit('updateIsConnected', true)
					commit('updateReconnectCount', 0)
					console.log('WebSocket 已连接')

					const timer = setInterval(() => {
						dispatch('sendMessage', {
							"id": "idstring",
							"type": "ping",
							"data": {}
						})
					}, state.heartbeatInterval);
					commit('updateHeartbeatTimer', timer)

					resolve()
				})
				socketTask.onClose((e) => {
					commit('updateIsConnected', false)
					commit('updateTask', null)
					console.log('WebSocket 关闭事件', e);
				})
				socketTask.onError(async () => {
					if (state.heartbeatInterval) {
						clearInterval(state.heartbeatInterval)
					}

					if (state.reconnectCount >= state.maxReconnectCount) {
						socketTask.close()
						commit('updateTask', null)
					}
					commit('updateReconnectCount', state.reconnectCount + 1)

					if (!state.isConnected) {
						await dispatch('initClient')
					}
				})
				socketTask.onMessage((message) => {
					try {
						const res = JSON.parse(message.data);
						console.log('websocket 收到消息', res)
						const messageCache = state.message;
						// 区分咨询消息和聊天消息
						const consultDataTypes = ['receiveConsult', 'sendConsult'];
						const chatDataTypes = ['sendChat', 'receiveChat'];

						if (res.code !== 20000) return

						if (consultDataTypes.includes(res.type)) {
							if (res.data.msg_type === 'file' || res.data.msg_type === 'text') {
								const isInCurrentConsult = state.currenPageInfo.consultId == res.id
								if (res.type === 'receiveConsult') {
									// 收到一条新消息，更新未读消息
									uni.$emit('ordersNotification:refresh-newMsgCount', res.data.consult_id, isInCurrentConsult ? 0 : 1, res.data)
								}
								uni.$emit('orderConsult:receiveMessage', res.data)
								if (messageCache[res.data.consult_id]) {
									messageCache[res.data.consult_id].list.push(res.data);
								} else {
									messageCache[res.data.consult_id] = {
										list: [res.data],
									}
								}
								uni.$emit('orderNotificationDetail:scrollToBottom')
								commit('updateMessage', messageCache)
							} else if (res.data.msg_type === 'notice') {
								// message : "{"type":"create_order","data":{"amount":100,"appointment_at":0,"bond":0,"order_id":2,"order_number":"549120186154749952","status":1}}"
								const message = JSON.parse(res.data.message)
								uni.$emit('orderNotificationDetail:refreshOrderInfo', {
									status: message.data.status,
									amount: message.data.amount,
									number: message.data.order_number,
									id: message.data.order_id
								})
							}
						} else if (chatDataTypes.includes(res.type)) {
							if (res.data.msg_type === 'file' || res.data.msg_type === 'text') {
								const currentConversationId = parseInt(state.currenPageInfo.conversationId);
								// 判断是否在当前会话：检查会话ID是否匹配
								const isInCurrentChat = currentConversationId && res.id && res.id.includes(`user-chat-${currentConversationId}`);
								
								if (res.type === 'receiveChat') {
									// 收到好友聊天消息，先检查是否为新好友
									const fromId = res.data.from_id;
									const friendConversationList = rootState?.friendNotification?.friendConversationList || [];
									const existingFriend = friendConversationList.find(item => 
										parseInt(item.friend.id) === parseInt(fromId)
									);
									
									if (!existingFriend) {
										// 新好友发来消息，需要刷新conversation list=
										dispatch('friendNotification/refreshConversationList', null, { root: true });
									}
									
									// 更新好友消息未读数
									dispatch('friendNotification/updateFriendUnreadCount', {
										fromId: res.data.from_id,
										count: isInCurrentChat ? 0 : 1,
										messageData: res.data
									}, { root: true })
								}
								uni.$emit('friendChat:receiveMessage', res.data)
							}
						}
					} catch (error) {
						console.log(error, 'cannot receive message')
					}
				})
			})
		},
		closeSocket({
			state,
			commit
		}) {
			if (state.task) {
				state.task.close()
				commit('updateTask', null)
				clearInterval(state.heartbeatTimer)
				commit('updateHeartbeatTimer', null)
			}
		},
		sendMessage({
			state,
		}, data) {
			if (state.task) {
				state.task.send({
					data: JSON.stringify(data),
					success: () => console.log('WebSocket 发送消息成功：', data),
					fail: err => console.log('WebSocket 发送消息失败：', err)
				})
			}
		},
		loginSend({ dispatch }, consultId) {
			dispatch('sendMessage', {
				id: '' + consultId,
				type: 'login',
				data: {
					token: auth.getToken(),
					terminal: 'wxmp'
				}
			})
		},
		// 发送咨询消息
		commentSend({ dispatch }, {
			consultId = null,
			content = '',
			type = 'text'
		}) {
			if (!consultId) return

			dispatch('loginSend', consultId)
			dispatch('sendMessage', {
				id: consultId,
				type: 'sendConsult',
				data: {
					to: +consultId,
					content,
					type
				}
			})
		},
		// 发送聊天消息
		chatSend({ dispatch }, {
			content = '',
			conversationId = null,
			friendId = null,
			type = 'text'
		}) {
			if (!conversationId || !friendId) return
			dispatch('loginSend', 'user-chat-' + conversationId)
			dispatch('sendMessage', {
				id: 'user-chat-' + conversationId,
				type: 'sendChat',
				data: {
					to: +friendId,
					content,
					type
				}
			})
		}
	}
}

export default messageStore