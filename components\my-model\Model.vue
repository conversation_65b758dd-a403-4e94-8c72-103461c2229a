<script setup>
defineProps(['height','show', 'onClose'])
</script>

<template>
    <div class="model" v-if="show">
        <div class="content" :style="{height}">
            <div class="title">
                <slot name="title"></slot>
                <image src="/static/icon/<EMAIL>" alt="" class="close" @click="onClose"/>
            </div>
            <slot name="content"></slot>
        </div>
    </div>
</template>

<style scoped lang="scss">
.model {
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    z-index: 999;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.4);
}

.content {
    width: 100%;
    min-height: 200rpx;
    max-height: 1030rpx;
    box-shadow: 0px -16rpx 64rpx 0px rgba(0,0,0,0.16);
    border-radius: 32rpx 32rpx 0px 0px;
    background: #FFFFFF;
}

.title {
    position: relative;
    .close {
        position: absolute;
        top: 24rpx;
        right: 24rpx;
        width: 48rpx;
        height: 48rpx;
    }
}
</style>