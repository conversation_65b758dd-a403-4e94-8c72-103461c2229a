<template>
	<z-paging class="app-container" ref="paging" v-model="orders" @query="queryList" :safe-area-inset-bottom="true" :use-safe-area-placeholder="true">
		<template #top>
			<Navbar :type="1" :showSearch="true" placeholder="" leftText="我的订单" :rightWidth="rightWidth"></Navbar>
			<view class="tabs">
				<dust-tabs v-model="activeTab" :list="tabList" tabType="secondary" lineGradientBorder showActiveStar @update:modelValue="updatemodelValue"></dust-tabs>
			</view>
		</template>
		<template #empty>
			<uv-empty />
		</template>
		<uni-list style="margin-top: 32rpx" :border="false">
			<uni-list-item v-for="(item, index) in orders" :key="item.id" :border="false">
				<template v-slot:body>
					<view class="listBox" @click="toDetails(item.id, index)">
						<view class="listBoxBorder">
							<view class="listBoxTop">
								<view class="listBoxTopLeft">
									<image src="../../../static/icon/<EMAIL>" mode="aspectFill"></image>
									<text>订单编号：{{ item.number }}</text>
								</view>
								<text>{{ typeSwitch(item.status) }}</text>
							</view>
							<view class="listBoxCenter">
								<image :src="prefixFilePath(item.snapshot.cover)" mode="aspectFill"></image>
								<view class="listBoxCenterRight">
									<text style="height: 88rpx" class="txtellipsis">{{ item.snapshot.name }}</text>
									<text style="margin-top: 24rpx">下单时间：{{ timeToString(item.created_at, 2) }}</text>
									<view>
										<text>实付：</text>
										<text>
											<text class="price">￥</text>
											<text>{{ item.amount / 100 }}</text>
										</text>
									</view>
								</view>
							</view>
							<view class="listBoxBottom" v-if="![16, 32, 64, 128].includes(item.status)">
								<view class="listBoxBottomRed" v-if="item.status == 1 || item.status == 256" @click.native.stop="topay(index)">
									<text>立即支付</text>
								</view>
								<view class="listBoxBottomBlack" v-if="item.status == 2" @click.native.stop="userDefault(index)">
									<text>取消订单</text>
								</view>
								<view class="listBoxBottomRed" v-if="item.status == 2" @click.native.stop="use(item.appointment_at, index)">
									<text>去使用</text>
								</view>
								<view class="listBoxBottomBlack" v-if="item.status == 8" @click.native.stop="onceConsult(item.service_item.id)">
									<text>再次咨询</text>
								</view>
								<view class="listBoxBottomRed" v-if="item.status == 8 && !item.has_review" @click.native.stop="toComment(item.id, index)">
									<text>去评价</text>
								</view>
							</view>
						</view>
					</view>
				</template>
			</uni-list-item>
		</uni-list>
	</z-paging>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { ordersList, payOrder, userExecuteOrder, userDefaultCancelOrder } from '../../../api/order';
import { useStore } from 'vuex';
import { timeToString } from '@/common/utils';
import { isApp } from '@/common/utils'
import { createConsult } from '../../../api/notification';
import { getMineBalance } from '../../../api/wallet';

const store = useStore();
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);
const rightWidth = computed(() => isApp() ? '0' : undefined);
function toDetails(id, index) {
	uni.navigateTo({
		url: `/pages/packageA/orderDetails/orderDetails?id=${id}&type=buy&&index=${index}`,
		events: {
			// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
			orderDeatilsComment: function (data) {
				orders.value[data.index].has_review = data.has_review;
			},
			orderDetailsType(data) {
				orders.value[index].status = data.status;
			}
		}
	});
}
function onceConsult(id) {
	createConsult(id).then((res) => {
		if (res.code === 20000) {
			uni.navigateTo({
				url: `/pages/tabbar/notification/ordersNotificationDetails?type=1&consultid=${res.data.id}`
			});
		}
	});
}
function prefixFilePath(url) {
	if (!url) return '';
	return cloudFileUrl.value + url;
}
function toComment(id, index) {
	uni.navigateTo({
		url: `/pages/packageA/orderComment/orderComment?id=${id}&index=${index}`,
		events: {
			// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
			orderListComment: function (data) {
				console.log('列表跳评论', data);
				orders.value[data.index].has_review = data.has_review;
			}
		}
	});
}
function typeSwitch(type) {
	let txt = '';
	switch (type) {
		case 1:
			txt = '待支付';
			break;
		case 2:
			txt = '待执行';
			break;
		case 4:
			txt = '已执行';
			break;
		case 8:
			txt = '已完成';
			break;
		case 16:
			txt = '已取消';
			break;
		case 32:
			txt = '已弃用';
			break;
		case 64:
			txt = '用户违约';
			break;
		case 128:
			txt = '商家违约';
			break;
		case 256:
			txt = '支付中';
			break;
		case 512:
			txt = '订单超时';
			break;
		case 1024:
			txt = '已退款';
			break;
		default:
			break;
	}
	return txt;
}
const activeTab = ref(0);
// OrderStatusPendingPayment int32 = 0b00000000001 // 1 待支付
// OrderStatusPendingUse     int32 = 0b00000000010 // 2 待使用
// OrderStatusUsed           int32 = 0b00000000100 // 4 已使用
// OrderStatusFinished       int32 = 0b00000001000 // 8 已完成
// OrderStatusCanceled       int32 = 0b00000010000 // 16 已取消（用户）
// OrderStatusDeprecated     int32 = 0b00000100000 // 32 已弃用（商家）
// OrderStatusDefaulted      int32 = 0b00001000000 // 64 已违约（用户）
// OrderStatusBreaked        int32 = 0b00010000000 // 128 已毁约（商家）
// OrderStatusPaymenting     int32 = 0b00100000000 // 256 支付中
// OrderStatusTimeout        int32 = 0b01000000000 // 512 已超时
// OrderStatusCompensate     int32 = 0b10000000000 // 1024 已协商退款
// 全部（时间排序），待执行，未付款 ，已执行，已取消
const tabList = ref([
	{ name: '全部', value: 0, apiValue: 0 },
	{ name: '待执行', value: 1, apiValue: 2 },
	{ name: '未付款', value: 2, apiValue: 1 },
	{ name: '已执行', value: 3, apiValue: 4 + 8 },
	{ name: '已取消', value: 4, apiValue: 16 + 32 + 64 + 128 + 512 + 1024 }
]);
function findActiveTab() {
	const tab = tabList.value.find((item) => item.value === activeTab.value);
	return tab;
}
//用户违约
function userDefault(index) {
	uni.showModal({
		title: '提示',
		content: `取消订单将扣除违约金${(orders.value[index].amount * orders.value[index].bond) / 100 / 100}元`,
		success: function (res) {
			if (res.confirm) {
				userDefaultCancelOrder(orders.value[index].id).then((res2) => {
					if (res2.code === 20000) {
						uni.showToast({
							title: '订单已取消，订单金额将在7个工作日，自动退款',
							icon: 'none',
							duration: 2000
						});
						orders.value[index].status = 64;
					}
				});
			}
		}
	});
}
//用户使用
function use(time, index) {
	if (isTime(time)) {
		uni.showModal({
			title: '提示',
			content: `请确认商家已为您完成了服务`,
			success: function (res) {
				if (res.confirm) {
					userExecuteOrder(orders.value[index].id).then((res2) => {
						if (res2.code === 20000) {
							orders.value[index].status = 4;
						}
					});
				}
			}
		});
	} else {
		uni.showToast({
			title: '当前还未到服务时间',
			icon: 'none',
			duration: 2000
		});
	}
}
//判断时间
function isTime(data) {
	return new Date().getTime() > data;
}
async function MineBalance() {
	return getMineBalance().then(async (res) => {
		return res.data.balance - res.data.freeze;
	});
}
const getLoginCode = () => {
	return new Promise((resolve, reject) => {
		uni.login({
			success: (res) => {
				if (!res.code) {
					reject(res);
				}
				resolve(res.code);
			},
			fail: (err) => {
				reject(err);
			}
		});
	});
};
//用户支付
async function topay(index) {
	uni.showModal({
		title: '提示',
		content: `是否支付订单金额${orders.value[index].amount / 100}元`,
		success: async function (r) {
			if (r.confirm) {
				let d = {
					password: '8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92'
				};
				let balance = await MineBalance();
				if (balance / 100 < orders.value[index].amount / 100) {
					//平台支付
					d.pay_method = 2;
					const code = await getLoginCode();
					d.code = code;
					payOrder(orders.value[index].id, d).then((res) => {
						uni.requestPayment({
							provider: 'wxpay',
							orderInfo: res.data.wx,
							timeStamp: res.data.wx.timeStamp,
							nonceStr: res.data.wx.nonceStr,
							package: res.data.wx.package,
							signType: res.data.wx.signType,
							paySign: res.data.wx.paySign,
							success: async (res) => {
								if (res.errMsg === 'requestPayment:ok') {
									uni.showToast({
										title: '支付成功',
										duration: 2000
									});
									orders.value[index].status = 2;
								}
							},
							fail: (err) => {
								console.log(err, 'fail');
							}
						});
					});
				} else {
					//余额支付
					d.pay_method = 1;
					payOrder(orders.value[index].id, d).then((res) => {
						if (res.code == 20000) {
							uni.showToast({
								title: '支付成功',
								duration: 2000
							});
							orders.value[index].status = 2;
						} else {
							uni.showToast({
								title: '支付失败',
								icon: 'error',
								duration: 2000
							});
						}
					});
				}
			}
		}
	});
}
watch(activeTab, () => {
	paging.value.reload();
});

const paging = ref(null);
const orders = ref([]);

function searchInput() {}
async function queryList(pageNo, pageSize) {
	const params = {
		page: pageNo,
		size: 10,
		type: 'buy'
	};
	const status = findActiveTab();
	if (status.apiValue) {
		params.status = status.apiValue;
	}
	let res = await ordersList(params);
	if (res.code === 20000) {
		res.data.results.forEach((item) => {
			item.snapshot = JSON.parse(item.snapshot);
		});
		console.log(res.data.results);
		paging.value.complete(res.data.results);
	} else {
		paging.value.complete(false);
	}
}
</script>

<style lang="scss" scoped>
.tabs {
	margin: 34rpx 32rpx 20rpx 32rpx;
	color: #000 !important;
}
:deep(.dust-tabs-container) {
	gap: 12px !important;
	justify-content: space-between !important;
}
.listBox {
	width: 100%;
	min-height: 332rpx;
	background-color: #f7f8fa;
	border-radius: 24rpx;
	.listBoxBorder {
		padding-left: 24rpx;
		padding-right: 24rpx;
	}
	.listBoxTop {
		margin-top: 24rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 28rpx;
		color: #48daea;
		.listBoxTopLeft {
			display: flex;
			align-items: center;
			color: #85878d;
			image {
				width: 40rpx;
				height: 40rpx;
				margin-right: 16rpx;
			}
		}
	}
	.listBoxCenter {
		height: 228rpx;
		margin-top: 28rpx;
		display: flex;
		image {
			width: 208rpx;
			height: 208rpx;
			border-radius: 20rpx;
			margin-right: 32rpx;
		}
		.listBoxCenterRight {
			width: 414rpx;
			display: flex;
			flex-direction: column;
			text:nth-child(1) {
				font-size: 32rpx;
				color: #3d3d3d;
			}
			text:nth-child(2) {
				font-size: 24rpx;
				color: #767676;
			}
			view {
				font-size: 24rpx;
				display: flex;
				margin-top: 20rpx;
				text:nth-child(1) {
					color: #767676;
				}
				text:nth-child(2) {
					color: #fe58b7;
					font-size: 36rpx;
				}
				.price {
					color: #fe58b7 !important;
					font-size: 24rpx;
				}
			}
		}
	}
	.listBoxBottom {
		// margin-top: 20rpx;
		border-top: 2rpx solid #eeeeee;
		height: 100rpx;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		view {
			width: 144rpx;
			height: 56rpx;
			border-radius: 48rpx;
			font-size: 24rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		.listBoxBottomBlack {
			border: 2rpx solid #d8d8d8;
			color: #1f1f1f;
		}
		.listBoxBottomRed {
			background-color: #fe58b7;
			color: #ffffff;
			margin-left: 32rpx;
		}
	}
}
</style>
