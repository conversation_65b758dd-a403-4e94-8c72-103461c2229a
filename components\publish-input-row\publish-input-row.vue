<template>
	<view class="containers">
		<view class="label">
			<text>{{ props.label }}</text>
		</view>
		<view class="nav">
			<input class="input" :type="props.inputType" :style="styleObject" :placeholder="props.placehold" v-model="model" />
			<uni-icons v-if="(props.showClear && !props.disabled && model)"
				type="clear" color="#c0c4cc" style="right: 0px;position: absolute;" @tap.stop="clearHandle" />
		</view>
	</view>
</template>

<script setup lang="ts">
import { PropType, computed } from 'vue';

const model = defineModel<string | null>();
const props = defineProps({
	label: {
		type: String,
		require: true
	},

	placehold: {
		type: String,
		default: "请输入"
	},
	
	textAlign: {
		type: String,
		default: "right"
	},

	showClear: {
		type: Boolean,
		default: true
	},
	
	disabled: {
		type: Boolean,
		default: false
	},
	
	inputType: {
		type: String,
		default: "number", 	// tel/number/text/digit
	}
});
const styleObject = computed(() => ({
	textAlign: props.textAlign
}));
interface Emits {
	(e: "clear"): void;
}
const emits = defineEmits<Emits>();

const clearHandle = () => {
	model.value = null,
	emits("clear")
}

</script>

<style scoped lang="scss">
.containers {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 80rpx;
	width: 100%;
	border-radius: 16rpx;
	padding: 0 24rpx;
	box-sizing: border-box;
	background-color: rgba(247, 248, 250);

	.label {
		flex: 0 0 auto;
		font-size: 32rpx;
		color: rgba(61, 61, 61);
		
	}

	.value {
		font-size: 32rpx;
		color: rgba(61, 61, 61);
	}
	
	.input {
		color: rgba(61, 61, 61);
		text-align: right;
	}

	.placeholder {
		color: rgba(133, 135, 141);
		font-size: 32rpx;
	}

	.nav {
		position: relative;
		display: flex;
		flex: 1 1 auto;
		padding: 0px 23px;
		.input {
			width: 100%
		}
		.image {
			width: 40rpx;
			height: 40rpx;
		}
	}
}
</style>