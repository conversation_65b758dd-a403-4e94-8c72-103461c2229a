<template>
  <z-paging ref="paging" class="app-container" :fixed="false">
    <template #empty>
		<empty-list v-if="dataList.length === 0">
			<text>{{ comboTitleMap.emptyCombo }}</text>
		</empty-list>
    </template>
    <template #top>
      <Navbar :type="1" :leftText="`${categoryTitle || ''}套餐`"></Navbar>
    </template>

    <view class="combolist-container">
      <uni-list class="dust-uni-list" :border="false">
        <uni-list-item :border="false" class="dust-uni-list-item" v-for="(item, index) in dataList" :key="item.id">
          <template v-slot:body>
            <view class="combo-item-wrapper" @click="() => handleTo(item.id)">
              <daliy-combo-item :item="item" />
            </view>
          </template>
        </uni-list-item>
      </uni-list>
    </view>

    <template #bottom>
      <view class="button-wrapper">
        <view class="btn" @click="() => handleTo('add')">新增套餐</view>
      </view>
    </template>
  </z-paging>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useTitle } from './hooks/useTitle'
import { getMineServiceDetailItems } from '@/api/mineService'
import DaliyComboItem from './components/daliy-combo-item.vue'

const { typeTitle, categoryTitle, comboTitleMap } = useTitle()

const dataList = ref([])
const serviceId = ref(null)

onLoad(async (option) => {
  const list = await queryCombos(option.serviceId)
  dataList.value = list

  serviceId.value = option.serviceId
})

uni.$on('service-combo-list:reload-data', async () => {
  const list = await queryCombos(serviceId.value)
  dataList.value = list
})

async function queryCombos(id) {
  try {
    const res = await getMineServiceDetailItems(id)

    if (res.code === 20000) {
      return res.data.sort((a, b) => b.is_on_sale - a.is_on_sale)
    }
    return []
  } catch (error) {
    console.log(error, 'getMineServiceDetailItems service')
    return []
  }
}

const handleTo = (id) => {
  let url = `/pages/packageA/service-create-edit/service-create-edit?serviceId=${serviceId.value}&categoryTitle=${categoryTitle.value}&typeTitle=${typeTitle.value}&itemId=${id}&edit=true`

  if(id === 'add') {
    url = `/pages/packageA/service-create-edit/service-create-edit?serviceId=${serviceId.value}&categoryTitle=${categoryTitle.value}&typeTitle=${typeTitle.value}`
  }

  url && uni.navigateTo({
    url
  })
}
</script>

<style scoped lang='scss'>
.empty-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #85878D;

  .icon {
    width: 380rpx;
    height: 380rpx;
  }
}

.combolist-container {
  margin-top: 24rpx;
  flex: 1;
  box-sizing: border-box;
  padding: 0 24rpx;

  .combo-item-wrapper {
    width: 100%;
  }
}

.button-wrapper {
  box-sizing: border-box;
  width: 100%;
  padding: 0 24rpx 68rpx;

  .btn {
    width: 100%;
    height: 96rpx;
    background: #FE58B7;
    border-radius: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
  }
}
</style>