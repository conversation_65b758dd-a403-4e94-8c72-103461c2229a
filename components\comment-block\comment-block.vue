<template>
  <view class="wrapper">
    <image :src="prefixFilePath(item.avatar)" class="avatar" @tap="handleToUserMine(item)"></image>

    <view class="content">
      <view class="name">
        <text>{{ item.name }}</text>
        <text class="rating" v-if="item.rating">评分: {{ item.rating }}</text>
      </view>
      <view class="image-list" v-if="item.image">
        <image :src="prefixFilePath(item.image)" @click="() => handlePreviewImage(item.image)"></image>
      </view>
      <view class="comment">{{ item.comment }}</view>
      <view class="footer" v-if="showFooter">
        <view class="time">{{ item.time }}</view>
        <view class="action" @click="handleReply">回复</view>
        <image v-if="showMore" src="/static/icon/dimension/more-horizontal.png" @click.stop="handleMoreAction"></image>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'

const store = useStore()
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl);

function prefixFilePath(url) {
  if (!url) return '';
  return cloudFileUrl.value + url;
}

const props = defineProps({
  item: {
    type: Object,
    default: () => ({})
  },
  showFooter: {
    type: Boolean,
    default: true
  },
  showMore: {
    type: Boolean,
    default: false
  }
})

const handlePreviewImage = (image) => {
  const urls = [image].map(item => prefixFilePath(item))
  uni.previewImage({
    urls
  })
}

const emit = defineEmits(['onMore', 'onReply'])
const handleMoreAction = () => {
  emit('onMore')
}

const handleReply = () => {
  emit('onReply')
}
function handleToUserMine(item) {
	uni.navigateTo({
		url: '/pages/home-pages/user-mine?id=' + item.user_id,
	})
}
</script>

<style scoped lang='scss'>
.wrapper {
  width: 100%;
  display: flex;
  gap: 24rpx;

  .avatar {
    width: 84rpx;
    height: 84rpx;
    border-radius: 50%;
    border: 2rpx solid #fff;
  }

  .image-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16rpx;

    image {
      width: 184rpx;
      height: 184rpx;
      border-radius: 4rpx;
    }
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16rpx;

    .name {
      font-weight: 400;
      font-size: 28rpx;
      color: #85878D;
      line-height: 28rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .rating {
        color: #FE58B7;
      }
    }

    .comment {
      font-weight: 400;
      font-size: 32rpx;
      color: #1F1F1F;
      line-height: 48rpx;
    }

    .footer {
      display: flex;
      align-items: center;
      gap: 40rpx;

      .time {
        font-weight: 400;
        font-size: 24rpx;
        color: #85878D;
        line-height: 24rpx;
      }

      .action {
        font-size: 28rpx;
        color: #48DAEA;
      }

      image {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
}
</style>