// 环境类型定义
type EnvType = 'development' | 'staging' | 'production';
type MiniProgramEnv = 'develop' | 'trial' | 'release';

// 环境API配置
interface EnvironmentConfig {
	hostUrl : string;
	wsUrl : string;
	fileUrl : string;
	cloudFileUrl : string;
	amapKey : string;
	tencentKey : string;
	mapSearchUrl : string;
	envVersion : MiniProgramEnv;
}

// 默认配置（将从环境变量中覆盖）
export const DEFAULT_CONFIG : Record<EnvType, EnvironmentConfig> = {
	development: {
		hostUrl: 'https://develop.ccycat.com',
		wsUrl: 'wss://develop.ccycat.com',
		fileUrl: 'https://develop.cos.ccycat.com/',
		cloudFileUrl: 'https://ciyuanxingchen-1306919981.cos.ap-beijing.myqcloud.com/',
		amapKey: 'b4988972efa8de3e1544093e33b4e16f',
		tencentKey: '4JLBZ-MIDCM-N7J6M-6B6OS-JPJAK-32FLV',
		mapSearchUrl: 'https://apis.map.qq.com/ws/place/v1',
		envVersion: 'develop'
	},
	staging: {
		hostUrl: 'https://staging.ccycat.com',
		wsUrl: 'wss://staging.ccycat.com',
		fileUrl: 'https://staging.cos.ccycat.com/',
		cloudFileUrl: 'https://staging.cos.ccycat.com/',
		amapKey: 'b4988972efa8de3e1544093e33b4e16f',
		tencentKey: '4JLBZ-MIDCM-N7J6M-6B6OS-JPJAK-32FLV',
		mapSearchUrl: 'https://apis.map.qq.com/ws/place/v1',
		envVersion: 'trial'
	},
	production: {
		hostUrl: 'https://production.ccycat.com',
		wsUrl: 'wss://production.ccycat.com',
		fileUrl: 'https://production.cos.ccycat.com/',
		cloudFileUrl: 'https://production.cos.ccycat.com/',
		amapKey: 'b4988972efa8de3e1544093e33b4e16f',
		tencentKey: '4JLBZ-MIDCM-N7J6M-6B6OS-JPJAK-32FLV',
		mapSearchUrl: 'https://apis.map.qq.com/ws/place/v1',
		envVersion: 'release'
	}
};

/**
 * APP 获取当前环境配置
 */
export function getEnvConfig() : EnvironmentConfig {
	const envType = process.env.NODE_ENV === 'development' ? 'staging' : 'production'

	// 合并环境变量和默认配置
	const config = {
		...DEFAULT_CONFIG[envType],
		hostUrl: DEFAULT_CONFIG[envType].hostUrl,
		wsUrl: DEFAULT_CONFIG[envType].wsUrl,
		fileUrl: DEFAULT_CONFIG[envType].fileUrl,
		cloudFileUrl: DEFAULT_CONFIG[envType].cloudFileUrl,
		envVersion: DEFAULT_CONFIG[envType].envVersion
	};

	return config;
}

// 导出类型
export type { EnvironmentConfig, EnvType, MiniProgramEnv };