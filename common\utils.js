import Config from "@/common/config";
import dayjs from "dayjs";

/** 清理空格、回车符 */
export function replacenull(Str) {
	var result1 = Str.replace(/\ +/g, ''); //去掉空格
	result1 = Str.replace(/[ ]/g, ''); //去掉空格
	result1 = Str.replace(/[\r\n]/g, ''); //去掉回车换行
	return result1;
}
// 获取页面宽度并动态计算高度 16:9 高度
export const calculateHeight = (paddingRpx = 24) => { // 左右内边距默认24rpx
	const paddingPx = uni.upx2px(paddingRpx); // 将rpx转换为px
	const screenWidth = uni.getSystemInfoSync().screenWidth; // 获取屏幕宽度（单位为px）
	const containerWidth = screenWidth - paddingPx * 2; // 减去左右内边距
	const containerHeight = Math.round((screenWidth - paddingPx * 2) * 9 / 16); // 计算16:9的高度

	return {
		width: containerWidth + 'px',
		height: containerHeight + 'px',
	}
}

/** get url 入参拼接 */
export function changeParam(url, param) {
	let str = ''
	if (param) {
		const encodedParams = Object.entries(param)
			.map(([key, value]) => {
				return `${key}=${encodeURIComponent(value)}`
			})
			.join('&');
		str = '?' + encodedParams
	}

	return url + str;
}

/**
 * 
 * @param {number} count 
 * @returns 100 1.0k 1.0w
 */
export function formatCountToString(count) {
	if (typeof count !== 'number') return 0
	if (count >= 1000) {
		return `${(count / 1000).toFixed(1)}k`
	} else if (count >= 10000) {
		return `${(count / 10000).toFixed(1)}w`
	} else {
		return count
	}
}

/**
 * 
 * @param {number} timestamp 时间戳
 * @param {string} connect 连接词 
 * @param {number} type 类型
 * @returns dddd-hh-mm
 */
export function timeToString(timestamp, type, connect = '.') {
	var myDate = new Date(timestamp);
	var y = myDate.getFullYear(); //年
	var m = (myDate.getMonth() + 1).toString().padStart(2, '0'); //月
	var d = myDate.getDate().toString().padStart(2, '0'); //日
	var hh = myDate.getHours().toString().padStart(2, '0');
	var mm = myDate.getMinutes().toString().padStart(2, '0');
	var ss = myDate.getSeconds().toString().padStart(2, '0');

	if (type == 1) { //yyyy-mm
		return [y, m].join(connect);
	} else if (type == 2) { //yyyy-mm-dd
		return [y, m, d].join(connect);
	} else if (type == 3) { //2020年02月
		return y + '年' + m + '月';
	} else if (type == 4) { //2020.02.02 00:00:00
		return [y, m, d].join(connect) + ' ' + [hh, mm, ss].join(':');
	} else if (type == 5) { //2020.02.02
		return [y, m, d].join(connect);
	} else if (type == 6) { // 2020.02
		return [y, m].join(connect);
	} else { //2020-02-02 00:00:00
		return [y, m, d].join(connect) + ' ' + [hh, mm, ss].join(':');
	}
}

// 路由跳转
export function navigateToWithCorrectStack(data) {
	const {
		targetRoute,
		emitName,
		params = {},
	} = data
	const pages = getCurrentPages();
	let delta = 0;

	// 计算需要返回的层级数，使当前页面栈回到目标页面之前的页面
	for (let i = pages.length - 1; i >= 0; i--) {
		if (targetRoute.indexOf(pages[i].route) > -1) {
			delta = pages.length - 1 - i;
			break;
		}
	}
	console.log('delta:', delta)
	if (targetRoute.indexOf('/pages/tabbar/') > -1) {
		// 如果是回到 tabbar 页面，就直接用 switchTab
		console.log('targetRoute:', targetRoute)
		uni.reLaunch({
			url: targetRoute,
			success() {
				// 返回成功后，使用事件机制在中间页面进行重定向跳转
				if (!!emitName) {
					uni.$emit(emitName, params);
				}
			}
		})
		return
	}

	if (delta > 0) {
		uni.navigateBack({
			delta: delta,
			success: () => {
				// 返回成功后，使用事件机制在中间页面进行重定向跳转
				if (!!emitName) {
					uni.$emit(emitName, params);
				}
			}
		});
	} else {
		// 如果没有找到目标页面，直接跳转到目标页面
		const paramString = Object.keys(params).map(key => `${key}=${[key]}`).join('&');
		uni.redirectTo({
			url: changeParam(targetRoute, params),
		});
	}
}
/** 提示弹窗 */
export function uptip(txt, icon = 'error') {
	return uni.showToast({
		icon,
		title: txt
	});
}
/** 跳转登录页 */
export function toLog() {
	if (['', null, undefined].includes(uni.getStorageSync('storage_token'))) {
		uni.navigateTo({
			url: "/pages/packageA/login-pwd/login-pwd"
		})
	}
}

// 过滤表情
export const emoteRegular =
	/[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi
export function filterEmote(value) {
	return value.replace(emoteRegular, "")
}

// 将时间戳转换为'YYYY-MM-DD'格式的日期
export function formatTimestampToDate(timestamp) {
	const date = new Date(timestamp);
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	const dd = `${year}-${month}-${day}`
	return dd;
}

// 判断是否为 iPhoneX 系列
export function isIPhoneX() {
	// 获取系统信息
	const systemInfo = uni.getDeviceInfo()
	const model = systemInfo.model.toLowerCase();
	const iphoneXSeriesRegex = /iphone\s(?:x|(10|[1-9][0-9]))/i;
	return iphoneXSeriesRegex.test(model);
}

/**
 * 根据图片路径返回完成的图片地址
 * @param {string} imagePath 图片路径
 * @returns {string} 完整路径
 */
export function getCompletedImageUrl(imagePath) {
	const isCompleteUrl = imagePath.indexOf("http") >= 0;
	if (isCompleteUrl) return imagePath;

	let completedUrl = Config.fileUrl;
	if (!completedUrl) return imagePath;

	(completedUrl.slice(-1) !== "/") && (completedUrl = completedUrl + "/");
	const isSplitPrefix = imagePath.slice(0, 1) === "/";
	completedUrl = completedUrl + (isSplitPrefix ? imagePath.slice(1) : imagePath);
	return completedUrl;
}

/**
 * 获取当前平台信息
 * @return {string} 平台信息
 */
export function getPlatform() {
	const platform = uni.getSystemInfoSync().platform.toLowerCase();
	console.log('当前平台信息：', platform);

	return platform;
}
/**
 * 是否是app平台
 * @return {boolean} 是否是app平台
 */
export function isApp() {
	return process.env.UNI_PLATFORM === 'app'
}

export function formatDateRange(start, end) {
	if (!start || !end) return '';

	// 确保输入是日期格式
	const startDayjs = dayjs(start);
	const endDayjs = dayjs(end);

	if (!startDayjs.isValid() || !endDayjs.isValid()) return '';

	// 如果起止日期相同
	if (startDayjs.isSame(endDayjs, 'day')) {
		return startDayjs.format('YYYY年MM月DD日');
	}

	// 如果年份相同
	if (startDayjs.isSame(endDayjs, 'year')) {
		// 如果月份相同
		if (startDayjs.isSame(endDayjs, 'month')) {
			return `${startDayjs.format('YYYY年MM月DD日')} 至 ${endDayjs.format('DD日')}`;
		}
		// 月份不同
		return `${startDayjs.format('YYYY年MM月DD日')} 至 ${endDayjs.format('MM月DD日')}`;
	}

	// 年份不同
	return `${startDayjs.format('YYYY年MM月DD日')} 至 ${endDayjs.format('YYYY年MM月DD日')}`;
}

export function formatTimeRange(start, end) {
	if (!start || !end) return '';
	const startDayjs = dayjs(start);
	const endDayjs = dayjs(end);

	const timeFormat = 'HH:mm';

	if (startDayjs.isSame(endDayjs, 'day')) {
		return `活动时间 ${startDayjs.format(timeFormat)} ~ ${endDayjs.format(timeFormat)}`;
	}

	return `活动时间 ${startDayjs.format(timeFormat)} ~ ${endDayjs.format(timeFormat)}`;
}