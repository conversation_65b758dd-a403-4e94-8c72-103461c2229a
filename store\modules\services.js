

/**
 * 单个服务信息
 * @typedef {Object} ServiceInfo
 * @property {string} name - 服务名称
 * @property {number} value - 服务标识
 * @property {string} icon - 服务icon
 * @property {boolean} disabled - 服务是否可用
 * @property {boolean} show - 服务是否显示（首页使用）
 */

/** 
 * @type {ServiceInfo[]} 
 */
const initServeceList = [{
		name: '约拍',
		value: 0,
		icon: '/static/icon/dust_icon_sy.png',
		disabled: false,
		show: true,
	},
	{
		name: '约妆',
		value: 1,
		icon: '/static/icon/dust_icon_hz.png',
		disabled: false,
		show: true,
	},
	{
		name: '开团',
		value: 2,
		icon: '/static/icon/dust_icon_kt.png',
		disabled: true,
		show: false,
	},
	{
		name: 'COS',
		value: 3,
		icon: '/static/icon/dust_icon_cos.png',
		disabled: true,
		show: false,
	},
	{
		name: '约毛',
		value: 4,
		icon: '/static/icon/dust_icon_ym.png',
		disabled: true,
		show: false,
	}
];

/**
 * store类型
 * @typedef {Object} Store
 * @property {boolean} namespaced - 是否开启命名空间
 * @property {readonly ServiceInfo[]} state - 状态数据
 * @property {{serviceOption: (state: ServiceInfo[]) => ServiceInfo[]}} getters - 计算属性
 */

/**
 * 公共状态模块
 * @type {Store} 
 */
const serviceStore = {
	namespaced: true, // 添加命名空间
	state: initServeceList,
	getters: {
		serviceOption: (state) => {
			return state.filter(item => !item.disabled);
		}
	},
}

export default serviceStore