{"name": "次元星尘", "appid": "__UNI__A74457B", "description": "二次元服务平台", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "compatConfig": {"MODE": 2}, "app-plus": {"usingComponents": true, "compilerVersion": 3, "modules": {"Camera": {}, "Geolocation": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "ios": {"idfa": true, "dSYMs": false}, "sdkConfigs": {"geolocation": {"system": {"__platform__": ["ios", "android"]}}, "maps": {}}}}, "quickapp": {}, "mp-weixin": {"appid": "wxbc43897ebc0dccff", "setting": {"urlCheck": false, "es6": true, "postcss": true, "minified": true, "bigPackageSizeSupport": true}, "usingComponents": true, "permission": {"scope.userLocation": {"desc": "位置信息将用于小程序定位"}}, "requiredPrivateInfos": ["getLocation"]}, "vueVersion": "3", "locale": "zh-Hans", "h5": {"sdkConfigs": {"maps": {"tencent": {"key": "4JLBZ-MIDCM-N7J6M-6B6OS-JPJAK-32FLV"}}}}}