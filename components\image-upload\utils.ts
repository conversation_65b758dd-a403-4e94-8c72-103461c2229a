function pickExclude(obj, keys) {
	// 某些情况下，type可能会为
    if (!['[object Object]', '[object File]'].includes(Object.prototype.toString.call(obj))) {
        return {}
    }
    return Object.keys(obj).reduce((prev, key) => {
        if (!keys.includes(key)) {
            prev[key] = obj[key]
        }
        return prev
    }, {})
}

export interface Resource {
	type: "video";
	url: string;
	size: number;   // 单位B
	thumb?: string; // 视频缩略图
};

export function chooseImage(multiple: boolean, maxCount: number) {
	return new Promise<Response[]>((resolve, reject) => {
		uni.chooseImage({
		    count: multiple ? Math.min(maxCount, 9) : 1,
		    success: (temp) => {
				const res: Response[] = (temp.tempFiles as any).map(item => ({
					...pickExclude(item, ['path']),
					type: "image",
					url: item.path,
					size: item.size
				}));
				resolve(res)
			},
		    fail: reject
		})
	});
}

export function chooseVideo() {
	return new Promise<Resource[]>((resolve, reject) => {
		uni.chooseVideo({
		    count: 1,
		    success: (temp) => {
				const res: Resource[] = [{
					...pickExclude(temp, ['tempFilePath']),
					type: 'video',
					url: temp.tempFilePath,
					size: temp.size,
				}];
				resolve(res);
			},
		    fail: reject
		})
	});
}

export function chooseMedia(multiple: boolean, maxCount: number, accept: "all" | "image" | "video") {
	return new Promise<Resource[]>((resolve, reject) => {
		uni.chooseMedia({
		  count: multiple ? Math.min(maxCount, 9) : 1,
		  mediaType: accept == "all" ? ['image','video'] : [accept],
		  sourceType: ['album', 'camera'],
		  maxDuration: 10,
		  success: (temp) => {
			  // 多选时 视频和图像一起选择时只保留视频
			const res: Resource[] = temp.tempFiles.map((item) => ({
			    ...pickExclude(item, ["tempFilePath", "thumbTempFilePath"]),
			    type: temp.type as any,
			    url: item.tempFilePath,
			  	size: item.size,
				thumb: item.thumbTempFilePath
			}));
		  	resolve(res);
		  },
		  fail: reject
		});
	});
	
}