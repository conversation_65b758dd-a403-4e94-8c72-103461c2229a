<template>
  <view :class="{ 'aspect-ratio-container': true, wrapperClass }"
    :style="{ paddingTop: paddingTop + '%', borderRadius: wrapperRadius }">
    <image class="content-wrapper" :src="prefixFilePath" :style="{ objectFit: fit }" />
  </view>
</template>

<script setup>
import { useStore } from 'vuex'
import { computed } from 'vue'

const props = defineProps({
  src: {
    type: String,
    required: true
  },
  local: {
    type: Boolean,
    default: true
  },
  fit: {
    type: String,
    default: 'cover'
  },
  aspectRatio: {
    type: [String, Number, Array],
    default: 1
  },
  wrapperClass: {
    type: String,
    default: ''
  },
  wrapperRadius: {
    type: String,
    default: ''
  }
})

const store = useStore()
const cloudFileUrl = computed(() => store.state.common.cloudFileUrl)
const prefixFilePath = computed(() => {
  if (!props.src) return ''
  if (props.local) {
    return props.src
  } else {
    const hasHttp = props.src.startsWith('http')
    return hasHttp ? props.src : cloudFileUrl.value + props.src
  }
})



// 计算padding-top百分比
const paddingTop = computed(() => {
  const ratio = parseAspectRatio(props.aspectRatio)
  return (1 / ratio) * 100
})

// 解析比例参数
function parseAspectRatio(input) {
  if (!input) return 1 // 默认1:1

  // 数组格式 [width, height]
  if (Array.isArray(input)) {
    return input[0] / input[1]
  }

  // 字符串格式 "16:9"
  if (typeof input === 'string' && input.includes(':')) {
    const parts = input.split(':')
    return Number(parts[0]) / Number(parts[1])
  }

  // 数字格式 1.77
  return Number(input)
}
</script>

<style scoped>
.aspect-ratio-container {
  position: relative;
  width: 100%;
  height: 0;
  overflow: hidden;
}

.content-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

img {
  width: 100%;
  height: 100%;
}

.preload-placeholder,
.error-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #ddd;
  border-top-color: #888;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>