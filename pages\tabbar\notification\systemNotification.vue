<template>
	<z-paging class="app-container" ref="paging" v-model="dataList" @query="queryList" :safe-area-inset-bottom="true"
		:use-safe-area-placeholder="true">
		<template #top>
			<Navbar :type="1" leftText="系统消息"></Navbar>
			<view class="allPidding" style="margin-top: 32rpx;">
				<NotificationNavbar :state='true' placeholder="消息内容"></NotificationNavbar>
			</view>
		</template>
		<template #empty>
			<uv-empty />
		</template>
		<view class="content allPidding">
			<view class="contentTitleBox" v-if="announcement">
				<text style="padding-left: 24rpx;">{{ announcement.title }}</text>
				<text style="padding: 0 24rpx;">{{ announcement.content }}</text>
				<text>{{ formatTime(announcement.created_at, 1) }}</text>
			</view>
			<uni-list style="margin-top: 24rpx;" :border="false">
				<uni-list-item v-for="(item, index) in messageList" :key="index" :border="false">
					<template v-slot:body>
						<view class="listBox" @click="() => toDetails(formatOrderMsg(item)?.order_id)">
							<image src="../../../static/icon/<EMAIL>" mode=""></image>
							<view class="listBody">
								<text class="name">{{ formatOrderMsg(item)?.title || '' }}</text>
								<text class="lab">服务项：{{ formatOrderMsg(item)?.service_item_name || '' }}</text>
								<text class="lab">服务者：{{ formatOrderMsg(item)?.provider_nickname || '' }}</text>
								<text class="lab">下单时间：{{ formatOrderMsg(item)?.order_at || '' }}</text>
							</view>
							<image src="../../../static/icon/<EMAIL>" mode=""></image>
						</view>
					</template>
				</uni-list-item>
			</uni-list>
		</view>
	</z-paging>
</template>

<script setup>
import {
	ref,
	onMounted
} from 'vue'
import { SYSTEM_MESSAGE_CACHE_KEY } from './hooks/useNotification';
import { timeToString } from '@/common/utils';
import { getSystemMessage } from '@/api/message'

const announcement = ref(null)
const messageList = ref([]);
const paging = ref(null)

onMounted(() => {
	const list = uni.getStorageSync(SYSTEM_MESSAGE_CACHE_KEY) || []
	formatMesssage(list)
})

async function queryList(pageNo) {
	try {
		const p = {
			page: pageNo,
			size: 10
		}
		const res = await getSystemMessage(p)
		if (res.code === 20000) {
			uni.setStorageSync(SYSTEM_MESSAGE_CACHE_KEY, res.data.results)
			paging.value.complete(res.data.results)
			formatMesssage(res.data.results)
		} else {
			paging.value.complete(false)
		}
	} catch (error) {
		paging.value.complete(false)
	}
}

function formatMesssage(list) {
	messageList.value = list.filter(item => item.type == 10)

	announcement.value = list.filter(item => item.type == 1).sort((a, b) => b.created_at - a.created_at)?.[0]
}

function formatTime(timestamp, format) {
	const date = new Date(timestamp);
	if (format === 1) {
		return date.getHours().toString().padStart(2, '0') + ':' + date.getMinutes().toString().padStart(2, '0')
	} else {
		return timeToString(timestamp, 5)
	}
}

function formatOrderMsg(item) {
	try {
		const data = JSON.parse(item.content)
		return data
	} catch (error) {
		console.log('formatOrderMsg', error)
		return null
	}
}

function toDetails(id) {
	if (!id) return
	uni.navigateTo({
		url: `/pages/packageA/orderDetails/orderDetails?id=${id}&type=buy`
	})
}

</script>
<style lang="scss" scoped>
:deep(.uni-list-item__container) {
	padding: 0 !important;
}

.allPidding {
	padding: 0 24rpx;
}

.content {
	margin-top: 40rpx;

	.listBox {
		width: 100%;
		height: 194rpx;
		background: #F7F8FA;
		border-radius: 24rpx;
		margin-bottom: 24rpx;
		position: relative;
		display: flex;

		.listBody {
			margin-top: 24rpx;
			display: flex;
			flex-direction: column;

			.name {
				font-size: 28rpx;
				margin-bottom: 20rpx;
			}

			.lab {
				font-size: 24rpx;
				color: #767676;
			}
		}

		image {
			width: 40rpx;
			height: 40rpx;
			margin-left: 24rpx;
			margin-top: 20rpx;
			margin-right: 16rpx;
		}

		image:nth-child(3) {
			position: absolute;
			top: 16rpx;
			right: 16rpx;
		}
	}

	.contentTitleBox {
		width: 100%;
		min-height: 254rpx;
		border-radius: 24rpx;
		border: 4rpx solid #000000;
		display: flex;
		flex-direction: column;
		margin-bottom: 24rpx;

		text:nth-child(1) {
			font-size: 36rpx;
			margin-top: 20rpx;
		}

		text:nth-child(2) {
			margin-top: 8rpx;
			font-size: 28rpx;
		}

		text:nth-child(3) {
			margin-top: 8rpx;
			margin-right: 24rpx;
			text-align: right;
			font-size: 24rpx;
			color: #85878D;
		}
	}
}
</style>