<template>
	<z-paging ref="paging" v-model="dataList" @query="queryList" :fixed="false">
		<template #empty>
			<!-- <view class="empty-list" v-if="dataList.length === 0">
				<image class="icon" src="/static/<EMAIL>"></image>
				<text>该地区暂无服务</text>
			</view> -->
			<empty-list>
				<text>这里还暂无服务哦 \n 点击发布，来添加第一个服务吧！</text>
			</empty-list>
		</template>
		<!-- <uni-swiper-dot :info="swiperList" :current="slideIndex" field="content">
			
		</uni-swiper-dot> -->
		<swiper class="swiper-container" :style="{ height: swiperHeight }" circular :autoplay="true" :interval="3000"
			@change="swiperChange">
			<swiper-item v-for="item in swiperList" :key="item.id">
				<view class="image-container" @tap="navigateToAnnouncement(item)">
					<image class="img" :src="item.url"></image>
				</view>
			</swiper-item>
			<!-- #ifdef MP-WEIXIN -->
			<!-- <swiper-item key="dimension-plaza-swiper-item-ad">
				<view class="swiper-ad-container">
					<ad-custom unit-id="adunit-6f95bf46a3d577aa"></ad-custom>
				</view>
			</swiper-item> -->
			<!-- #endif -->
		</swiper>

		<view class="quick-entry-container flex">
			<template v-for="item in quickEntry" :key="item.value">
				<view class="quick-entry-item" v-if="item.show" :class="{ disabled: item.disabled }"
					@tap="() => handleTo(item)">
					<image :src="item.icon"></image>
					<text class="">{{ item.name }}</text>
				</view>
			</template>
		</view>

		<view class="notice-bar" v-if="noticeStore.showNotice">
			<carson-notice theme="primary" :list="noticeStore.noticeList" :show-more="false" show-icon
				custom-icon="/static/icon/dust_icon_notice.png" color="#FE58B7" moreColor="#fff"
				background-color="#FFF3FA" border-radius="8px" theKey="title" speed="slow" direction="row"
				@close="closeNotice" />
		</view>

		<uni-list class="dust-uni-list" :border="false">
			<template v-for="(item, index) in dataList" :key="item.id">
				<uni-list-item :border="false" class="dust-uni-list-item">
					<template v-slot:body>
						<view class="article-item-wrapper" @click="() => handleTo('article', item.id)">
							<article-item :article="item" :showIndex="true"></article-item>
						</view>
					</template>
				</uni-list-item>
				<!-- #ifdef MP-WEIXIN -->
				<uni-list-item :border="false" class="dust-uni-list-item" v-if="adPositions.includes(index)">
					<template v-slot:body>
						<view class="ad-item-wrapper">
							<ad-custom unit-id="adunit-683ade455c0dedcb"></ad-custom>
						</view>
					</template>
				</uni-list-item>
				<!-- #endif -->
			</template>
		</uni-list>
	</z-paging>
</template>

<script setup>
	import {
		ref,
		computed,
		onMounted,
		watch
	} from 'vue';
	import {
		useStore
	} from 'vuex';
	import {
		calculateHeight
	} from '@/common/utils';
	import {
		getDimensionServices
	} from '@/api/dimensionService';
	import {
		getAnnouncementList
	} from "@/api/announcement";

	const store = useStore();
	const location = computed(() => store.state.location);
	const noticeStore = computed(() => store.state.notice);
	const commonStore = computed(() => store.state.common);

	const slideIndex = ref(0);
	const swiperList = ref([{
		url: '/static/img/swiper/swiper-pic1.jpg'
	}, {
		url: '/static/img/swiper/swiper-pic2.jpg'
	}]);
	const swiperHeight = ref(calculateHeight(30).height);
	const paging = ref(null);
	const dataList = ref([]);

	// 因其他页面共用服务项数据，故从store中获取。
	const QUICK_ENTRY = Array.from(store.state.service);
	const quickEntry = ref(QUICK_ENTRY);

	// 存储所有广告的全局索引位置
	const adPositions = ref([])

	// 存储每页的广告位置计算状态
	const pageCalculated = ref({})

	uni.$on('location-change-refresh-dimension', () => {
		paging.value && paging.value.reload();
	})

	onMounted(() => {
		store.dispatch('notice/getNoticeList');
	});

	// 计算每页的起始索引
	function getPageStartIndex(pageNum) {
		return (pageNum - 1) * 10
	}

	// 计算每页的数据量
	function getPageSize(pageNum) {
		const start = getPageStartIndex(pageNum)
		const end = start + 10
		return Math.min(end, dataList.value.length) - start
	}

	// 计算广告位置
	function calculateAdPosition(pageNum) {
		// 如果已经为该页计算过位置，不再重新计算
		if (pageCalculated.value[pageNum]) return

		const pageSize = getPageSize(pageNum)

		// 当前页数据少于5条时不显示广告
		if (pageSize < 5) {
			// 标记该页已计算，但无广告
			pageCalculated.value[pageNum] = true
			return
		}

		// 计算广告位置
		let min, max

		// 第一页：5-10之间随机位置
		if (pageNum === 1) {
			min = 4 // 第5条对应索引4
			max = Math.min(9, pageSize - 1) // 第10条对应索引9
		}
		// 其他页：5-8之间随机位置
		else {
			min = 4 // 第5条对应索引4
			max = Math.min(7, pageSize - 1) // 第8条对应索引7
		}

		// 生成随机位置（当前页内的相对位置）
		const relativePosition = Math.floor(Math.random() * (max - min + 1)) + min

		// 转换为全局索引
		const globalPosition = getPageStartIndex(pageNum) + relativePosition

		// 添加到广告位置数组
		adPositions.value.push(globalPosition)

		// 标记该页已计算
		pageCalculated.value[pageNum] = true
	}

	// 计算所有已加载页面的广告位置
	function calculateAllAdPositions() {
		const totalPages = Math.ceil(dataList.value.length / 10)

		for (let page = 1; page <= totalPages; page++) {
			// 如果该页尚未计算，则计算广告位置
			if (!pageCalculated.value[page]) {
				calculateAdPosition(page)
			}
		}
	}

	// 监听数据列表变化，重新计算广告位置
	watch(() => dataList.value, (newDataList, oldDataList) => {
		// 当数据列表长度变化时，重新计算所有广告位置
		if (oldDataList === undefined || newDataList.length !== oldDataList.length) {
			calculateAllAdPositions()
		}
	}, {
		deep: true,
		immediate: true
	})

	watch(() => commonStore.value.adminSetting, (newValue) => {
		if (newValue.isAudit === '0') {
			quickEntry.value = QUICK_ENTRY.map(item => ({
				...item,
				show: true,
			}))
		} else {
			quickEntry.value = QUICK_ENTRY.map(item => ({
				...item,
				show: item.value === 0 || item.value === 1,
			}))
		}
	}, {
		deep: true,
		immediate: true,
	})

	function swiperChange(e) {
		slideIndex.value = e.detail.current;
	}

	getAnnouncementList().then(res => {
		const isSucessed = res.code === 20000;
		if (!isSucessed) return uni.showToast({
			icon: "error",
			title: "获取公告页失败"
		});
		const userList = res.data.results || [];
		if (!userList.length) return;
		console.log("请求的公告数据:", JSON.stringify(userList));
		swiperList.value = userList.map(item => ({
			url: item.cover,
			id: item.id
		}));
	});

	async function queryList(pageNo, pageSize) {
		const params = {
			page: pageNo,
			size: 10,
			is_daily: true
		};

		if (location.value.province?.id) {
			params.province = location.value.province.id;
		}

		if (location.value.city?.id) {
			params.city = location.value.city.id;
		}

		let res = await getDimensionServices(params);
		if (res.code === 20000) {
			paging.value.complete(res.data.results);
		} else {
			paging.value.complete(false);
		}
	}

	function navigateToAnnouncement(item) {
		if (!item.id) return;
		uni.navigateTo({
			url: "/pages/announcement/detail?id=" + item.id
		});
	}

	function handleTo(item, id) {
		let url = '';

		if (item === 'article') {
			url = `/pages/packageA/photographyDetails/photographyDetails?id=${id}`;
		} else {
			switch (item.value) {
				case 0:
					url = `/pages/packageA/photography/photography?type=1&is_daily=true`;
					break;
				case 1:
					url = `/pages/packageA/photography/photography?type=2&is_daily=true`;
					break;
				case 5:
					uni.switchTab({
						url: "/pages/tabbar/publish/publish"
					});
					break;
			}
		}

		url &&
			uni.navigateTo({
				url
			});
	}

	// 关闭通知
	function closeNotice() {
		store.dispatch('notice/closeNotice');
	}
</script>

<style lang="scss" scoped>
	.empty-list {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #85878D;

		.icon {
			width: 380rpx;
			height: 380rpx;
		}
	}

	.swiper-container {
		width: 100%;
		margin-top: 20rpx;
		border-radius: 20rpx;
		overflow: hidden;

		.swiper-ad-container {
			width: 100%;
		}
	}

	.quick-entry-container {
		gap: 44rpx;
		margin-top: 20rpx;
		padding: 24rpx 0;

		.quick-entry-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 16rpx;
			font-size: 24rpx;

			&.disabled {
				filter: grayscale(1);
			}

			image {
				width: 96rpx;
				height: 96rpx;
			}
		}
	}

	.notice-bar {
		margin-top: 20rpx;
		margin-bottom: 14rpx;
	}

	.article-item-wrapper {
		width: 100%;
	}

	.ad-item-wrapper {
		width: 100%;
		border-radius: 40rpx;
		overflow: hidden;
	}
</style>