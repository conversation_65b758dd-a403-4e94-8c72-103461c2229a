<template>
	<z-paging class="app-container" ref="paging" v-model="dataList" @query="queryList" :fixed="false">
		<template #empty>
			<text>暂无订单</text>
		</template>

		<Navbar :type="1" leftText="我的服务"></Navbar>
		<view class="wrapper">
			<view class="service-action-wrapper">
				<view class="service-action-container" @click="() => handleTo('create')">
					<image class="icon" src="../static/service/<EMAIL>"></image>
					<text class="label">服务项</text>
				</view>
				<view class="service-action-container" @click="() => handleTo('comment')">
					<image class="icon" src="../static/service/<EMAIL>"></image>
					<text class="label">服务评价</text>
				</view>
			</view>
			<dust-tabs :list="tabs" v-model="activeTab" tab-type="secondary" :custom-style="{ justifyContent: 'space-between', gap: 0 }"></dust-tabs>

			<view class="list-wrapper">
				<uni-list :border="false">
					<uni-list-item v-for="(item, index) in dataList" :key="item.id" :border="false">
						<template v-slot:body>
							<view @click="() => toDetails(item.id, index)" style="width: 100%">
								<order-item
									class="order-wrapper"
									:order="item"
									:arrid="index"
									@primaryClick="handleOrderPrimaryClick"
									@secondaryClick="handleOrderSecondaryClick"
								/>
							</view>
						</template>
					</uni-list-item>
				</uni-list>
			</view>
		</view>
	</z-paging>
</template>

<script setup>
import { ref, watch } from 'vue';
import { ordersList, cancelOrder, executeOrder } from '@/api/order';
import OrderItem from '@/components/order-item/order-item.vue';

const tabs = ref([
	{ name: '全部', value: 0, apiValue: 0 },
	{ name: '待支付', value: 1, apiValue: 1 },
	{ name: '待完成', value: 2, apiValue: 2 + 4 },
	{ name: '已完成', value: 3, apiValue: 8 },
	// 16: 用户取消 | 32: 商家弃用 | 64: 用户违约 | 128: 商家违约|512：订单超时
	{ name: '取消/退款', value: 4, apiValue: 16 + 32 + 64 + 128 + 512 }
]);
const activeTab = ref(0);
const paging = ref(null);
const dataList = ref([]);

function findActiveTab() {
	const tab = tabs.value.find((item) => item.value === activeTab.value);
	return tab;
}

watch(activeTab, () => {
	paging.value.reload();
});

async function queryList(pageNo, pageSize) {
	const params = {
		page: pageNo,
		size: 10,
		type: 'sell'
	};
	const status = findActiveTab();
	if (status.apiValue) {
		params.status = status.apiValue;
	}

	let res = await ordersList(params);
	if (res.code === 20000) {
		res.data.results.forEach((item) => {
			item.snapshot = JSON.parse(item.snapshot);
		});
		paging.value.complete(res.data.results);
	} else {
		paging.value.complete(false);
	}
}

function handleTo(type, id) {
	let url = '';
	switch (type) {
		case 'create':
			url = '/pages/packageA/service-create/basic';
			break;
		case 'comment':
			url = '/pages/packageA/service-comment/service-comment';
			break;
	}
	url &&
		uni.navigateTo({
			url
		});
}
function toDetails(id, index) {
	uni.navigateTo({
		url: `/pages/packageA/orderDetails/orderDetails?id=${id}&type=sell`,
		events: {
			orderDetailsType(data) {
				dataList.value[index].status = data.status;
			}
		}
	});
}
function handleOrderPrimaryClick(order, index) {
	console.log(order, 'primary');
	if (isTime(order.appointment_at)) {
		uni.showModal({
			title: '提示',
			content: `请确认已完成服务`,
			success: function (res) {
				if (res.confirm) {
					executeOrder(dataList.value[index].id).then((res2) => {
						if (res2.code === 20000) {
							dataList.value[index].status = 8;
						}
					});
				}
			}
		});
	} else {
		uni.showToast({
			title: '当前还未到服务时间',
			icon: 'none',
			duration: 2000
		});
	}
}
//判断时间
// function isTime() {
// 	return new Date().getTime() > info.value.appointment_at;
// }
function isTime(data) {
	return new Date().getTime() > data;
}
function handleOrderSecondaryClick(order, index) {
	console.log(order, 'secondary');
	uni.showModal({
		title: '提示',
		content: `取消订单将扣除违约金${(dataList.value[index].amount / 100 / 100) * dataList.value[index].bond}元`,
		success: function (res) {
			if (res.confirm) {
				cancelOrder(dataList.value[index].id).then((res2) => {
					if (res2.code === 20000) {
						uni.showToast({
							title: '订单已取消',
							icon: 'none',
							duration: 2000
						});
						dataList.value[index].status = 64;
					}
				});
			}
		}
	});
}
</script>

<style scoped lang="scss">
:deep(.uni-list-item__container) {
	padding: 0;
}
.wrapper {
	box-sizing: border-box;
	width: 100%;
	padding: 0 24rpx;
	margin-top: 32rpx;
	color: #1f1f1f;
}
.order-wrapper {
	width: 100%;
}
.service-action-wrapper {
	width: 100%;
	height: 96rpx;
	display: flex;
	gap: 40rpx;
	margin-bottom: 42rpx;

	.service-action-container {
		flex: 1;
		height: 100%;
		border-radius: 24rpx;
		position: relative;

		&:first-child {
			background: #e9fafc;
			font-weight: 700;
			font-size: 32rpx;
			color: #48daea;
			line-height: 32rpx;

			.label {
				position: absolute;
				top: 50%;
				right: 64rpx;
				transform: translateY(-50%);
			}
		}

		&:last-child {
			background: rgba(254, 88, 183, 0.12);
			font-weight: 700;
			font-size: 32rpx;
			color: #fe58b7;
			line-height: 32rpx;

			.label {
				position: absolute;
				top: 50%;
				right: 36rpx;
				transform: translateY(-50%);
			}
		}

		.icon {
			width: 128rpx;
			height: 128rpx;
			position: absolute;
			bottom: 0;
			left: 20rpx;
		}
	}
}

.list-wrapper {
	margin-top: 32rpx;
}
</style>
