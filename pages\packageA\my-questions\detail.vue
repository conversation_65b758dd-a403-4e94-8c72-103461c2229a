<!-- 活动发布主界面 -->
<template>
	<view class="container">
		<Navbar :type="1"  leftText="常见问题"></Navbar>
		<view class="main">
			<view class="reason-container">
				<text class="label">{{title}}</text>
			</view>
			<view class="desc-container">
				<text>{{reason}}</text>
			</view>
		</view>
	</view>
	
</template>
<script setup lang="ts">
	import { ref } from 'vue';
	import { onLoad } from "@dcloudio/uni-app";
	const title = ref("");
	const reason = ref("");

	onLoad((query: {title: string, value: string}) => {
		title.value = query.title;
		reason.value = query.value;
	});
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		height: 100%;
		box-sizing: border-box;
		
		padding-bottom: constant(safe-area-inset-bottom);;
		padding-bottom: env(safe-area-inset-bottom);
	}
	:deep(.uv-upload__wrap .uv-upload__wrap__preview .uv-upload__wrap__preview__image) {
		width: 208rpx !important;
		height: 208rpx !important;
		border-radius: 16rpx !important;
	}
	.main {
		flex: 1 1 auto;
		height: 100%;
		display: flex;
		flex-direction: column;
		gap: 32rpx;
		box-sizing: border-box;
		padding: 40rpx 32rpx 32rpx;
		overflow-y: hidden;
	}
	
	.reason-container {
		font-size: 36rpx;
		height: 36rpx;
		line-height: 36rpx;
	}
	.desc-container {
		padding: 28rpx 24rpx;
		min-height: 352rpx;
		font-size: 32rpx;
		color: #AAAAAA;
		background-color: rgba(247, 248, 250);
		border-radius: 16rpx;
		color: rgba(31, 31, 31);
	}
</style>
