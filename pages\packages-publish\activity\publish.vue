<!-- 活动发布主界面 -->
<template>
	<view class="container">
		<view style="transform: translate(0);">
			<Navbar :type="1" leftText="发活动"></Navbar>
		</view>
		<view class="main">
			<view class="form-wrapper">
				<CoverUpload :url="coverImage" @delete="deleteCoverImage" @uploadSucess="uploadCoverImage" />
				
				<view class="content-warpper">
					<input class="title" v-model="title" placeholder="请输入活动标题" placeholder-class="title-placeholder" />
					<textarea class="desc" type="textarea" v-model="desc" placeholder="请输入活动描述"
						placeholder-class="desc-placeholder" :disabled="disableInput" />
					<view class="upload-container">
						<image-upload :fileList="imageUrls" @delete="deleteImage" @uploadSucess="uploadImage" multiple accept="image"
							maxCount="9" />
					</view>
				</view>

				<view class="select-wrapper">
					<publish-navigate-row label="活动类型" :modelValue="types.name" @click="selectTypesHandle" @clear="clearTypesHandle" />
						
					<publish-navigate-row label="活动人数" :modelValue="membersInfo.displayModel"
						 @click="membersInfo.show = true" @clear="membersInfo.model = null;" />

					<uni-datetime-picker type="daterange" v-model="dateInfo.model" :start="Date.now()"
						@change="(v) => dateInfo.show = !v" @maskClick="dateInfo.show = false">
						<publish-navigate-row :modelValue="dateInfo.displayModel" :label="dateInfo.title"
							@click="dateInfo.show = true" @clear="dateInfo.model = []" />
					</uni-datetime-picker>
					<publish-navigate-row label="活动地址" :show-clear="false"
						:model-value="addressInfo && addressInfo.title" @click="selectAddressHandle" />
					<publish-navigate-row label="活动服务" :modelValue="serviceInfo.displayModel"
						@click="serviceInfo.show = true" @clear="serviceInfo.model = []" />
						
					<publish-input-row label="联系方式（选填）" v-model="phoneNum"/>
				</view>

				<!-- 选择人数下拉 -->
				<publish-pop-select v-if="membersInfo.show" v-model="membersInfo.model" :title="membersInfo.title"
					:localdata="membersInfo.options" @close="membersInfo.show = false"
					@confirm="membersInfo.show = false" @clear="membersInfo.model = 0"
					:multiple="membersInfo.multiple" />

				<!-- 选择服务下拉 -->
				<publish-pop-select v-if="serviceInfo.show" v-model="serviceInfo.model" :title="serviceInfo.title"
					:localdata="serviceInfo.options" @close="serviceInfo.show = false"
					@confirm="serviceInfo.show = false" :multiple="serviceInfo.multiple" />

			</view>

			<view class="footer-contener">
				<view class="btn-container save" @tap.stop="saveHandle">
					<image class="image" src="/pages/packages-publish/static/<EMAIL>" mode="aspectFit"></image>
					<text>存草稿</text>
				</view>

				<view class="btn-container publish" @tap.stop="commitHandle">
					<image class="image" src="/pages/packages-publish/static/<EMAIL>" mode="aspectFit">
					</image>
					<text>发布活动</text>
				</view>
			</view>
			<view class="message">
				<text @tap="publishNoticHandle">点击查看发布规则</text>
			</view>
		</view>
		<publish-notice :cutdown="cutdown" v-if="showPublishNotice" @close="onClosePublishNotice"
			@confirm="onClosePublishNotice" />
	</view>

</template>
<script setup lang="ts">
import { ref, toRaw, onUnmounted, watchEffect, computed } from 'vue';
import { useStore } from 'vuex';
import {
	createPublishAvtivity,
	type PublishActivityRequest
} from "@/api/publish";
import { PUBLISH_ACTIVITY_ADDRESS_SELECT, PUBLISH_ACTIVITY_TYPES_SELECTE } from "./const";
import type { AddressItem } from "./const";
import CoverUpload from './components/coverUpload.vue';

// import publishInputRow from "@/components/publish-input-row/publish-input-row.vue"


import type { ServiceInfo } from "@/store/modules/services.js";
import typesVue from './types.vue';
const store: any = useStore();
// 发布漫展需要实名认证
const userInfo = computed(() => store.state.userInfo);
const storeServiceOption: ServiceInfo[] = store.getters["service/serviceOption"];
const serviceOptions: { text: string, value: number }[] = storeServiceOption.map(item => ({ text: item.name, value: item.value }));

interface LocalSaveInfo {
	cover: string,
	name?: string, // 活动标题
	introduction?: string, // 活动描述
	imageUrls?: { url: string }[], // 图片url列表
	type?: {name: string, id: number};	// 活动类型
	max_people?: number, // 活动人数
	dateRange?: string[], // 活动区域时间
	service?: string[], // 活动服务
	address?: AddressItem;	// 活动地址
	contact?: string; // 联系电话
}

const localSave = getLocalSaveInfo();

interface FileItem {
	url: string; // 网址
	status?: "uploading" | "success" | "failed"; // 上传中 上传成功 上传失败
	message?: string; //提示文本
}

// 封面图
const coverImage = ref(localSave.cover || "");
function deleteCoverImage() {
	coverImage.value = "";
}

function uploadCoverImage(url) {
	coverImage.value = url;
}

// 上传的图片数组
const imageUrls = ref<{ url: string }[]>(localSave.imageUrls || []);

// 图片上传
const uploadImage = (files: any[]) => {
	files.forEach(item => {
		imageUrls.value.push({ url: item.url });
	})
};
// 真机发现，在有弹出框时，点击输入位置仍会获取焦点，需特殊处理
const disableInput = computed(() => {
	return dateInfo.value.show || membersInfo.value.show || serviceInfo.value.show;
});

// 图片删除
const deleteImage = (item: { index: number }) => {
	imageUrls.value.splice(item.index, 1)
}

// 活动名称
const title = ref<string>(localSave.name || "");
const desc = ref<string>(localSave.introduction || "");

// 活动类型
const types = ref<{name: string, id: number}>({
	name: localSave.type ? localSave.type.name : "", 
	id: localSave.type ? localSave.type.id : undefined,
});

function clearTypesHandle() {
	types.value.name = "";
	types.value.id = undefined;
}

const selectTypesHandle = function () {
	uni.$off(PUBLISH_ACTIVITY_TYPES_SELECTE);
	uni.$on(PUBLISH_ACTIVITY_TYPES_SELECTE, (item: {name: string, id: number, select: boolean}) => {
		console.log("接收到的活动类型", item);
		types.value.name = item.select ? item.name : "";
		types.value.id = item.select ? item.id : null;
	});
	uni.navigateTo({
		url: "/pages/packages-publish/activity/types?type=" + (types.value.id || ""),
	});
};

interface SelectInfo<T> {
	show: boolean;  // 是否显示下拉框
	model: T;	  // 选择后显示的文本信息
	options: { text: string, value: string | number }[]; // 下拉选项
	multiple: boolean; // 是否可以多选
	title: string; // 选择框弹出的标题文本
	get displayModel(): string;
}

// 活动人数选择
const membersInfo = ref<SelectInfo<null | number>>({
	show: false,
	model: localSave.max_people == null ? null : localSave.max_people,
	options: [{ text: "300以内", value: 300 }, { text: "1000-3000人", value: 3000 }, { text: "3000-10000人", value: 10000 }, { text: "10000以上", value: 100000 }],
	multiple: false,
	title: "选择人数",
	get displayModel() {
		const model = this.model;
		const item = this.options.find(item => item.value == model);
		if (!item) return "";
		return item.text;
	}
});
// 时间选择
const dateInfo = ref<SelectInfo<string[]>>({
	show: false,
	model: localSave.dateRange || [],
	options: [],
	multiple: false,
	title: "选择时间",
	get displayModel() {
		const model = this.model || [];
		if (!model.length) return "";
		return model.join(" ~ ");
	}
});
// 地址选择
const addressInfo = ref<AddressItem | undefined>(localSave.address);
const selectAddressHandle = function () {
	uni.$off(PUBLISH_ACTIVITY_ADDRESS_SELECT);
	uni.$on(PUBLISH_ACTIVITY_ADDRESS_SELECT, (item: AddressItem) => {
		console.log("接收到的选择地址", item);
		addressInfo.value = item;
	});
	let loactionStr = "";
	const location = addressInfo.value;
	if (location && location.latitude && location.longitude) {
		loactionStr = `longitude=${location.longitude}&latitude=${location.latitude}`;
	}
	uni.navigateTo({
		url: "/pages/packages-publish/activity/chooseAddress?" + loactionStr,
	});
};

// 活动服务选择
const serviceInfo = ref<SelectInfo<string[]>>({
	show: false,
	model: localSave.service || [],
	options: serviceOptions,
	multiple: true,
	title: "选择活动",
	get displayModel() {
		const model = this.model;
		const items: string[] = [];
		this.options.forEach(item => {
			const findIndex = model.findIndex(value => value == item.value);
			if (findIndex < 0) return;
			items.push(item.text);
		});
		if (!items.length) return "";
		return items.join(" ");
	}
});
// 联系方式
const phoneNum = ref(localSave.contact || null);

// 提交检验输入项
const preCommiteCheck = function () {
	interface ChackItem {
		name: string;
		validate: () => string | undefined;
	}
	const checkItems: ChackItem[] = [
		{
			name: "活动图片", validate: () => {
				if (coverImage.value) return;
				return "请上传封面图片"
			}
		},
		{
			name: "活动标题", validate: () => {
				if (title.value) return;
				return "请输入活动标题"
			}
		},
		{
			name: "活动描述", validate: () => {
				if (desc.value) return;
				return "请输入活动描述"
			}
		},
		{
			name: "活动描述图片", validate: () => {
				if (imageUrls.value.length) return;
				return "请上传活动描述图片"
			}
		},
		{
			name: "活动类型", validate: () => {
				if (types.value.name) return;
				return "请选择活动类型"
			}
		},
		{
			name: "活动人数", validate: () => {
				if (membersInfo.value.model !== null) return;
				return "请选择活动人数"
			}
		},
		{
			name: "活动时间", validate: () => {
				if (dateInfo.value.model.length) return;
				return "请选择活动时间"
			}
		},
		{
			name: "活动地址", validate: () => {
				if (addressInfo.value && addressInfo.value.title) return;
				return "请选择活动地址"
			}
		},
		{
			name: "活动服务", validate: () => {
				if (serviceInfo.value.model.length) return;
				return "请选择活动服务"
			}
		},
	];

	for (let item of checkItems) {
		const errMsg = item.validate();
		if (errMsg) {
			uni.showToast({
				title: errMsg,
				icon: "none"
			});
			return false
		}
	}
	return true;
}

const commitHandle = () => {

	const validate = preCommiteCheck();
	if (!validate) return;
	// if (showFirstShowNotice()) return;
	if (userInfo.value.cert_level <= 0) {
		return uni.showModal({
			title: '提示',
			content: '活动需要实名认证后才可以发布',
			confirmText: "确认跳转",
			success: (res) => {
				if (res.cancel) return;
				uni.navigateTo({
					url: '/pages/packageA/verify-idcard/verify-idcard'
				})
			}
		});
	}
	uni.showLoading({
		title: "活动发布中..."
	});

	createPublishAvtivity(createRequestParams()).then(res => {
		const isSucessed = res.code === 20000;
		uni.hideLoading();
		if (!isSucessed) return uni.showToast({
			icon: "error",
			title: "活动发布失败"
		});
		// 保存成功后清空上次的本地缓存
		removeLocalSaveInfo();
		uni.navigateBack();
	}).finally(() => uni.hideLoading());

}

// 保存草稿相关
const LOCAL_SAVE_KEY = "activity-save-key";
const createLocalSaveInfo = function (): LocalSaveInfo {
	return {
		cover: coverImage.value,
		name: title.value,
		introduction: desc.value,
		imageUrls: imageUrls.value,
		type: types.value,
		max_people: membersInfo.value.model,
		dateRange: dateInfo.value.model as any,
		service: serviceInfo.value.model as any,
		address: addressInfo.value,
		contact: phoneNum.value,
	}
}
const createRequestParams = function (): PublishActivityRequest {
	const detialImages: string[] = [];
	imageUrls.value.forEach((item, index) => {
		if (!index) return;
		detialImages.push(item.url);
	});
	const dates = dateInfo.value.model.map(item => new Date(item).getTime());

	return {
		cover: coverImage.value,
		name: title.value,
		introduction: desc.value,
		detail: JSON.stringify(detialImages),
		type: types.value.id,
		max_people: membersInfo.value.model as any,
		contact: phoneNum.value,
		start_time: dates[0],
		end_time: dates[1],
		sponsor: serviceInfo.value.displayModel,
		categories: serviceInfo.value.model,
		editor: "json",
		organizer: "个人主办",
		location: addressInfo.value.address,
		province: addressInfo.value.province || 0,
		city: addressInfo.value.city || 0,
		district: addressInfo.value.district || 0,
		longitude: addressInfo.value.longitude,
		latitude: addressInfo.value.latitude
	}
}

function saveHandle() {
	uni.setStorage({
		key: LOCAL_SAVE_KEY,
		data: createLocalSaveInfo(),
		success: () => uni.showToast({
			title: "保存成功"
		})
	})
}

function getLocalSaveInfo(): LocalSaveInfo {
	const localInfo = uni.getStorageSync(LOCAL_SAVE_KEY) || {};
	return localInfo;
}

function removeLocalSaveInfo() {
	uni.removeStorage({
		key: LOCAL_SAVE_KEY
	})
}

// 2025/05/16新增：发布时需要查看发布规则
const localNoticeKey = "publish-activity-notice-show";
let alreadyShowNotice = uni.getStorageSync(localNoticeKey) || false;
const showPublishNotice = ref(alreadyShowNotice ? false : true);
const cutdown = ref(alreadyShowNotice ? 0 : 5);
function showFirstShowNotice() {
	if (alreadyShowNotice) return false;
	alreadyShowNotice = true;
	showPublishNotice.value = true;
	return true;
}
const publishNoticHandle = () => {
	if (showPublishNotice.value) return;
	showPublishNotice.value = true;
	if (alreadyShowNotice) return
	alreadyShowNotice = true;
	cutdown.value = 0;
	uni.setStorage({
		key: localNoticeKey,
		data: true
	});
}

const onClosePublishNotice = () => {
	showPublishNotice.value = false;
	if (alreadyShowNotice) return
	alreadyShowNotice = true;
	cutdown.value = 0;
	uni.setStorage({
		key: localNoticeKey,
		data: true
	});
}

</script>

<style lang="scss" scoped>
.container {
	display: flex;
	flex-direction: column;
	height: 100%;
	box-sizing: border-box;

	padding-bottom: constant(safe-area-inset-bottom);
	;
	padding-bottom: env(safe-area-inset-bottom);
}

.test {
	width: 100%;
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: flex-start; // 调整项

	.item {
		width: 32%;
		border: 1px solid #ccc;
		box-sizing: border-box;
		margin-right: 14rpx; // 调整项
		margin-top: 14rpx;

		.inner {
			height: 0;
			padding-bottom: 100%;
		}
	}

	.item:first-child,
	.item:nth-child(2),
	.item:nth-child(3) {
		margin-top: 0;
	}

	// 调整项
	.item:nth-child(3n) {
		margin-right: 0;
	}
}

:deep(.uv-upload__wrap .uv-upload__wrap__preview .uv-upload__wrap__preview__image) {
	width: 208rpx !important;
	height: 208rpx !important;
	border-radius: 16rpx !important;
}

:deep(.uni-date-btn--ok .uni-datetime-picker--btn) {
	background-color: rgba(254, 88, 183) !important;
}

:deep(.uni-calendar-item--multiple .uni-calendar-item--before-checked) {
	background-color: rgba(254, 88, 183) !important;
}

:deep(.uni-calendar-item--multiple .uni-calendar-item--after-checked) {
	background-color: rgba(254, 88, 183) !important;
}

:deep(.uni-calendar-item__weeks-box .uni-calendar-item--checked) {
	background-color: rgba(254, 88, 183) !important;
}

.label-title {
	color: black;
	font-weight: bold;
}

.main {
	flex: 1 1 auto;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	box-sizing: border-box;
	padding: 32rpx 32rpx 24rpx;
	overflow: hidden;

	.upload {
		width: 208rpx;
		height: 208px;
		border-radius: 16rpx;
	}

	.form-wrapper {
		flex: 1 1 auto;
		overflow-y: auto;
	}

	.content-warpper {
		margin-top: 20rpx;
		flex: 0 0 auto;
		width: 100%;
		background-color: rgba(247, 248, 250);
		border-radius: 16rpx;
		padding: 24rpx;
		box-sizing: border-box;
		color: rgba(31, 31, 31);

		.title {
			width: 100%;
			flex: 0 0 auto;
			height: 56rpx;
			font-size: 40rpx;
			line-height: 56rpx;
			font-weight: 700;
			background-color: transparent;
		}

		.title-placeholder {
			color: rgba(133, 135, 141);
		}

		.desc {
			margin-top: 15px;
			width: 100%;
			height: 236;
			font-size: 36rpx;
			line-height: 56rpx;
			font-weight: 400;
			background-color: transparent;
		}

		.desc-placeholder {
			color: rgba(133, 135, 141);
		}
		
		.upload-container {
			// padding: 0 0 20rpx;
			box-sizing: border-box;
		}
	}

	.disabled {
		pointer-events: none;
	}

	.select-wrapper {
		display: flex;
		flex-direction: column;
		gap: 24rpx;
		padding: 32rpx 0;
	}

	.form-container {
		flex: 1 1 auto;
		overflow-y: auto;
	}

	.footer-contener {
		display: flex;
		height: 118rpx;
		flex: 0 0 auto;
		gap: 32rpx;
		align-items: center;
		padding: 0rpx 8rpx;
		box-sizing: border-box;

		.btn-container {
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 16rpx;

			height: 78rpx;
			padding: 0 44rpx;
			flex: 1 1 auto;
			background-color: rgba(238, 238, 238);
			border-radius: 40rpx;

			font-size: 32rpx;
			font-weight: 700;

			.image {
				width: 40rpx;
				height: 40rpx;
			}
		}

		.save {
			flex: 0 0 auto;
		}

		.publish {
			flex: 1 1 auto;
		}

	}

	.message {
		font-size: 14px;
		color: rgba(255, 206, 89, 1);
		flex: none;
		text-align: center;
	}
}
</style>
