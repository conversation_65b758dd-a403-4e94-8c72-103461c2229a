<template>
	<view class="app-container">
		<z-paging ref="paging" :auto="false" :fixed="true" :refresher-enabled="false" :loading-more-enabled="false"
			:hide-empty-view="true">
			<template #top>
				<Navbar :type="1" leftText="提交订单"></Navbar>
				<!-- <view class="allPidding" style="margin-top: 32rpx">
					<NotificationNavbar :state="true" placeholder="消息内容"></NotificationNavbar>
				</view> -->
			</template>
			<view class="list allPidding">
				<text class="title txtellipsis">{{ name }}</text>
				<text>商家保证金</text>
				<view>
					<uni-data-select v-model="bond" :localdata="range"></uni-data-select>
					<image src="../../../static/icon/<EMAIL>" mode=""></image>
				</view>
				<text>价格</text>
				<view>
					<input type="number" style="padding-left: 24rpx; color: #3d3d3d; width: 100%" v-model="data.amount"
						@input="inputnumber" placeholder="请输入整数金额" />
				</view>
				<text>约拍时间</text>
				<view>
					<picker mode="date" :value="date" :start="startDate" :end="endDate" @change="bindDateChange">
						<view class="uni-input">
							<text>{{ date }}</text>
						</view>
					</picker>
					<image src="../../../static/icon/<EMAIL>" mode=""></image>
				</view>
				<view class="button" @click="creat">
					<view>生成订单</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup>
	import {
		ref,
		reactive
	} from 'vue';
	import dayjs from 'dayjs'

	import {
		onLoad,
		onShow
	} from '@dcloudio/uni-app';
	import {
		createOrder
	} from '../../../api/order';
	import {
		getMineBalance
	} from '../../../api/wallet';
	// const value = ref(10);
	let consultid = ref(null);
	let name = ref(null);
	let bond = ref(10);
	let data = ref({
		service_item_id: null, //服务项 ID
		user_id: null,
		amount: 268
	});
	let balance = ref(0);
	onLoad((e) => {
		name.value = e.name;
		consultid.value = e.consult_id;
		let o = {
			service_item_id: Number(e.service_item_id),
			user_id: Number(e.user_id),
			amount: Number(e.amount) / 100
			// amount: 0.01
		};
		data.value = o;
		bond.value = Number(e.bond);
	});
	onShow((e) => {
		MineBalance();
	});

	function MineBalance() {
		getMineBalance().then((res) => {
			balance.value = res.data.balance - res.data.freeze;
		});
	}
	const range = ref([{
			value: 0,
			text: '0%'
		},
		{
			value: 5,
			text: '5%'
		},
		{
			value: 10,
			text: '10%'
		},
		{
			value: 15,
			text: '15%'
		},
		{
			value: 20,
			text: '20%'
		}
	]);
	const date = ref(getDate());

	function getDate(type) {
		let date = dayjs();
		if (type === 'start') {
			date = date.subtract(60, 'year');
		} else if (type === 'end') {
			date = date.add(2, 'year');
		}
		return date.format('YYYY.MM.DD');
	}

	function bindDateChange(e) {
		const formattedDate = dayjs(e.detail.value).format('YYYY.MM.DD');
		date.value = formattedDate;
	}

	function creat() {
		let dataobj = JSON.parse(JSON.stringify(data.value));
		dataobj.bond = bond.value;
		dataobj.appointment_at = dayjs(date.value.replace(/\./g, '/')).valueOf();

		uni.showModal({
			title: '提示',
			content: `本次订单需要缴纳 ${(dataobj.amount * dataobj.bond) / 100} 元保证金`,
			success: function(r) {
				if (r.confirm) {
					if (balance.value / 100 >= (dataobj.amount * dataobj.bond) / 100) {
						dataobj.amount = dataobj.amount * 100;
						// return;
						createOrder(consultid.value, dataobj).then((res) => {
							if (res.code === 20000) {
								uni.showToast({
									title: '已生成订单',
									duration: 2000
								});
								uni.navigateBack();
							}
						});
					} else {
						uni.showModal({
							title: '提示',
							content: `保证金不足，请先充值`,
							success: function(res2) {
								if (res2.confirm) {
									uni.navigateTo({
										url: '/pages/packageA/wallet/wallet'
									});
								}
							}
						});
					}
				}
			}
		});
	}

	function inputnumber(e) {
		setTimeout(() => {
			data.value.amount = Number(e.detail.value.replace(/[^\d]/g, ''));
		}, 0);
	}
</script>
<style lang="scss" scoped>
	:deep(.uni-select__input-text) {
		font-size: 32rpx !important;
		color: #3d3d3d !important;
	}

	:deep(.uni-select) {
		border: none !important;
		width: 702rpx !important;
	}

	:deep(.uni-icons) {
		display: none !important;
	}

	.allPidding {
		padding: 0 24rpx;
	}

	.textHide {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.title {
		font-weight: 700;
		font-size: 32rpx;
		color: #3d3d3d;
		margin-top: 48rpx;
		margin-bottom: 44rpx;
	}

	.list {
		// margin-top: 44rpx;
		width: 100%;
		display: flex;
		flex-direction: column;

		text {
			font-size: 32rpx;
			color: #3d3d3d;
		}

		view {
			width: 702rpx;
			height: 72rpx;
			background: #f7f8fa;
			border-radius: 16rpx;
			margin-top: 12rpx;
			margin-bottom: 24rpx;
			display: flex;
			align-items: center;
			position: relative;

			text {
				padding-left: 24rpx;
			}

			image {
				width: 40rpx;
				height: 40rpx;
				position: absolute;
				right: 24rpx;
				pointer-events: none;
			}
		}
	}

	.button {
		padding-top: 56rpx;
		background: #fff !important;

		view {
			width: 702rpx;
			height: 96rpx;
			background: #fe58b7;
			border-radius: 48rpx;
			color: #fff;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
</style>