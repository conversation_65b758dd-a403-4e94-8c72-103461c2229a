import * as auth from '@/common/auth'
import Config from '@/common/config'
import {
	getAdminSettingApi
} from '@/api/common'

const TABBAR_LIST = [{
		index: 0,
		name: '首页',
		img: '/static/tabbar/icon_sy.png',
		acImg: '/static/tabbar/icon_sy2.png'
	},
	{
		index: 1,
		name: '次元',
		img: '/static/tabbar/icon_cy.png',
		acImg: '/static/tabbar/icon_cy2.png'
	},
	{
		index: 2,
		name: '发布',
		img: '/static/tabbar/icon_fb.png',
		acImg: '/static/tabbar/icon_fb.png'
	},
	{
		index: 3,
		name: '消息',
		img: '/static/tabbar/icon_xx.png',
		acImg: '/static/tabbar/icon_xx2.png'
	},
	{
		index: 4,
		name: '我的',
		img: '/static/tabbar/icon_wd.png',
		acImg: '/static/tabbar/icon_wd2.png'
	},
]

const COMMON_STATE = {
	curPage: '', // 当前页面
	sysInfo: {}, // 系统信息
	// 服务器静态文件地址
	cloudFileUrl: Config.cloudFileUrl,
	amapKey: Config.amapKey,
	tencentKey: Config.tencentKey,
	adminSetting: {
		isAudit: "0"
	},
	TabBarList: TABBAR_LIST,
	inviteCode: '',
}

// 公共状态模块
const common = {
	namespaced: true, // 添加命名空间
	state: {
		...COMMON_STATE
	},
	getters: {},
	mutations: {
		updateState(state, payload) { // 更新
			for (let key in payload) {
				state[key] = payload[key]
			}
		}
	},
	actions: {
		setCommonInfo({
			state,
			commit
		}, payload) {
			commit('updateState', payload)
		},
		async getAdminSetting({
			commit
		}) {
			const res = await getAdminSettingApi()
			let tabbar = []
			console.log('getAdminSettingApi:', res)
			commit('updateState', {
				adminSetting: res?.data ? res.data : { isAudit: '1' },
			})
		},
		// 存储邀请码
		setInviteCode({ state, commit }, payload) {
			console.log('setInviteCode:', payload)
			commit('updateState', { inviteCode: payload })
		},
	}
}

export default common