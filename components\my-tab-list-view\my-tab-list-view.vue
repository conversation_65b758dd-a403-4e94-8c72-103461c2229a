<template>
	<view class="content">
		<!-- 这里设置了z-paging加载时禁止自动调用reload方法，自行控制何时reload（懒加载）-->
		<z-paging ref="paging" v-model="dataList" @query="queryList" use-page-scroll :scrollable="false"
			:refresher-enabled="false" :hide-empty-view="hideEmptyView" @contentHeightChanged="contentHeightChanged"
			:auto="false" :auto-clean-list-when-reload="false">
			<!-- 如果希望其他view跟着页面滚动，可以放在z-paging标签内 -->
			<view class="tabs-body">
				<view class="item" v-for="(item, index) in dataList" :key="index" @click="clickHandle(item)">
					<view class="tag" v-if="item.categories" :class="item.categories.type">{{ item.categories.name }}
					</view>
					<image class="item-img" :src="prefixFilePath(item.cover)" />
					<view class="sc">
						<view class="sc-bar">
							<image class="icon-sc" src="/static/my/<EMAIL>" />
						</view>
						<text>{{ item.count }}</text>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<script setup name="my-tab-list">
import { ref, computed, watch, nextTick } from 'vue'
import { mapState, mapActions, useStore } from 'vuex'
import { getDimenstar, getAlbum, getFavorite, getCollection } from '@/api/user'

const props = defineProps({
	// 当前组件的index，也就是当前组件是swiper中的第几个
	tabIndex: {
		type: Number,
		default: function () {
			return 0
		}
	},
	// 当前swiper切换到第几个index
	currentIndex: {
		type: Number,
		default: function () {
			return 0
		}
	},
	// 选中分类中的 子分类 如收藏分类中的 全部|服务|漫展
	selectedInfo: {
		type: Object,
		// tag: user、expo、dimension、service、subject
		// type: 1 
		default: () => ({ tag: "", type: "" })
	}
})
const emit = defineEmits(['heightChanged'])
const store = useStore()
let paging = ref(null)
let dataList = ref([])
let height = ref(0)
let hideEmptyView = ref(true)
let completeFunc = ref(null)
const generateData = () => {
	const data = [];
	for (let i = 1; i <= 55; i++) {
		data.push({
			id: i,
			name: `第${i}届IJOY漫展xCGF游戏节`,
			cover: 'https://p1.itc.cn/q_70/images03/20230427/97e4cf398c1c453f98f8135b202479d6.jpeg',
			thumbs_up_count: i + 1,
		});
	}
	return data;
}
let allData = ref(generateData());
let tabList = ref(Object.freeze([{
	name: '次元',
	tags: []
},
{
	name: '相册',
	tags: []
},
{
	name: '赞过',
	tags: []
},
{
	name: '收藏',
	tags: [
		{
			lable: "服务", value: "3-0", externalParams: { type: "service" },
			types: [
				{ lable: "全部", value: "3-0-0", externalParams: { categories: "1,2" } },
				{ lable: "约拍", value: "3-0-1", externalParams: { categories: "1" } },
				{ lable: "约妆", value: "3-0-2", externalParams: { categories: "2" } }
			],
		},
		{
			lable: "活动", value: "3-1", externalParams: { type: "expo" },
			types: [{ lable: "漫展", value: "3-1-0", externalParams: {} }]
		},
	]
}
]))

const userInfo = computed(() => store.state.userInfo)
const commonStore = computed(() => store.state.common)
const externalParams = computed(() => {
	const selectedInfo = props.selectedInfo;
	const tags = tabList.value[props.currentIndex].tags || [];

	const selectedTag = tags.find(tag => tag.value === selectedInfo.tag);
	if (!selectedTag) return {};
	const selectedType = selectedTag.types.find(type => type.value === selectedInfo.type) || {};
	return { ...selectedTag.externalParams, ...selectedType.externalParams };
})

watch(() => props.currentIndex, (newVal) => {
	console.log('newVal:', newVal, props.tabIndex)
	if (newVal === props.tabIndex) {
		// 懒加载，当滑动到当前的item时，才去加载
		nextTick(() => {
			setTimeout(() => {
				paging.value?.reload();
			}, 100);
		})
	}
}, { immediate: true })
watch(() => userInfo.value, (newVal) => {
	if (newVal.token) {
		nextTick(() => {
			setTimeout(() => {
				paging.value?.reload();
			}, 100);
		})
	}
}, { immediate: true, deep: true })

const queryList = async (pageNum, pageSize) => {
	// 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
	// 这里的pageNum和pageSize会自动计算好，直接传给服务器即可
	// 模拟请求服务器获取分页数据，请替换成自己的网络请求
	const params = {
		page: pageNum,
		size: 12,
	}
	let res = {
		code: 20000,
		data: {
			results: []
		}
	}

	// res = await this.fetchData(params)
	if (userInfo.value.token) {
		if (props.tabIndex === 0) {
			res = await getDimenstar(params);
			const result = (res.data.results || []).map(item => ({ ...item, count: item.like_count }));
			res.data.results = result;
		} else if (props.tabIndex === 1) {
			res = await getAlbum(params)
		} else if (props.tabIndex === 2) {
			res = await getFavorite(params)
		} else if (props.tabIndex === 3) {
			// 收藏
			const completeParams = { ...params, ...externalParams.value };
			res = await getCollection(completeParams);
			// 新增表示type用来表示 是service还是expo
			const results = (res.data.results || []).map(item => {
				if (completeParams.type === "expo") {
					return { ...item, count: item.collection_count, type: completeParams.type };
				} else if (completeParams.type === "service") {
					const categories = item.categories ? item.categories[0] : {};
					if (categories.id == "1") {
						categories.type = "photograph";
					} else if (categories.id == "2") {
						categories.type = "decoration";
					}
					return { ...item, categories, count: item.favorite_count, type: completeParams.type }
				}
				return item;
			});
			res.data.results = results;
		}
	}
	console.log('res:', props.tabIndex, res)
	if (res.code === 20000) {
		paging.value?.complete(res.data.results);
	} else {
		paging.value?.complete([]);
	}
	hideEmptyView.value = false;

	//请求结束，调用父组件的下拉刷新结束回调函数，使得父组件中的z-paging下拉刷新结束
	if (completeFunc.value) {
		completeFunc.value();
	}
}
// 页面通知当前子组件刷新列表
const reload = (completeFuncs) => {
	// 先把父组件下拉刷新的回调函数存起来
	completeFunc.value = completeFuncs;
	// 调用z-paging的reload方法
	paging.value.reload();
}
// 当列表高度改变时，通知页面的swiper同步更改高度
const contentHeightChanged = (height) => {
	const finalHeight = dataList.value?.length ? height : 0;
	// 限制内容最小高度为屏幕可见高度减z-tabs高度。注意，页面中有使用slot="top"插入的view，则此处的minHeight还应该减去slot="top"插入的view的高度
	const minHeight = uni.getSystemInfoSync().windowHeight - uni.upx2px(700);
	emit('heightChanged', Math.max(finalHeight, minHeight));
}
// 页面通知当前子组件加载更多数据
const doLoadMore = () => {
	paging.value?.doLoadMore();
}
// 页面通知当前子组件清除数据
const clear = () => {
	paging.value?.clear();
	hideEmptyView.value = true;
}
const clearData = () => {
	paging.value?.clear();
	hideEmptyView.value = false;
};
const prefixFilePath = (url) => {
	if (!url) return ''
	if (url.includes("http")) return url;
	return commonStore.value.cloudFileUrl + url
}
const fetchData = ({
	page,
	size
}) => {
	return new Promise(function (resolve) {
		setTimeout(() => {
			const start = (page - 1) * size;
			const end = page * size;
			const pageData = allData.value?.slice(start, end);
			resolve({
				code: 20000,
				data: {
					results: pageData,
				},
			});
		}, 500); // 模拟网络延迟
	});
}
const clickHandle = (item) => {
	console.log("我的-item点击", props.tabIndex, item);
	if (props.tabIndex !== 3) return;
	const url = item.type === "service" ? `/pages/packageA/photographyDetails/photographyDetails?id=${item.id}`
		: `/pages/packageA/exhibition-details/exhibition-details?id=${item.id}`;
	uni.navigateTo({
		url: url
	});

}
// 标签点击
const radioClick = (value) => {
	// this.radios.map((item) => {
	// 	item.checked = item.value === value ? true : false
	// });
	// this.$refs.paging.reload();
}
defineExpose({
	reload,
	doLoadMore,
	clear,
	clearData
})
</script>

<style lang="scss" scoped>
/* 注意，1、父节点需要固定高度，z-paging的height:100%才会生效 */
/* 注意，2、请确保z-paging与同级的其他view的总高度不得超过屏幕宽度，以避免超出屏幕高度时页面的滚动与z-paging内部的滚动冲突 */
.content {
	height: 100%;
}

.tabs-body {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	// grid-template-rows: repeat(3, 1fr);
	padding: 0 32rpx 32rpx;
	gap: 32rpx;

	.item {
		border-radius: 24rpx;
		position: relative;

		.tag {
			display: flex;
			position: absolute;
			left: 0;
			top: 0;
			justify-content: center;
			align-items: center;
			height: 40rpx;
			padding: 0 14rpx;
			font-size: 28rpx;
			font-weight: 600;
			border-radius: 24rpx 0rpx 24rpx 0rpx;
			opacity: 0;
		}

		.tag.photograph {
			opacity: 1;
			background-color: rgba(72, 218, 234, 1);
		}

		.tag.decoration {
			opacity: 1;
			background-color: rgba(254, 88, 183, 1);
		}
	}

	.item-img {
		border-radius: 24rpx;
		width: 208rpx;
		height: 160rpx;
	}

	.sc {
		background: rgba(0, 0, 0, 0.5);
		border-radius: 20rpx 0rpx 24rpx 0rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 88rpx;
		height: 32rpx;
		position: absolute;
		right: 0;
		bottom: 0;
		font-size: 20rpx;
		color: #FFFFFF;
	}

	.sc-bar {
		position: relative;
		width: 24rpx;
		height: 24rpx;
		margin-right: 8rpx;
	}

	.icon-sc {
		width: 44rpx;
		height: 44rpx;
		position: absolute;
		left: -6rpx;
		top: 0;
	}
}
</style>