<template>
	<view class="containers" @tap="clickHandle">
		<view class="label">
			<text>{{ props.label }}</text>
		</view>
		<view class="nav">
			<text v-if="model" class="value">{{ model }}</text>
			<text v-else class="placeholder">{{ props.placehold }}</text>

			<uni-icons v-if="showClear && model" type="clear" style="padding: 5rpx; margin-right: -5rpx;"
				color="#c0c4cc" @tap.stop="clearHandle" />
			<uni-icons v-else type="forward" style="padding: 5rpx; margin-right: -5rpx;" color="#c0c4cc" />
		</view>
	</view>
</template>

<script setup lang="ts">
import { PropType, computed } from 'vue';

const model = defineModel<string | number>();
const props = defineProps({
	label: {
		type: String,
		require: true
	},

	placehold: {
		type: String,
		default: "请选择"
	},

	showClear: {
		type: <PERSON>olean,
		default: true
	}
});

interface Emits {
	(e: "click"): void;
	(e: "clear"): void;
}
const emits = defineEmits<Emits>();
const clickHandle = () => {
	emits("click");
}

const clearHandle = () => {
	emits("clear")
}

</script>

<style scoped lang="scss">
.containers {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 80rpx;
	width: 100%;
	border-radius: 16rpx;
	padding: 0 24rpx;
	box-sizing: border-box;
	background-color: rgba(247, 248, 250);

	.label {
		flex: 0 0 auto;
		font-size: 32rpx;
		color: rgba(61, 61, 61);
	}

	.value {
		font-size: 32rpx;
		color: rgba(61, 61, 61);
	}

	.placeholder {
		color: rgba(133, 135, 141);
		font-size: 32rpx;
	}

	.nav {
		display: flex;
		flex: 1 1 auto;
		justify-content: flex-end;
		align-items: center;
		gap: 12rpx;

		.image {
			width: 40rpx;
			height: 40rpx;
		}
	}
}
</style>