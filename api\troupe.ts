import { requestApi } from './request';

interface GetHomeTroupeListParams {
	page: number;
	size: number;
	skip: number;
	limit: number;
	type: 'all' | 'owner' | 'joinin'; // all：全部、owner：我创建的、joinin：我参与的
	status: number; // 状态 1:招募中 2已满员 3:已停止
	property: number[]; // 属性 1:多角色|多服装 2:多角色|单服装 3:单角色|多服装 4:单角色|单服装
	start_at: number; // 执行时间 +: 当前时间之后的 -: 当前时间之前的
	order_by: string; // 英文逗号分割，-表示倒叙
}

export interface GetHomeTroupeListResult {
	id: number;
	
}

/** 获取剧团列表 */
export const getHomeTroupeListApi = (params: GetHomeTroupeListParams) => {
	return requestApi<{
		results: GetHomeTroupeListResult[];
		count: number;
	}>({
		url: "/api/v1/foreground/home/<USER>",
		data: params,
		method: "GET",
		loading: false,
		isToken: false,
	});
}

/** 获取指定剧 */
export const getHomeTroupeDetailApi = (troupe_id: number) => {
	return requestApi({
		url: `/api/v1/foreground/home/<USER>/${troupe_id}`,
		method: 'GET',
		isToken: false,
	})
}