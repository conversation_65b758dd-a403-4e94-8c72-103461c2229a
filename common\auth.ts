// 获取本地存储的 token
export function getToken() : string {
	return uni.getStorageSync('storage_token');
}

// 设置 token
export function setToken(value : string) : void {
	uni.setStorageSync('storage_token', value);
}

// 设置 邀请码
export function updateInviteCode(value : string) : void {
	uni.setStorageSync('storage_inviteCode', value);
}

// 获取 邀请码
export function getInviteCode() : string {
	return uni.getStorageSync('storage_inviteCode');
}