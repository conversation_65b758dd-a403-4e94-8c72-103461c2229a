import {
	ref,
	computed
} from 'vue'
import {
	onLoad
} from '@dcloudio/uni-app'

export const useTitle = () => {
	// 漫展 / 日常
	const typeTitle = ref(null)
	// 约拍 / 约妆
	const categoryId = ref(null)
	const categoryTitle = ref(null)
	const isDaliy = ref(false)

	onLoad((option) => {
		const categoryIdMap = {
			'约妆': 2,
			'约拍': 1
		}
		categoryId.value = categoryIdMap[option.categoryTitle]
		categoryTitle.value = option.categoryTitle
		typeTitle.value = option.typeTitle

		isDaliy.value = option.typeTitle === '日常'
	})

	const comboTitleMap = computed(() => {
		if (isDaliy.value) {
			return {
				title: '服务套餐',
				emptyCombo: '暂无套餐',
				addCombo: '新增套餐'
			}
		} else {
			return {
				title: '漫展服务',
				emptyCombo: '暂无服务',
				addCombo: '新增服务'
			}
		}
	})

	return {
		isDaliy,
		typeTitle,
		categoryId,
		categoryTitle,
		comboTitleMap
	}
}