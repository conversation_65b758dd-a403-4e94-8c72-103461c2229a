export const stringToNodes = (str, selectedList) => {
	const lines = str.split('\n')
	const nodes = lines.map(line => {
		if (line.trim() === '') {
			return {
				name: 'br',
				children: [{ type: '', text: '' }]
			}
		}

		const regex = /(@\S+)/g;
		const splitList = line.split(regex).filter(part => part !== '')

		const lineNodes = splitList.map(item => {
			if (selectedList.includes(item.replace('@', ''))) {
				return {
					name: 'span',
					attrs: {
						style: 'color: #5E94CE;'
					},
					children: [
						{ type: 'text', text: `${item} ` }
					]
				}
			} else {
				return {
					name: 'span',
					children: [
						{ type: 'text', text: `${item}` }
					]
				}
			}
		})
		return {
			name: 'p',
			children: lineNodes
		}
	})
	return nodes
}